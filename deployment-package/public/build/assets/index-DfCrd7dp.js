var Zx=Object.defineProperty,Jx=Object.defineProperties;var e1=Object.getOwnPropertyDescriptors;var yi=Object.getOwnPropertySymbols;var Hf=Object.prototype.hasOwnProperty,Vf=Object.prototype.propertyIsEnumerable;var Qf=e=>{throw TypeError(e)},Kf=Math.pow,Wf=(e,t,n)=>t in e?Zx(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,N=(e,t)=>{for(var n in t||(t={}))Hf.call(t,n)&&Wf(e,n,t[n]);if(yi)for(var n of yi(t))Vf.call(t,n)&&Wf(e,n,t[n]);return e},E=(e,t)=>Jx(e,e1(t));var D=(e,t)=>{var n={};for(var r in e)Hf.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&yi)for(var r of yi(e))t.indexOf(r)<0&&Vf.call(e,r)&&(n[r]=e[r]);return n};var t1=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var $l=(e,t,n)=>t.has(e)||Qf("Cannot "+n);var j=(e,t,n)=>($l(e,t,"read from private field"),n?n.call(e):t.get(e)),G=(e,t,n)=>t.has(e)?Qf("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),H=(e,t,n,r)=>($l(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),re=(e,t,n)=>($l(e,t,"access private method"),n);var xi=(e,t,n,r)=>({set _(o){H(e,t,o,n)},get _(){return j(e,t,r)}});var se=(e,t,n)=>new Promise((r,o)=>{var s=c=>{try{l(n.next(c))}catch(u){o(u)}},i=c=>{try{l(n.throw(c))}catch(u){o(u)}},l=c=>c.done?r(c.value):Promise.resolve(c.value).then(s,i);l((n=n.apply(e,t)).next())});var i4=t1(Ve=>{function n1(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function Ka(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var pm={exports:{}},qa={},hm={exports:{}},ne={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oi=Symbol.for("react.element"),r1=Symbol.for("react.portal"),o1=Symbol.for("react.fragment"),s1=Symbol.for("react.strict_mode"),i1=Symbol.for("react.profiler"),a1=Symbol.for("react.provider"),l1=Symbol.for("react.context"),c1=Symbol.for("react.forward_ref"),u1=Symbol.for("react.suspense"),d1=Symbol.for("react.memo"),f1=Symbol.for("react.lazy"),qf=Symbol.iterator;function p1(e){return e===null||typeof e!="object"?null:(e=qf&&e[qf]||e["@@iterator"],typeof e=="function"?e:null)}var mm={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},gm=Object.assign,vm={};function Yo(e,t,n){this.props=e,this.context=t,this.refs=vm,this.updater=n||mm}Yo.prototype.isReactComponent={};Yo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Yo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function ym(){}ym.prototype=Yo.prototype;function ld(e,t,n){this.props=e,this.context=t,this.refs=vm,this.updater=n||mm}var cd=ld.prototype=new ym;cd.constructor=ld;gm(cd,Yo.prototype);cd.isPureReactComponent=!0;var Gf=Array.isArray,xm=Object.prototype.hasOwnProperty,ud={current:null},wm={key:!0,ref:!0,__self:!0,__source:!0};function bm(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)xm.call(t,r)&&!wm.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];o.children=c}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:oi,type:e,key:s,ref:i,props:o,_owner:ud.current}}function h1(e,t){return{$$typeof:oi,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function dd(e){return typeof e=="object"&&e!==null&&e.$$typeof===oi}function m1(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Yf=/\/+/g;function zl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?m1(""+e.key):t.toString(36)}function qi(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case oi:case r1:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+zl(i,0):r,Gf(o)?(n="",e!=null&&(n=e.replace(Yf,"$&/")+"/"),qi(o,t,n,"",function(u){return u})):o!=null&&(dd(o)&&(o=h1(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Yf,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",Gf(e))for(var l=0;l<e.length;l++){s=e[l];var c=r+zl(s,l);i+=qi(s,t,n,c,o)}else if(c=p1(e),typeof c=="function")for(e=c.call(e),l=0;!(s=e.next()).done;)s=s.value,c=r+zl(s,l++),i+=qi(s,t,n,c,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function wi(e,t,n){if(e==null)return e;var r=[],o=0;return qi(e,r,"","",function(s){return t.call(n,s,o++)}),r}function g1(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var at={current:null},Gi={transition:null},v1={ReactCurrentDispatcher:at,ReactCurrentBatchConfig:Gi,ReactCurrentOwner:ud};function Sm(){throw Error("act(...) is not supported in production builds of React.")}ne.Children={map:wi,forEach:function(e,t,n){wi(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return wi(e,function(){t++}),t},toArray:function(e){return wi(e,function(t){return t})||[]},only:function(e){if(!dd(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};ne.Component=Yo;ne.Fragment=o1;ne.Profiler=i1;ne.PureComponent=ld;ne.StrictMode=s1;ne.Suspense=u1;ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=v1;ne.act=Sm;ne.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=gm({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=ud.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)xm.call(t,c)&&!wm.hasOwnProperty(c)&&(r[c]=t[c]===void 0&&l!==void 0?l[c]:t[c])}var c=arguments.length-2;if(c===1)r.children=n;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];r.children=l}return{$$typeof:oi,type:e.type,key:o,ref:s,props:r,_owner:i}};ne.createContext=function(e){return e={$$typeof:l1,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:a1,_context:e},e.Consumer=e};ne.createElement=bm;ne.createFactory=function(e){var t=bm.bind(null,e);return t.type=e,t};ne.createRef=function(){return{current:null}};ne.forwardRef=function(e){return{$$typeof:c1,render:e}};ne.isValidElement=dd;ne.lazy=function(e){return{$$typeof:f1,_payload:{_status:-1,_result:e},_init:g1}};ne.memo=function(e,t){return{$$typeof:d1,type:e,compare:t===void 0?null:t}};ne.startTransition=function(e){var t=Gi.transition;Gi.transition={};try{e()}finally{Gi.transition=t}};ne.unstable_act=Sm;ne.useCallback=function(e,t){return at.current.useCallback(e,t)};ne.useContext=function(e){return at.current.useContext(e)};ne.useDebugValue=function(){};ne.useDeferredValue=function(e){return at.current.useDeferredValue(e)};ne.useEffect=function(e,t){return at.current.useEffect(e,t)};ne.useId=function(){return at.current.useId()};ne.useImperativeHandle=function(e,t,n){return at.current.useImperativeHandle(e,t,n)};ne.useInsertionEffect=function(e,t){return at.current.useInsertionEffect(e,t)};ne.useLayoutEffect=function(e,t){return at.current.useLayoutEffect(e,t)};ne.useMemo=function(e,t){return at.current.useMemo(e,t)};ne.useReducer=function(e,t,n){return at.current.useReducer(e,t,n)};ne.useRef=function(e){return at.current.useRef(e)};ne.useState=function(e){return at.current.useState(e)};ne.useSyncExternalStore=function(e,t,n){return at.current.useSyncExternalStore(e,t,n)};ne.useTransition=function(){return at.current.useTransition()};ne.version="18.3.1";hm.exports=ne;var h=hm.exports;const F=Ka(h),Nm=n1({__proto__:null,default:F},[h]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var y1=h,x1=Symbol.for("react.element"),w1=Symbol.for("react.fragment"),b1=Object.prototype.hasOwnProperty,S1=y1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,N1={key:!0,ref:!0,__self:!0,__source:!0};function Cm(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)b1.call(t,r)&&!N1.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:x1,type:e,key:s,ref:i,props:o,_owner:S1.current}}qa.Fragment=w1;qa.jsx=Cm;qa.jsxs=Cm;pm.exports=qa;var a=pm.exports,jm={exports:{}},kt={},Em={exports:{}},km={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(P,I){var U=P.length;P.push(I);e:for(;0<U;){var $=U-1>>>1,Q=P[$];if(0<o(Q,I))P[$]=I,P[U]=Q,U=$;else break e}}function n(P){return P.length===0?null:P[0]}function r(P){if(P.length===0)return null;var I=P[0],U=P.pop();if(U!==I){P[0]=U;e:for(var $=0,Q=P.length,te=Q>>>1;$<te;){var pe=2*($+1)-1,Ce=P[pe],ie=pe+1,$e=P[ie];if(0>o(Ce,U))ie<Q&&0>o($e,Ce)?(P[$]=$e,P[ie]=U,$=ie):(P[$]=Ce,P[pe]=U,$=pe);else if(ie<Q&&0>o($e,U))P[$]=$e,P[ie]=U,$=ie;else break e}}return I}function o(P,I){var U=P.sortIndex-I.sortIndex;return U!==0?U:P.id-I.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}var c=[],u=[],p=1,f=null,d=3,y=!1,b=!1,v=!1,w=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,g=typeof setImmediate!="undefined"?setImmediate:null;typeof navigator!="undefined"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function x(P){for(var I=n(u);I!==null;){if(I.callback===null)r(u);else if(I.startTime<=P)r(u),I.sortIndex=I.expirationTime,t(c,I);else break;I=n(u)}}function S(P){if(v=!1,x(P),!b)if(n(c)!==null)b=!0,z(C);else{var I=n(u);I!==null&&q(S,I.startTime-P)}}function C(P,I){b=!1,v&&(v=!1,m(R),R=-1),y=!0;var U=d;try{for(x(I),f=n(c);f!==null&&(!(f.expirationTime>I)||P&&!B());){var $=f.callback;if(typeof $=="function"){f.callback=null,d=f.priorityLevel;var Q=$(f.expirationTime<=I);I=e.unstable_now(),typeof Q=="function"?f.callback=Q:f===n(c)&&r(c),x(I)}else r(c);f=n(c)}if(f!==null)var te=!0;else{var pe=n(u);pe!==null&&q(S,pe.startTime-I),te=!1}return te}finally{f=null,d=U,y=!1}}var T=!1,k=null,R=-1,_=5,O=-1;function B(){return!(e.unstable_now()-O<_)}function L(){if(k!==null){var P=e.unstable_now();O=P;var I=!0;try{I=k(!0,P)}finally{I?V():(T=!1,k=null)}}else T=!1}var V;if(typeof g=="function")V=function(){g(L)};else if(typeof MessageChannel!="undefined"){var Y=new MessageChannel,M=Y.port2;Y.port1.onmessage=L,V=function(){M.postMessage(null)}}else V=function(){w(L,0)};function z(P){k=P,T||(T=!0,V())}function q(P,I){R=w(function(){P(e.unstable_now())},I)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(P){P.callback=null},e.unstable_continueExecution=function(){b||y||(b=!0,z(C))},e.unstable_forceFrameRate=function(P){0>P||125<P?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<P?Math.floor(1e3/P):5},e.unstable_getCurrentPriorityLevel=function(){return d},e.unstable_getFirstCallbackNode=function(){return n(c)},e.unstable_next=function(P){switch(d){case 1:case 2:case 3:var I=3;break;default:I=d}var U=d;d=I;try{return P()}finally{d=U}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(P,I){switch(P){case 1:case 2:case 3:case 4:case 5:break;default:P=3}var U=d;d=P;try{return I()}finally{d=U}},e.unstable_scheduleCallback=function(P,I,U){var $=e.unstable_now();switch(typeof U=="object"&&U!==null?(U=U.delay,U=typeof U=="number"&&0<U?$+U:$):U=$,P){case 1:var Q=-1;break;case 2:Q=250;break;case 5:Q=**********;break;case 4:Q=1e4;break;default:Q=5e3}return Q=U+Q,P={id:p++,callback:I,priorityLevel:P,startTime:U,expirationTime:Q,sortIndex:-1},U>$?(P.sortIndex=U,t(u,P),n(c)===null&&P===n(u)&&(v?(m(R),R=-1):v=!0,q(S,U-$))):(P.sortIndex=Q,t(c,P),b||y||(b=!0,z(C))),P},e.unstable_shouldYield=B,e.unstable_wrapCallback=function(P){var I=d;return function(){var U=d;d=I;try{return P.apply(this,arguments)}finally{d=U}}}})(km);Em.exports=km;var C1=Em.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var j1=h,jt=C1;function A(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Pm=new Set,_s={};function Vr(e,t){zo(e,t),zo(e+"Capture",t)}function zo(e,t){for(_s[e]=t,e=0;e<t.length;e++)Pm.add(t[e])}var Sn=!(typeof window=="undefined"||typeof window.document=="undefined"||typeof window.document.createElement=="undefined"),Pc=Object.prototype.hasOwnProperty,E1=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Xf={},Zf={};function k1(e){return Pc.call(Zf,e)?!0:Pc.call(Xf,e)?!1:E1.test(e)?Zf[e]=!0:(Xf[e]=!0,!1)}function P1(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function T1(e,t,n,r){if(t===null||typeof t=="undefined"||P1(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function lt(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Ke={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ke[e]=new lt(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ke[t]=new lt(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ke[e]=new lt(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ke[e]=new lt(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ke[e]=new lt(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ke[e]=new lt(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ke[e]=new lt(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ke[e]=new lt(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ke[e]=new lt(e,5,!1,e.toLowerCase(),null,!1,!1)});var fd=/[\-:]([a-z])/g;function pd(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(fd,pd);Ke[t]=new lt(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(fd,pd);Ke[t]=new lt(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(fd,pd);Ke[t]=new lt(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ke[e]=new lt(e,1,!1,e.toLowerCase(),null,!1,!1)});Ke.xlinkHref=new lt("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ke[e]=new lt(e,1,!1,e.toLowerCase(),null,!0,!0)});function hd(e,t,n,r){var o=Ke.hasOwnProperty(t)?Ke[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(T1(t,n,o,r)&&(n=null),r||o===null?k1(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Pn=j1.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,bi=Symbol.for("react.element"),ro=Symbol.for("react.portal"),oo=Symbol.for("react.fragment"),md=Symbol.for("react.strict_mode"),Tc=Symbol.for("react.profiler"),Tm=Symbol.for("react.provider"),Rm=Symbol.for("react.context"),gd=Symbol.for("react.forward_ref"),Rc=Symbol.for("react.suspense"),_c=Symbol.for("react.suspense_list"),vd=Symbol.for("react.memo"),Dn=Symbol.for("react.lazy"),_m=Symbol.for("react.offscreen"),Jf=Symbol.iterator;function ss(e){return e===null||typeof e!="object"?null:(e=Jf&&e[Jf]||e["@@iterator"],typeof e=="function"?e:null)}var Pe=Object.assign,Ul;function ms(e){if(Ul===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ul=t&&t[1]||""}return`
`+Ul+e}var Bl=!1;function Wl(e,t){if(!e||Bl)return"";Bl=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var o=u.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,l=s.length-1;1<=i&&0<=l&&o[i]!==s[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==s[l]){if(i!==1||l!==1)do if(i--,l--,0>l||o[i]!==s[l]){var c=`
`+o[i].replace(" at new "," at ");return e.displayName&&c.includes("<anonymous>")&&(c=c.replace("<anonymous>",e.displayName)),c}while(1<=i&&0<=l);break}}}finally{Bl=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ms(e):""}function R1(e){switch(e.tag){case 5:return ms(e.type);case 16:return ms("Lazy");case 13:return ms("Suspense");case 19:return ms("SuspenseList");case 0:case 2:case 15:return e=Wl(e.type,!1),e;case 11:return e=Wl(e.type.render,!1),e;case 1:return e=Wl(e.type,!0),e;default:return""}}function Oc(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case oo:return"Fragment";case ro:return"Portal";case Tc:return"Profiler";case md:return"StrictMode";case Rc:return"Suspense";case _c:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Rm:return(e.displayName||"Context")+".Consumer";case Tm:return(e._context.displayName||"Context")+".Provider";case gd:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case vd:return t=e.displayName||null,t!==null?t:Oc(e.type)||"Memo";case Dn:t=e._payload,e=e._init;try{return Oc(e(t))}catch(n){}}return null}function _1(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Oc(t);case 8:return t===md?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function ir(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Om(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function O1(e){var t=Om(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n!="undefined"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Si(e){e._valueTracker||(e._valueTracker=O1(e))}function Am(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Om(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function fa(e){if(e=e||(typeof document!="undefined"?document:void 0),typeof e=="undefined")return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ac(e,t){var n=t.checked;return Pe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function ep(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=ir(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Lm(e,t){t=t.checked,t!=null&&hd(e,"checked",t,!1)}function Lc(e,t){Lm(e,t);var n=ir(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Mc(e,t.type,n):t.hasOwnProperty("defaultValue")&&Mc(e,t.type,ir(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function tp(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Mc(e,t,n){(t!=="number"||fa(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var gs=Array.isArray;function go(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ir(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ic(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(A(91));return Pe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function np(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(A(92));if(gs(n)){if(1<n.length)throw Error(A(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:ir(n)}}function Mm(e,t){var n=ir(t.value),r=ir(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function rp(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Im(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Dc(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Im(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ni,Dm=function(e){return typeof MSApp!="undefined"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ni=Ni||document.createElement("div"),Ni.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ni.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Os(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var bs={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},A1=["Webkit","ms","Moz","O"];Object.keys(bs).forEach(function(e){A1.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),bs[t]=bs[e]})});function Fm(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||bs.hasOwnProperty(e)&&bs[e]?(""+t).trim():t+"px"}function $m(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Fm(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var L1=Pe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Fc(e,t){if(t){if(L1[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(A(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(A(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(A(61))}if(t.style!=null&&typeof t.style!="object")throw Error(A(62))}}function $c(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var zc=null;function yd(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Uc=null,vo=null,yo=null;function op(e){if(e=ai(e)){if(typeof Uc!="function")throw Error(A(280));var t=e.stateNode;t&&(t=Ja(t),Uc(e.stateNode,e.type,t))}}function zm(e){vo?yo?yo.push(e):yo=[e]:vo=e}function Um(){if(vo){var e=vo,t=yo;if(yo=vo=null,op(e),t)for(e=0;e<t.length;e++)op(t[e])}}function Bm(e,t){return e(t)}function Wm(){}var Hl=!1;function Hm(e,t,n){if(Hl)return e(t,n);Hl=!0;try{return Bm(e,t,n)}finally{Hl=!1,(vo!==null||yo!==null)&&(Wm(),Um())}}function As(e,t){var n=e.stateNode;if(n===null)return null;var r=Ja(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(A(231,t,typeof n));return n}var Bc=!1;if(Sn)try{var is={};Object.defineProperty(is,"passive",{get:function(){Bc=!0}}),window.addEventListener("test",is,is),window.removeEventListener("test",is,is)}catch(e){Bc=!1}function M1(e,t,n,r,o,s,i,l,c){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(p){this.onError(p)}}var Ss=!1,pa=null,ha=!1,Wc=null,I1={onError:function(e){Ss=!0,pa=e}};function D1(e,t,n,r,o,s,i,l,c){Ss=!1,pa=null,M1.apply(I1,arguments)}function F1(e,t,n,r,o,s,i,l,c){if(D1.apply(this,arguments),Ss){if(Ss){var u=pa;Ss=!1,pa=null}else throw Error(A(198));ha||(ha=!0,Wc=u)}}function Qr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Vm(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function sp(e){if(Qr(e)!==e)throw Error(A(188))}function $1(e){var t=e.alternate;if(!t){if(t=Qr(e),t===null)throw Error(A(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return sp(o),e;if(s===r)return sp(o),t;s=s.sibling}throw Error(A(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=s;break}if(l===r){i=!0,r=o,n=s;break}l=l.sibling}if(!i){for(l=s.child;l;){if(l===n){i=!0,n=s,r=o;break}if(l===r){i=!0,r=s,n=o;break}l=l.sibling}if(!i)throw Error(A(189))}}if(n.alternate!==r)throw Error(A(190))}if(n.tag!==3)throw Error(A(188));return n.stateNode.current===n?e:t}function Qm(e){return e=$1(e),e!==null?Km(e):null}function Km(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Km(e);if(t!==null)return t;e=e.sibling}return null}var qm=jt.unstable_scheduleCallback,ip=jt.unstable_cancelCallback,z1=jt.unstable_shouldYield,U1=jt.unstable_requestPaint,Oe=jt.unstable_now,B1=jt.unstable_getCurrentPriorityLevel,xd=jt.unstable_ImmediatePriority,Gm=jt.unstable_UserBlockingPriority,ma=jt.unstable_NormalPriority,W1=jt.unstable_LowPriority,Ym=jt.unstable_IdlePriority,Ga=null,cn=null;function H1(e){if(cn&&typeof cn.onCommitFiberRoot=="function")try{cn.onCommitFiberRoot(Ga,e,void 0,(e.current.flags&128)===128)}catch(t){}}var Qt=Math.clz32?Math.clz32:K1,V1=Math.log,Q1=Math.LN2;function K1(e){return e>>>=0,e===0?32:31-(V1(e)/Q1|0)|0}var Ci=64,ji=4194304;function vs(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ga(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var l=i&~o;l!==0?r=vs(l):(s&=i,s!==0&&(r=vs(s)))}else i=n&~o,i!==0?r=vs(i):s!==0&&(r=vs(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Qt(t),o=1<<n,r|=e[n],t&=~o;return r}function q1(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function G1(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-Qt(s),l=1<<i,c=o[i];c===-1?(!(l&n)||l&r)&&(o[i]=q1(l,t)):c<=t&&(e.expiredLanes|=l),s&=~l}}function Hc(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Xm(){var e=Ci;return Ci<<=1,!(Ci&4194240)&&(Ci=64),e}function Vl(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function si(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Qt(t),e[t]=n}function Y1(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Qt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function wd(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Qt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var he=0;function Zm(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Jm,bd,eg,tg,ng,Vc=!1,Ei=[],Xn=null,Zn=null,Jn=null,Ls=new Map,Ms=new Map,$n=[],X1="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function ap(e,t){switch(e){case"focusin":case"focusout":Xn=null;break;case"dragenter":case"dragleave":Zn=null;break;case"mouseover":case"mouseout":Jn=null;break;case"pointerover":case"pointerout":Ls.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ms.delete(t.pointerId)}}function as(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=ai(t),t!==null&&bd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function Z1(e,t,n,r,o){switch(t){case"focusin":return Xn=as(Xn,e,t,n,r,o),!0;case"dragenter":return Zn=as(Zn,e,t,n,r,o),!0;case"mouseover":return Jn=as(Jn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return Ls.set(s,as(Ls.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,Ms.set(s,as(Ms.get(s)||null,e,t,n,r,o)),!0}return!1}function rg(e){var t=Nr(e.target);if(t!==null){var n=Qr(t);if(n!==null){if(t=n.tag,t===13){if(t=Vm(n),t!==null){e.blockedOn=t,ng(e.priority,function(){eg(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Yi(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Qc(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);zc=r,n.target.dispatchEvent(r),zc=null}else return t=ai(n),t!==null&&bd(t),e.blockedOn=n,!1;t.shift()}return!0}function lp(e,t,n){Yi(e)&&n.delete(t)}function J1(){Vc=!1,Xn!==null&&Yi(Xn)&&(Xn=null),Zn!==null&&Yi(Zn)&&(Zn=null),Jn!==null&&Yi(Jn)&&(Jn=null),Ls.forEach(lp),Ms.forEach(lp)}function ls(e,t){e.blockedOn===t&&(e.blockedOn=null,Vc||(Vc=!0,jt.unstable_scheduleCallback(jt.unstable_NormalPriority,J1)))}function Is(e){function t(o){return ls(o,e)}if(0<Ei.length){ls(Ei[0],e);for(var n=1;n<Ei.length;n++){var r=Ei[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Xn!==null&&ls(Xn,e),Zn!==null&&ls(Zn,e),Jn!==null&&ls(Jn,e),Ls.forEach(t),Ms.forEach(t),n=0;n<$n.length;n++)r=$n[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<$n.length&&(n=$n[0],n.blockedOn===null);)rg(n),n.blockedOn===null&&$n.shift()}var xo=Pn.ReactCurrentBatchConfig,va=!0;function ew(e,t,n,r){var o=he,s=xo.transition;xo.transition=null;try{he=1,Sd(e,t,n,r)}finally{he=o,xo.transition=s}}function tw(e,t,n,r){var o=he,s=xo.transition;xo.transition=null;try{he=4,Sd(e,t,n,r)}finally{he=o,xo.transition=s}}function Sd(e,t,n,r){if(va){var o=Qc(e,t,n,r);if(o===null)tc(e,t,r,ya,n),ap(e,r);else if(Z1(o,e,t,n,r))r.stopPropagation();else if(ap(e,r),t&4&&-1<X1.indexOf(e)){for(;o!==null;){var s=ai(o);if(s!==null&&Jm(s),s=Qc(e,t,n,r),s===null&&tc(e,t,r,ya,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else tc(e,t,r,null,n)}}var ya=null;function Qc(e,t,n,r){if(ya=null,e=yd(r),e=Nr(e),e!==null)if(t=Qr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Vm(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ya=e,null}function og(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(B1()){case xd:return 1;case Gm:return 4;case ma:case W1:return 16;case Ym:return 536870912;default:return 16}default:return 16}}var qn=null,Nd=null,Xi=null;function sg(){if(Xi)return Xi;var e,t=Nd,n=t.length,r,o="value"in qn?qn.value:qn.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Xi=o.slice(e,1<r?1-r:void 0)}function Zi(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ki(){return!0}function cp(){return!1}function Pt(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ki:cp,this.isPropagationStopped=cp,this}return Pe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ki)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ki)},persist:function(){},isPersistent:ki}),t}var Xo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Cd=Pt(Xo),ii=Pe({},Xo,{view:0,detail:0}),nw=Pt(ii),Ql,Kl,cs,Ya=Pe({},ii,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:jd,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==cs&&(cs&&e.type==="mousemove"?(Ql=e.screenX-cs.screenX,Kl=e.screenY-cs.screenY):Kl=Ql=0,cs=e),Ql)},movementY:function(e){return"movementY"in e?e.movementY:Kl}}),up=Pt(Ya),rw=Pe({},Ya,{dataTransfer:0}),ow=Pt(rw),sw=Pe({},ii,{relatedTarget:0}),ql=Pt(sw),iw=Pe({},Xo,{animationName:0,elapsedTime:0,pseudoElement:0}),aw=Pt(iw),lw=Pe({},Xo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),cw=Pt(lw),uw=Pe({},Xo,{data:0}),dp=Pt(uw),dw={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},fw={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},pw={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function hw(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=pw[e])?!!t[e]:!1}function jd(){return hw}var mw=Pe({},ii,{key:function(e){if(e.key){var t=dw[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Zi(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?fw[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:jd,charCode:function(e){return e.type==="keypress"?Zi(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Zi(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),gw=Pt(mw),vw=Pe({},Ya,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),fp=Pt(vw),yw=Pe({},ii,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:jd}),xw=Pt(yw),ww=Pe({},Xo,{propertyName:0,elapsedTime:0,pseudoElement:0}),bw=Pt(ww),Sw=Pe({},Ya,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Nw=Pt(Sw),Cw=[9,13,27,32],Ed=Sn&&"CompositionEvent"in window,Ns=null;Sn&&"documentMode"in document&&(Ns=document.documentMode);var jw=Sn&&"TextEvent"in window&&!Ns,ig=Sn&&(!Ed||Ns&&8<Ns&&11>=Ns),pp=" ",hp=!1;function ag(e,t){switch(e){case"keyup":return Cw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function lg(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var so=!1;function Ew(e,t){switch(e){case"compositionend":return lg(t);case"keypress":return t.which!==32?null:(hp=!0,pp);case"textInput":return e=t.data,e===pp&&hp?null:e;default:return null}}function kw(e,t){if(so)return e==="compositionend"||!Ed&&ag(e,t)?(e=sg(),Xi=Nd=qn=null,so=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ig&&t.locale!=="ko"?null:t.data;default:return null}}var Pw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function mp(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Pw[e.type]:t==="textarea"}function cg(e,t,n,r){zm(r),t=xa(t,"onChange"),0<t.length&&(n=new Cd("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Cs=null,Ds=null;function Tw(e){wg(e,0)}function Xa(e){var t=lo(e);if(Am(t))return e}function Rw(e,t){if(e==="change")return t}var ug=!1;if(Sn){var Gl;if(Sn){var Yl="oninput"in document;if(!Yl){var gp=document.createElement("div");gp.setAttribute("oninput","return;"),Yl=typeof gp.oninput=="function"}Gl=Yl}else Gl=!1;ug=Gl&&(!document.documentMode||9<document.documentMode)}function vp(){Cs&&(Cs.detachEvent("onpropertychange",dg),Ds=Cs=null)}function dg(e){if(e.propertyName==="value"&&Xa(Ds)){var t=[];cg(t,Ds,e,yd(e)),Hm(Tw,t)}}function _w(e,t,n){e==="focusin"?(vp(),Cs=t,Ds=n,Cs.attachEvent("onpropertychange",dg)):e==="focusout"&&vp()}function Ow(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Xa(Ds)}function Aw(e,t){if(e==="click")return Xa(t)}function Lw(e,t){if(e==="input"||e==="change")return Xa(t)}function Mw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var qt=typeof Object.is=="function"?Object.is:Mw;function Fs(e,t){if(qt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Pc.call(t,o)||!qt(e[o],t[o]))return!1}return!0}function yp(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function xp(e,t){var n=yp(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=yp(n)}}function fg(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?fg(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function pg(){for(var e=window,t=fa();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch(r){n=!1}if(n)e=t.contentWindow;else break;t=fa(e.document)}return t}function kd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function Iw(e){var t=pg(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fg(n.ownerDocument.documentElement,n)){if(r!==null&&kd(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=xp(n,s);var i=xp(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Dw=Sn&&"documentMode"in document&&11>=document.documentMode,io=null,Kc=null,js=null,qc=!1;function wp(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;qc||io==null||io!==fa(r)||(r=io,"selectionStart"in r&&kd(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),js&&Fs(js,r)||(js=r,r=xa(Kc,"onSelect"),0<r.length&&(t=new Cd("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=io)))}function Pi(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var ao={animationend:Pi("Animation","AnimationEnd"),animationiteration:Pi("Animation","AnimationIteration"),animationstart:Pi("Animation","AnimationStart"),transitionend:Pi("Transition","TransitionEnd")},Xl={},hg={};Sn&&(hg=document.createElement("div").style,"AnimationEvent"in window||(delete ao.animationend.animation,delete ao.animationiteration.animation,delete ao.animationstart.animation),"TransitionEvent"in window||delete ao.transitionend.transition);function Za(e){if(Xl[e])return Xl[e];if(!ao[e])return e;var t=ao[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in hg)return Xl[e]=t[n];return e}var mg=Za("animationend"),gg=Za("animationiteration"),vg=Za("animationstart"),yg=Za("transitionend"),xg=new Map,bp="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function fr(e,t){xg.set(e,t),Vr(t,[e])}for(var Zl=0;Zl<bp.length;Zl++){var Jl=bp[Zl],Fw=Jl.toLowerCase(),$w=Jl[0].toUpperCase()+Jl.slice(1);fr(Fw,"on"+$w)}fr(mg,"onAnimationEnd");fr(gg,"onAnimationIteration");fr(vg,"onAnimationStart");fr("dblclick","onDoubleClick");fr("focusin","onFocus");fr("focusout","onBlur");fr(yg,"onTransitionEnd");zo("onMouseEnter",["mouseout","mouseover"]);zo("onMouseLeave",["mouseout","mouseover"]);zo("onPointerEnter",["pointerout","pointerover"]);zo("onPointerLeave",["pointerout","pointerover"]);Vr("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Vr("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Vr("onBeforeInput",["compositionend","keypress","textInput","paste"]);Vr("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Vr("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Vr("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var ys="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),zw=new Set("cancel close invalid load scroll toggle".split(" ").concat(ys));function Sp(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,F1(r,t,void 0,e),e.currentTarget=null}function wg(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],c=l.instance,u=l.currentTarget;if(l=l.listener,c!==s&&o.isPropagationStopped())break e;Sp(o,l,u),s=c}else for(i=0;i<r.length;i++){if(l=r[i],c=l.instance,u=l.currentTarget,l=l.listener,c!==s&&o.isPropagationStopped())break e;Sp(o,l,u),s=c}}}if(ha)throw e=Wc,ha=!1,Wc=null,e}function be(e,t){var n=t[Jc];n===void 0&&(n=t[Jc]=new Set);var r=e+"__bubble";n.has(r)||(bg(t,e,2,!1),n.add(r))}function ec(e,t,n){var r=0;t&&(r|=4),bg(n,e,r,t)}var Ti="_reactListening"+Math.random().toString(36).slice(2);function $s(e){if(!e[Ti]){e[Ti]=!0,Pm.forEach(function(n){n!=="selectionchange"&&(zw.has(n)||ec(n,!1,e),ec(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Ti]||(t[Ti]=!0,ec("selectionchange",!1,t))}}function bg(e,t,n,r){switch(og(t)){case 1:var o=ew;break;case 4:o=tw;break;default:o=Sd}n=o.bind(null,t,n,e),o=void 0,!Bc||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function tc(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var c=i.tag;if((c===3||c===4)&&(c=i.stateNode.containerInfo,c===o||c.nodeType===8&&c.parentNode===o))return;i=i.return}for(;l!==null;){if(i=Nr(l),i===null)return;if(c=i.tag,c===5||c===6){r=s=i;continue e}l=l.parentNode}}r=r.return}Hm(function(){var u=s,p=yd(n),f=[];e:{var d=xg.get(e);if(d!==void 0){var y=Cd,b=e;switch(e){case"keypress":if(Zi(n)===0)break e;case"keydown":case"keyup":y=gw;break;case"focusin":b="focus",y=ql;break;case"focusout":b="blur",y=ql;break;case"beforeblur":case"afterblur":y=ql;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":y=up;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":y=ow;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":y=xw;break;case mg:case gg:case vg:y=aw;break;case yg:y=bw;break;case"scroll":y=nw;break;case"wheel":y=Nw;break;case"copy":case"cut":case"paste":y=cw;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":y=fp}var v=(t&4)!==0,w=!v&&e==="scroll",m=v?d!==null?d+"Capture":null:d;v=[];for(var g=u,x;g!==null;){x=g;var S=x.stateNode;if(x.tag===5&&S!==null&&(x=S,m!==null&&(S=As(g,m),S!=null&&v.push(zs(g,S,x)))),w)break;g=g.return}0<v.length&&(d=new y(d,b,null,n,p),f.push({event:d,listeners:v}))}}if(!(t&7)){e:{if(d=e==="mouseover"||e==="pointerover",y=e==="mouseout"||e==="pointerout",d&&n!==zc&&(b=n.relatedTarget||n.fromElement)&&(Nr(b)||b[Nn]))break e;if((y||d)&&(d=p.window===p?p:(d=p.ownerDocument)?d.defaultView||d.parentWindow:window,y?(b=n.relatedTarget||n.toElement,y=u,b=b?Nr(b):null,b!==null&&(w=Qr(b),b!==w||b.tag!==5&&b.tag!==6)&&(b=null)):(y=null,b=u),y!==b)){if(v=up,S="onMouseLeave",m="onMouseEnter",g="mouse",(e==="pointerout"||e==="pointerover")&&(v=fp,S="onPointerLeave",m="onPointerEnter",g="pointer"),w=y==null?d:lo(y),x=b==null?d:lo(b),d=new v(S,g+"leave",y,n,p),d.target=w,d.relatedTarget=x,S=null,Nr(p)===u&&(v=new v(m,g+"enter",b,n,p),v.target=x,v.relatedTarget=w,S=v),w=S,y&&b)t:{for(v=y,m=b,g=0,x=v;x;x=Zr(x))g++;for(x=0,S=m;S;S=Zr(S))x++;for(;0<g-x;)v=Zr(v),g--;for(;0<x-g;)m=Zr(m),x--;for(;g--;){if(v===m||m!==null&&v===m.alternate)break t;v=Zr(v),m=Zr(m)}v=null}else v=null;y!==null&&Np(f,d,y,v,!1),b!==null&&w!==null&&Np(f,w,b,v,!0)}}e:{if(d=u?lo(u):window,y=d.nodeName&&d.nodeName.toLowerCase(),y==="select"||y==="input"&&d.type==="file")var C=Rw;else if(mp(d))if(ug)C=Lw;else{C=Ow;var T=_w}else(y=d.nodeName)&&y.toLowerCase()==="input"&&(d.type==="checkbox"||d.type==="radio")&&(C=Aw);if(C&&(C=C(e,u))){cg(f,C,n,p);break e}T&&T(e,d,u),e==="focusout"&&(T=d._wrapperState)&&T.controlled&&d.type==="number"&&Mc(d,"number",d.value)}switch(T=u?lo(u):window,e){case"focusin":(mp(T)||T.contentEditable==="true")&&(io=T,Kc=u,js=null);break;case"focusout":js=Kc=io=null;break;case"mousedown":qc=!0;break;case"contextmenu":case"mouseup":case"dragend":qc=!1,wp(f,n,p);break;case"selectionchange":if(Dw)break;case"keydown":case"keyup":wp(f,n,p)}var k;if(Ed)e:{switch(e){case"compositionstart":var R="onCompositionStart";break e;case"compositionend":R="onCompositionEnd";break e;case"compositionupdate":R="onCompositionUpdate";break e}R=void 0}else so?ag(e,n)&&(R="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(R="onCompositionStart");R&&(ig&&n.locale!=="ko"&&(so||R!=="onCompositionStart"?R==="onCompositionEnd"&&so&&(k=sg()):(qn=p,Nd="value"in qn?qn.value:qn.textContent,so=!0)),T=xa(u,R),0<T.length&&(R=new dp(R,e,null,n,p),f.push({event:R,listeners:T}),k?R.data=k:(k=lg(n),k!==null&&(R.data=k)))),(k=jw?Ew(e,n):kw(e,n))&&(u=xa(u,"onBeforeInput"),0<u.length&&(p=new dp("onBeforeInput","beforeinput",null,n,p),f.push({event:p,listeners:u}),p.data=k))}wg(f,t)})}function zs(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xa(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=As(e,n),s!=null&&r.unshift(zs(e,s,o)),s=As(e,t),s!=null&&r.push(zs(e,s,o))),e=e.return}return r}function Zr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Np(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var l=n,c=l.alternate,u=l.stateNode;if(c!==null&&c===r)break;l.tag===5&&u!==null&&(l=u,o?(c=As(n,s),c!=null&&i.unshift(zs(n,c,l))):o||(c=As(n,s),c!=null&&i.push(zs(n,c,l)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var Uw=/\r\n?/g,Bw=/\u0000|\uFFFD/g;function Cp(e){return(typeof e=="string"?e:""+e).replace(Uw,`
`).replace(Bw,"")}function Ri(e,t,n){if(t=Cp(t),Cp(e)!==t&&n)throw Error(A(425))}function wa(){}var Gc=null,Yc=null;function Xc(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zc=typeof setTimeout=="function"?setTimeout:void 0,Ww=typeof clearTimeout=="function"?clearTimeout:void 0,jp=typeof Promise=="function"?Promise:void 0,Hw=typeof queueMicrotask=="function"?queueMicrotask:typeof jp!="undefined"?function(e){return jp.resolve(null).then(e).catch(Vw)}:Zc;function Vw(e){setTimeout(function(){throw e})}function nc(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),Is(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);Is(t)}function er(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Ep(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Zo=Math.random().toString(36).slice(2),an="__reactFiber$"+Zo,Us="__reactProps$"+Zo,Nn="__reactContainer$"+Zo,Jc="__reactEvents$"+Zo,Qw="__reactListeners$"+Zo,Kw="__reactHandles$"+Zo;function Nr(e){var t=e[an];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Nn]||n[an]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Ep(e);e!==null;){if(n=e[an])return n;e=Ep(e)}return t}e=n,n=e.parentNode}return null}function ai(e){return e=e[an]||e[Nn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function lo(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(A(33))}function Ja(e){return e[Us]||null}var eu=[],co=-1;function pr(e){return{current:e}}function Se(e){0>co||(e.current=eu[co],eu[co]=null,co--)}function ye(e,t){co++,eu[co]=e.current,e.current=t}var ar={},tt=pr(ar),ht=pr(!1),Ir=ar;function Uo(e,t){var n=e.type.contextTypes;if(!n)return ar;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function mt(e){return e=e.childContextTypes,e!=null}function ba(){Se(ht),Se(tt)}function kp(e,t,n){if(tt.current!==ar)throw Error(A(168));ye(tt,t),ye(ht,n)}function Sg(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(A(108,_1(e)||"Unknown",o));return Pe({},n,r)}function Sa(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ar,Ir=tt.current,ye(tt,e),ye(ht,ht.current),!0}function Pp(e,t,n){var r=e.stateNode;if(!r)throw Error(A(169));n?(e=Sg(e,t,Ir),r.__reactInternalMemoizedMergedChildContext=e,Se(ht),Se(tt),ye(tt,e)):Se(ht),ye(ht,n)}var yn=null,el=!1,rc=!1;function Ng(e){yn===null?yn=[e]:yn.push(e)}function qw(e){el=!0,Ng(e)}function hr(){if(!rc&&yn!==null){rc=!0;var e=0,t=he;try{var n=yn;for(he=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}yn=null,el=!1}catch(o){throw yn!==null&&(yn=yn.slice(e+1)),qm(xd,hr),o}finally{he=t,rc=!1}}return null}var uo=[],fo=0,Na=null,Ca=0,Ot=[],At=0,Dr=null,xn=1,wn="";function wr(e,t){uo[fo++]=Ca,uo[fo++]=Na,Na=e,Ca=t}function Cg(e,t,n){Ot[At++]=xn,Ot[At++]=wn,Ot[At++]=Dr,Dr=e;var r=xn;e=wn;var o=32-Qt(r)-1;r&=~(1<<o),n+=1;var s=32-Qt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,xn=1<<32-Qt(t)+o|n<<o|r,wn=s+e}else xn=1<<s|n<<o|r,wn=e}function Pd(e){e.return!==null&&(wr(e,1),Cg(e,1,0))}function Td(e){for(;e===Na;)Na=uo[--fo],uo[fo]=null,Ca=uo[--fo],uo[fo]=null;for(;e===Dr;)Dr=Ot[--At],Ot[At]=null,wn=Ot[--At],Ot[At]=null,xn=Ot[--At],Ot[At]=null}var Nt=null,bt=null,je=!1,Ht=null;function jg(e,t){var n=Lt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Tp(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Nt=e,bt=er(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Nt=e,bt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Dr!==null?{id:xn,overflow:wn}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=Lt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Nt=e,bt=null,!0):!1;default:return!1}}function tu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function nu(e){if(je){var t=bt;if(t){var n=t;if(!Tp(e,t)){if(tu(e))throw Error(A(418));t=er(n.nextSibling);var r=Nt;t&&Tp(e,t)?jg(r,n):(e.flags=e.flags&-4097|2,je=!1,Nt=e)}}else{if(tu(e))throw Error(A(418));e.flags=e.flags&-4097|2,je=!1,Nt=e}}}function Rp(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Nt=e}function _i(e){if(e!==Nt)return!1;if(!je)return Rp(e),je=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Xc(e.type,e.memoizedProps)),t&&(t=bt)){if(tu(e))throw Eg(),Error(A(418));for(;t;)jg(e,t),t=er(t.nextSibling)}if(Rp(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(A(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){bt=er(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}bt=null}}else bt=Nt?er(e.stateNode.nextSibling):null;return!0}function Eg(){for(var e=bt;e;)e=er(e.nextSibling)}function Bo(){bt=Nt=null,je=!1}function Rd(e){Ht===null?Ht=[e]:Ht.push(e)}var Gw=Pn.ReactCurrentBatchConfig;function us(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(A(309));var r=n.stateNode}if(!r)throw Error(A(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var l=o.refs;i===null?delete l[s]:l[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(A(284));if(!n._owner)throw Error(A(290,e))}return e}function Oi(e,t){throw e=Object.prototype.toString.call(t),Error(A(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function _p(e){var t=e._init;return t(e._payload)}function kg(e){function t(m,g){if(e){var x=m.deletions;x===null?(m.deletions=[g],m.flags|=16):x.push(g)}}function n(m,g){if(!e)return null;for(;g!==null;)t(m,g),g=g.sibling;return null}function r(m,g){for(m=new Map;g!==null;)g.key!==null?m.set(g.key,g):m.set(g.index,g),g=g.sibling;return m}function o(m,g){return m=or(m,g),m.index=0,m.sibling=null,m}function s(m,g,x){return m.index=x,e?(x=m.alternate,x!==null?(x=x.index,x<g?(m.flags|=2,g):x):(m.flags|=2,g)):(m.flags|=1048576,g)}function i(m){return e&&m.alternate===null&&(m.flags|=2),m}function l(m,g,x,S){return g===null||g.tag!==6?(g=uc(x,m.mode,S),g.return=m,g):(g=o(g,x),g.return=m,g)}function c(m,g,x,S){var C=x.type;return C===oo?p(m,g,x.props.children,S,x.key):g!==null&&(g.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Dn&&_p(C)===g.type)?(S=o(g,x.props),S.ref=us(m,g,x),S.return=m,S):(S=sa(x.type,x.key,x.props,null,m.mode,S),S.ref=us(m,g,x),S.return=m,S)}function u(m,g,x,S){return g===null||g.tag!==4||g.stateNode.containerInfo!==x.containerInfo||g.stateNode.implementation!==x.implementation?(g=dc(x,m.mode,S),g.return=m,g):(g=o(g,x.children||[]),g.return=m,g)}function p(m,g,x,S,C){return g===null||g.tag!==7?(g=Mr(x,m.mode,S,C),g.return=m,g):(g=o(g,x),g.return=m,g)}function f(m,g,x){if(typeof g=="string"&&g!==""||typeof g=="number")return g=uc(""+g,m.mode,x),g.return=m,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case bi:return x=sa(g.type,g.key,g.props,null,m.mode,x),x.ref=us(m,null,g),x.return=m,x;case ro:return g=dc(g,m.mode,x),g.return=m,g;case Dn:var S=g._init;return f(m,S(g._payload),x)}if(gs(g)||ss(g))return g=Mr(g,m.mode,x,null),g.return=m,g;Oi(m,g)}return null}function d(m,g,x,S){var C=g!==null?g.key:null;if(typeof x=="string"&&x!==""||typeof x=="number")return C!==null?null:l(m,g,""+x,S);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case bi:return x.key===C?c(m,g,x,S):null;case ro:return x.key===C?u(m,g,x,S):null;case Dn:return C=x._init,d(m,g,C(x._payload),S)}if(gs(x)||ss(x))return C!==null?null:p(m,g,x,S,null);Oi(m,x)}return null}function y(m,g,x,S,C){if(typeof S=="string"&&S!==""||typeof S=="number")return m=m.get(x)||null,l(g,m,""+S,C);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case bi:return m=m.get(S.key===null?x:S.key)||null,c(g,m,S,C);case ro:return m=m.get(S.key===null?x:S.key)||null,u(g,m,S,C);case Dn:var T=S._init;return y(m,g,x,T(S._payload),C)}if(gs(S)||ss(S))return m=m.get(x)||null,p(g,m,S,C,null);Oi(g,S)}return null}function b(m,g,x,S){for(var C=null,T=null,k=g,R=g=0,_=null;k!==null&&R<x.length;R++){k.index>R?(_=k,k=null):_=k.sibling;var O=d(m,k,x[R],S);if(O===null){k===null&&(k=_);break}e&&k&&O.alternate===null&&t(m,k),g=s(O,g,R),T===null?C=O:T.sibling=O,T=O,k=_}if(R===x.length)return n(m,k),je&&wr(m,R),C;if(k===null){for(;R<x.length;R++)k=f(m,x[R],S),k!==null&&(g=s(k,g,R),T===null?C=k:T.sibling=k,T=k);return je&&wr(m,R),C}for(k=r(m,k);R<x.length;R++)_=y(k,m,R,x[R],S),_!==null&&(e&&_.alternate!==null&&k.delete(_.key===null?R:_.key),g=s(_,g,R),T===null?C=_:T.sibling=_,T=_);return e&&k.forEach(function(B){return t(m,B)}),je&&wr(m,R),C}function v(m,g,x,S){var C=ss(x);if(typeof C!="function")throw Error(A(150));if(x=C.call(x),x==null)throw Error(A(151));for(var T=C=null,k=g,R=g=0,_=null,O=x.next();k!==null&&!O.done;R++,O=x.next()){k.index>R?(_=k,k=null):_=k.sibling;var B=d(m,k,O.value,S);if(B===null){k===null&&(k=_);break}e&&k&&B.alternate===null&&t(m,k),g=s(B,g,R),T===null?C=B:T.sibling=B,T=B,k=_}if(O.done)return n(m,k),je&&wr(m,R),C;if(k===null){for(;!O.done;R++,O=x.next())O=f(m,O.value,S),O!==null&&(g=s(O,g,R),T===null?C=O:T.sibling=O,T=O);return je&&wr(m,R),C}for(k=r(m,k);!O.done;R++,O=x.next())O=y(k,m,R,O.value,S),O!==null&&(e&&O.alternate!==null&&k.delete(O.key===null?R:O.key),g=s(O,g,R),T===null?C=O:T.sibling=O,T=O);return e&&k.forEach(function(L){return t(m,L)}),je&&wr(m,R),C}function w(m,g,x,S){if(typeof x=="object"&&x!==null&&x.type===oo&&x.key===null&&(x=x.props.children),typeof x=="object"&&x!==null){switch(x.$$typeof){case bi:e:{for(var C=x.key,T=g;T!==null;){if(T.key===C){if(C=x.type,C===oo){if(T.tag===7){n(m,T.sibling),g=o(T,x.props.children),g.return=m,m=g;break e}}else if(T.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Dn&&_p(C)===T.type){n(m,T.sibling),g=o(T,x.props),g.ref=us(m,T,x),g.return=m,m=g;break e}n(m,T);break}else t(m,T);T=T.sibling}x.type===oo?(g=Mr(x.props.children,m.mode,S,x.key),g.return=m,m=g):(S=sa(x.type,x.key,x.props,null,m.mode,S),S.ref=us(m,g,x),S.return=m,m=S)}return i(m);case ro:e:{for(T=x.key;g!==null;){if(g.key===T)if(g.tag===4&&g.stateNode.containerInfo===x.containerInfo&&g.stateNode.implementation===x.implementation){n(m,g.sibling),g=o(g,x.children||[]),g.return=m,m=g;break e}else{n(m,g);break}else t(m,g);g=g.sibling}g=dc(x,m.mode,S),g.return=m,m=g}return i(m);case Dn:return T=x._init,w(m,g,T(x._payload),S)}if(gs(x))return b(m,g,x,S);if(ss(x))return v(m,g,x,S);Oi(m,x)}return typeof x=="string"&&x!==""||typeof x=="number"?(x=""+x,g!==null&&g.tag===6?(n(m,g.sibling),g=o(g,x),g.return=m,m=g):(n(m,g),g=uc(x,m.mode,S),g.return=m,m=g),i(m)):n(m,g)}return w}var Wo=kg(!0),Pg=kg(!1),ja=pr(null),Ea=null,po=null,_d=null;function Od(){_d=po=Ea=null}function Ad(e){var t=ja.current;Se(ja),e._currentValue=t}function ru(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function wo(e,t){Ea=e,_d=po=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(pt=!0),e.firstContext=null)}function It(e){var t=e._currentValue;if(_d!==e)if(e={context:e,memoizedValue:t,next:null},po===null){if(Ea===null)throw Error(A(308));po=e,Ea.dependencies={lanes:0,firstContext:e}}else po=po.next=e;return t}var Cr=null;function Ld(e){Cr===null?Cr=[e]:Cr.push(e)}function Tg(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,Ld(t)):(n.next=o.next,o.next=n),t.interleaved=n,Cn(e,r)}function Cn(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Fn=!1;function Md(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Rg(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function bn(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function tr(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,ae&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Cn(e,n)}return o=r.interleaved,o===null?(t.next=t,Ld(r)):(t.next=o.next,o.next=t),r.interleaved=t,Cn(e,n)}function Ji(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,wd(e,n)}}function Op(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function ka(e,t,n,r){var o=e.updateQueue;Fn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var c=l,u=c.next;c.next=null,i===null?s=u:i.next=u,i=c;var p=e.alternate;p!==null&&(p=p.updateQueue,l=p.lastBaseUpdate,l!==i&&(l===null?p.firstBaseUpdate=u:l.next=u,p.lastBaseUpdate=c))}if(s!==null){var f=o.baseState;i=0,p=u=c=null,l=s;do{var d=l.lane,y=l.eventTime;if((r&d)===d){p!==null&&(p=p.next={eventTime:y,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var b=e,v=l;switch(d=t,y=n,v.tag){case 1:if(b=v.payload,typeof b=="function"){f=b.call(y,f,d);break e}f=b;break e;case 3:b.flags=b.flags&-65537|128;case 0:if(b=v.payload,d=typeof b=="function"?b.call(y,f,d):b,d==null)break e;f=Pe({},f,d);break e;case 2:Fn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,d=o.effects,d===null?o.effects=[l]:d.push(l))}else y={eventTime:y,lane:d,tag:l.tag,payload:l.payload,callback:l.callback,next:null},p===null?(u=p=y,c=f):p=p.next=y,i|=d;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;d=l,l=d.next,d.next=null,o.lastBaseUpdate=d,o.shared.pending=null}}while(!0);if(p===null&&(c=f),o.baseState=c,o.firstBaseUpdate=u,o.lastBaseUpdate=p,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);$r|=i,e.lanes=i,e.memoizedState=f}}function Ap(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(A(191,o));o.call(r)}}}var li={},un=pr(li),Bs=pr(li),Ws=pr(li);function jr(e){if(e===li)throw Error(A(174));return e}function Id(e,t){switch(ye(Ws,t),ye(Bs,e),ye(un,li),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Dc(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Dc(t,e)}Se(un),ye(un,t)}function Ho(){Se(un),Se(Bs),Se(Ws)}function _g(e){jr(Ws.current);var t=jr(un.current),n=Dc(t,e.type);t!==n&&(ye(Bs,e),ye(un,n))}function Dd(e){Bs.current===e&&(Se(un),Se(Bs))}var Ee=pr(0);function Pa(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var oc=[];function Fd(){for(var e=0;e<oc.length;e++)oc[e]._workInProgressVersionPrimary=null;oc.length=0}var ea=Pn.ReactCurrentDispatcher,sc=Pn.ReactCurrentBatchConfig,Fr=0,ke=null,De=null,ze=null,Ta=!1,Es=!1,Hs=0,Yw=0;function Xe(){throw Error(A(321))}function $d(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!qt(e[n],t[n]))return!1;return!0}function zd(e,t,n,r,o,s){if(Fr=s,ke=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ea.current=e===null||e.memoizedState===null?eb:tb,e=n(r,o),Es){s=0;do{if(Es=!1,Hs=0,25<=s)throw Error(A(301));s+=1,ze=De=null,t.updateQueue=null,ea.current=nb,e=n(r,o)}while(Es)}if(ea.current=Ra,t=De!==null&&De.next!==null,Fr=0,ze=De=ke=null,Ta=!1,t)throw Error(A(300));return e}function Ud(){var e=Hs!==0;return Hs=0,e}function tn(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ze===null?ke.memoizedState=ze=e:ze=ze.next=e,ze}function Dt(){if(De===null){var e=ke.alternate;e=e!==null?e.memoizedState:null}else e=De.next;var t=ze===null?ke.memoizedState:ze.next;if(t!==null)ze=t,De=e;else{if(e===null)throw Error(A(310));De=e,e={memoizedState:De.memoizedState,baseState:De.baseState,baseQueue:De.baseQueue,queue:De.queue,next:null},ze===null?ke.memoizedState=ze=e:ze=ze.next=e}return ze}function Vs(e,t){return typeof t=="function"?t(e):t}function ic(e){var t=Dt(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=De,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var l=i=null,c=null,u=s;do{var p=u.lane;if((Fr&p)===p)c!==null&&(c=c.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var f={lane:p,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};c===null?(l=c=f,i=r):c=c.next=f,ke.lanes|=p,$r|=p}u=u.next}while(u!==null&&u!==s);c===null?i=r:c.next=l,qt(r,t.memoizedState)||(pt=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=c,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,ke.lanes|=s,$r|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ac(e){var t=Dt(),n=t.queue;if(n===null)throw Error(A(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);qt(s,t.memoizedState)||(pt=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Og(){}function Ag(e,t){var n=ke,r=Dt(),o=t(),s=!qt(r.memoizedState,o);if(s&&(r.memoizedState=o,pt=!0),r=r.queue,Bd(Ig.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ze!==null&&ze.memoizedState.tag&1){if(n.flags|=2048,Qs(9,Mg.bind(null,n,r,o,t),void 0,null),Ue===null)throw Error(A(349));Fr&30||Lg(n,t,o)}return o}function Lg(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Mg(e,t,n,r){t.value=n,t.getSnapshot=r,Dg(t)&&Fg(e)}function Ig(e,t,n){return n(function(){Dg(t)&&Fg(e)})}function Dg(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!qt(e,n)}catch(r){return!0}}function Fg(e){var t=Cn(e,1);t!==null&&Kt(t,e,1,-1)}function Lp(e){var t=tn();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Vs,lastRenderedState:e},t.queue=e,e=e.dispatch=Jw.bind(null,ke,e),[t.memoizedState,e]}function Qs(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=ke.updateQueue,t===null?(t={lastEffect:null,stores:null},ke.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function $g(){return Dt().memoizedState}function ta(e,t,n,r){var o=tn();ke.flags|=e,o.memoizedState=Qs(1|t,n,void 0,r===void 0?null:r)}function tl(e,t,n,r){var o=Dt();r=r===void 0?null:r;var s=void 0;if(De!==null){var i=De.memoizedState;if(s=i.destroy,r!==null&&$d(r,i.deps)){o.memoizedState=Qs(t,n,s,r);return}}ke.flags|=e,o.memoizedState=Qs(1|t,n,s,r)}function Mp(e,t){return ta(8390656,8,e,t)}function Bd(e,t){return tl(2048,8,e,t)}function zg(e,t){return tl(4,2,e,t)}function Ug(e,t){return tl(4,4,e,t)}function Bg(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Wg(e,t,n){return n=n!=null?n.concat([e]):null,tl(4,4,Bg.bind(null,t,e),n)}function Wd(){}function Hg(e,t){var n=Dt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&$d(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vg(e,t){var n=Dt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&$d(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Qg(e,t,n){return Fr&21?(qt(n,t)||(n=Xm(),ke.lanes|=n,$r|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,pt=!0),e.memoizedState=n)}function Xw(e,t){var n=he;he=n!==0&&4>n?n:4,e(!0);var r=sc.transition;sc.transition={};try{e(!1),t()}finally{he=n,sc.transition=r}}function Kg(){return Dt().memoizedState}function Zw(e,t,n){var r=rr(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},qg(e))Gg(t,n);else if(n=Tg(e,t,n,r),n!==null){var o=it();Kt(n,e,r,o),Yg(n,t,r)}}function Jw(e,t,n){var r=rr(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(qg(e))Gg(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,l=s(i,n);if(o.hasEagerState=!0,o.eagerState=l,qt(l,i)){var c=t.interleaved;c===null?(o.next=o,Ld(t)):(o.next=c.next,c.next=o),t.interleaved=o;return}}catch(u){}finally{}n=Tg(e,t,o,r),n!==null&&(o=it(),Kt(n,e,r,o),Yg(n,t,r))}}function qg(e){var t=e.alternate;return e===ke||t!==null&&t===ke}function Gg(e,t){Es=Ta=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Yg(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,wd(e,n)}}var Ra={readContext:It,useCallback:Xe,useContext:Xe,useEffect:Xe,useImperativeHandle:Xe,useInsertionEffect:Xe,useLayoutEffect:Xe,useMemo:Xe,useReducer:Xe,useRef:Xe,useState:Xe,useDebugValue:Xe,useDeferredValue:Xe,useTransition:Xe,useMutableSource:Xe,useSyncExternalStore:Xe,useId:Xe,unstable_isNewReconciler:!1},eb={readContext:It,useCallback:function(e,t){return tn().memoizedState=[e,t===void 0?null:t],e},useContext:It,useEffect:Mp,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ta(4194308,4,Bg.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ta(4194308,4,e,t)},useInsertionEffect:function(e,t){return ta(4,2,e,t)},useMemo:function(e,t){var n=tn();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=tn();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=Zw.bind(null,ke,e),[r.memoizedState,e]},useRef:function(e){var t=tn();return e={current:e},t.memoizedState=e},useState:Lp,useDebugValue:Wd,useDeferredValue:function(e){return tn().memoizedState=e},useTransition:function(){var e=Lp(!1),t=e[0];return e=Xw.bind(null,e[1]),tn().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=ke,o=tn();if(je){if(n===void 0)throw Error(A(407));n=n()}else{if(n=t(),Ue===null)throw Error(A(349));Fr&30||Lg(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,Mp(Ig.bind(null,r,s,e),[e]),r.flags|=2048,Qs(9,Mg.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=tn(),t=Ue.identifierPrefix;if(je){var n=wn,r=xn;n=(r&~(1<<32-Qt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Hs++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Yw++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},tb={readContext:It,useCallback:Hg,useContext:It,useEffect:Bd,useImperativeHandle:Wg,useInsertionEffect:zg,useLayoutEffect:Ug,useMemo:Vg,useReducer:ic,useRef:$g,useState:function(){return ic(Vs)},useDebugValue:Wd,useDeferredValue:function(e){var t=Dt();return Qg(t,De.memoizedState,e)},useTransition:function(){var e=ic(Vs)[0],t=Dt().memoizedState;return[e,t]},useMutableSource:Og,useSyncExternalStore:Ag,useId:Kg,unstable_isNewReconciler:!1},nb={readContext:It,useCallback:Hg,useContext:It,useEffect:Bd,useImperativeHandle:Wg,useInsertionEffect:zg,useLayoutEffect:Ug,useMemo:Vg,useReducer:ac,useRef:$g,useState:function(){return ac(Vs)},useDebugValue:Wd,useDeferredValue:function(e){var t=Dt();return De===null?t.memoizedState=e:Qg(t,De.memoizedState,e)},useTransition:function(){var e=ac(Vs)[0],t=Dt().memoizedState;return[e,t]},useMutableSource:Og,useSyncExternalStore:Ag,useId:Kg,unstable_isNewReconciler:!1};function Ut(e,t){if(e&&e.defaultProps){t=Pe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ou(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Pe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var nl={isMounted:function(e){return(e=e._reactInternals)?Qr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=it(),o=rr(e),s=bn(r,o);s.payload=t,n!=null&&(s.callback=n),t=tr(e,s,o),t!==null&&(Kt(t,e,o,r),Ji(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=it(),o=rr(e),s=bn(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=tr(e,s,o),t!==null&&(Kt(t,e,o,r),Ji(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=it(),r=rr(e),o=bn(n,r);o.tag=2,t!=null&&(o.callback=t),t=tr(e,o,r),t!==null&&(Kt(t,e,r,n),Ji(t,e,r))}};function Ip(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!Fs(n,r)||!Fs(o,s):!0}function Xg(e,t,n){var r=!1,o=ar,s=t.contextType;return typeof s=="object"&&s!==null?s=It(s):(o=mt(t)?Ir:tt.current,r=t.contextTypes,s=(r=r!=null)?Uo(e,o):ar),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=nl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function Dp(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&nl.enqueueReplaceState(t,t.state,null)}function su(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Md(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=It(s):(s=mt(t)?Ir:tt.current,o.context=Uo(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(ou(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&nl.enqueueReplaceState(o,o.state,null),ka(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function Vo(e,t){try{var n="",r=t;do n+=R1(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function lc(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function iu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var rb=typeof WeakMap=="function"?WeakMap:Map;function Zg(e,t,n){n=bn(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Oa||(Oa=!0,gu=r),iu(e,t)},n}function Jg(e,t,n){n=bn(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){iu(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){iu(e,t),typeof r!="function"&&(nr===null?nr=new Set([this]):nr.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Fp(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new rb;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=vb.bind(null,e,t,n),t.then(e,e))}function $p(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function zp(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=bn(-1,1),t.tag=2,tr(n,t,1))),n.lanes|=1),e)}var ob=Pn.ReactCurrentOwner,pt=!1;function st(e,t,n,r){t.child=e===null?Pg(t,null,n,r):Wo(t,e.child,n,r)}function Up(e,t,n,r,o){n=n.render;var s=t.ref;return wo(t,o),r=zd(e,t,n,r,s,o),n=Ud(),e!==null&&!pt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,jn(e,t,o)):(je&&n&&Pd(t),t.flags|=1,st(e,t,r,o),t.child)}function Bp(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!Xd(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,e0(e,t,s,r,o)):(e=sa(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:Fs,n(i,r)&&e.ref===t.ref)return jn(e,t,o)}return t.flags|=1,e=or(s,r),e.ref=t.ref,e.return=t,t.child=e}function e0(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(Fs(s,r)&&e.ref===t.ref)if(pt=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(pt=!0);else return t.lanes=e.lanes,jn(e,t,o)}return au(e,t,n,r,o)}function t0(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ye(mo,xt),xt|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ye(mo,xt),xt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ye(mo,xt),xt|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ye(mo,xt),xt|=r;return st(e,t,o,n),t.child}function n0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function au(e,t,n,r,o){var s=mt(n)?Ir:tt.current;return s=Uo(t,s),wo(t,o),n=zd(e,t,n,r,s,o),r=Ud(),e!==null&&!pt?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,jn(e,t,o)):(je&&r&&Pd(t),t.flags|=1,st(e,t,n,o),t.child)}function Wp(e,t,n,r,o){if(mt(n)){var s=!0;Sa(t)}else s=!1;if(wo(t,o),t.stateNode===null)na(e,t),Xg(t,n,r),su(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,l=t.memoizedProps;i.props=l;var c=i.context,u=n.contextType;typeof u=="object"&&u!==null?u=It(u):(u=mt(n)?Ir:tt.current,u=Uo(t,u));var p=n.getDerivedStateFromProps,f=typeof p=="function"||typeof i.getSnapshotBeforeUpdate=="function";f||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==r||c!==u)&&Dp(t,i,r,u),Fn=!1;var d=t.memoizedState;i.state=d,ka(t,r,i,o),c=t.memoizedState,l!==r||d!==c||ht.current||Fn?(typeof p=="function"&&(ou(t,n,p,r),c=t.memoizedState),(l=Fn||Ip(t,n,l,r,d,c,u))?(f||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),i.props=r,i.state=c,i.context=u,r=l):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Rg(e,t),l=t.memoizedProps,u=t.type===t.elementType?l:Ut(t.type,l),i.props=u,f=t.pendingProps,d=i.context,c=n.contextType,typeof c=="object"&&c!==null?c=It(c):(c=mt(n)?Ir:tt.current,c=Uo(t,c));var y=n.getDerivedStateFromProps;(p=typeof y=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==f||d!==c)&&Dp(t,i,r,c),Fn=!1,d=t.memoizedState,i.state=d,ka(t,r,i,o);var b=t.memoizedState;l!==f||d!==b||ht.current||Fn?(typeof y=="function"&&(ou(t,n,y,r),b=t.memoizedState),(u=Fn||Ip(t,n,u,r,d,b,c)||!1)?(p||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,b,c),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,b,c)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=b),i.props=r,i.state=b,i.context=c,r=u):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&d===e.memoizedState||(t.flags|=1024),r=!1)}return lu(e,t,n,r,s,o)}function lu(e,t,n,r,o,s){n0(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&Pp(t,n,!1),jn(e,t,s);r=t.stateNode,ob.current=t;var l=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=Wo(t,e.child,null,s),t.child=Wo(t,null,l,s)):st(e,t,l,s),t.memoizedState=r.state,o&&Pp(t,n,!0),t.child}function r0(e){var t=e.stateNode;t.pendingContext?kp(e,t.pendingContext,t.pendingContext!==t.context):t.context&&kp(e,t.context,!1),Id(e,t.containerInfo)}function Hp(e,t,n,r,o){return Bo(),Rd(o),t.flags|=256,st(e,t,n,r),t.child}var cu={dehydrated:null,treeContext:null,retryLane:0};function uu(e){return{baseLanes:e,cachePool:null,transitions:null}}function o0(e,t,n){var r=t.pendingProps,o=Ee.current,s=!1,i=(t.flags&128)!==0,l;if((l=i)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ye(Ee,o&1),e===null)return nu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=sl(i,r,0,null),e=Mr(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=uu(n),t.memoizedState=cu,e):Hd(t,i));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return sb(e,t,i,r,l,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,l=o.sibling;var c={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=c,t.deletions=null):(r=or(o,c),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?s=or(l,s):(s=Mr(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?uu(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=cu,r}return s=e.child,e=s.sibling,r=or(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Hd(e,t){return t=sl({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ai(e,t,n,r){return r!==null&&Rd(r),Wo(t,e.child,null,n),e=Hd(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function sb(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=lc(Error(A(422))),Ai(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=sl({mode:"visible",children:r.children},o,0,null),s=Mr(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Wo(t,e.child,null,i),t.child.memoizedState=uu(i),t.memoizedState=cu,s);if(!(t.mode&1))return Ai(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(A(419)),r=lc(s,r,void 0),Ai(e,t,i,r)}if(l=(i&e.childLanes)!==0,pt||l){if(r=Ue,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,Cn(e,o),Kt(r,e,o,-1))}return Yd(),r=lc(Error(A(421))),Ai(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=yb.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,bt=er(o.nextSibling),Nt=t,je=!0,Ht=null,e!==null&&(Ot[At++]=xn,Ot[At++]=wn,Ot[At++]=Dr,xn=e.id,wn=e.overflow,Dr=t),t=Hd(t,r.children),t.flags|=4096,t)}function Vp(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),ru(e.return,t,n)}function cc(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function s0(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(st(e,t,r.children,n),r=Ee.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Vp(e,n,t);else if(e.tag===19)Vp(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ye(Ee,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Pa(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),cc(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Pa(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}cc(t,!0,n,null,s);break;case"together":cc(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function na(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function jn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),$r|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(A(153));if(t.child!==null){for(e=t.child,n=or(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=or(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function ib(e,t,n){switch(t.tag){case 3:r0(t),Bo();break;case 5:_g(t);break;case 1:mt(t.type)&&Sa(t);break;case 4:Id(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ye(ja,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ye(Ee,Ee.current&1),t.flags|=128,null):n&t.child.childLanes?o0(e,t,n):(ye(Ee,Ee.current&1),e=jn(e,t,n),e!==null?e.sibling:null);ye(Ee,Ee.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return s0(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ye(Ee,Ee.current),r)break;return null;case 22:case 23:return t.lanes=0,t0(e,t,n)}return jn(e,t,n)}var i0,du,a0,l0;i0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};du=function(){};a0=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,jr(un.current);var s=null;switch(n){case"input":o=Ac(e,o),r=Ac(e,r),s=[];break;case"select":o=Pe({},o,{value:void 0}),r=Pe({},r,{value:void 0}),s=[];break;case"textarea":o=Ic(e,o),r=Ic(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=wa)}Fc(n,r);var i;n=null;for(u in o)if(!r.hasOwnProperty(u)&&o.hasOwnProperty(u)&&o[u]!=null)if(u==="style"){var l=o[u];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(_s.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var c=r[u];if(l=o!=null?o[u]:void 0,r.hasOwnProperty(u)&&c!==l&&(c!=null||l!=null))if(u==="style")if(l){for(i in l)!l.hasOwnProperty(i)||c&&c.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in c)c.hasOwnProperty(i)&&l[i]!==c[i]&&(n||(n={}),n[i]=c[i])}else n||(s||(s=[]),s.push(u,n)),n=c;else u==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,l=l?l.__html:void 0,c!=null&&l!==c&&(s=s||[]).push(u,c)):u==="children"?typeof c!="string"&&typeof c!="number"||(s=s||[]).push(u,""+c):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(_s.hasOwnProperty(u)?(c!=null&&u==="onScroll"&&be("scroll",e),s||l===c||(s=[])):(s=s||[]).push(u,c))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};l0=function(e,t,n,r){n!==r&&(t.flags|=4)};function ds(e,t){if(!je)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ze(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ab(e,t,n){var r=t.pendingProps;switch(Td(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ze(t),null;case 1:return mt(t.type)&&ba(),Ze(t),null;case 3:return r=t.stateNode,Ho(),Se(ht),Se(tt),Fd(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(_i(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Ht!==null&&(xu(Ht),Ht=null))),du(e,t),Ze(t),null;case 5:Dd(t);var o=jr(Ws.current);if(n=t.type,e!==null&&t.stateNode!=null)a0(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(A(166));return Ze(t),null}if(e=jr(un.current),_i(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[an]=t,r[Us]=s,e=(t.mode&1)!==0,n){case"dialog":be("cancel",r),be("close",r);break;case"iframe":case"object":case"embed":be("load",r);break;case"video":case"audio":for(o=0;o<ys.length;o++)be(ys[o],r);break;case"source":be("error",r);break;case"img":case"image":case"link":be("error",r),be("load",r);break;case"details":be("toggle",r);break;case"input":ep(r,s),be("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},be("invalid",r);break;case"textarea":np(r,s),be("invalid",r)}Fc(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&Ri(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&Ri(r.textContent,l,e),o=["children",""+l]):_s.hasOwnProperty(i)&&l!=null&&i==="onScroll"&&be("scroll",r)}switch(n){case"input":Si(r),tp(r,s,!0);break;case"textarea":Si(r),rp(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=wa)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Im(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[an]=t,e[Us]=r,i0(e,t,!1,!1),t.stateNode=e;e:{switch(i=$c(n,r),n){case"dialog":be("cancel",e),be("close",e),o=r;break;case"iframe":case"object":case"embed":be("load",e),o=r;break;case"video":case"audio":for(o=0;o<ys.length;o++)be(ys[o],e);o=r;break;case"source":be("error",e),o=r;break;case"img":case"image":case"link":be("error",e),be("load",e),o=r;break;case"details":be("toggle",e),o=r;break;case"input":ep(e,r),o=Ac(e,r),be("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=Pe({},r,{value:void 0}),be("invalid",e);break;case"textarea":np(e,r),o=Ic(e,r),be("invalid",e);break;default:o=r}Fc(n,o),l=o;for(s in l)if(l.hasOwnProperty(s)){var c=l[s];s==="style"?$m(e,c):s==="dangerouslySetInnerHTML"?(c=c?c.__html:void 0,c!=null&&Dm(e,c)):s==="children"?typeof c=="string"?(n!=="textarea"||c!=="")&&Os(e,c):typeof c=="number"&&Os(e,""+c):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(_s.hasOwnProperty(s)?c!=null&&s==="onScroll"&&be("scroll",e):c!=null&&hd(e,s,c,i))}switch(n){case"input":Si(e),tp(e,r,!1);break;case"textarea":Si(e),rp(e);break;case"option":r.value!=null&&e.setAttribute("value",""+ir(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?go(e,!!r.multiple,s,!1):r.defaultValue!=null&&go(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=wa)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Ze(t),null;case 6:if(e&&t.stateNode!=null)l0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(A(166));if(n=jr(Ws.current),jr(un.current),_i(t)){if(r=t.stateNode,n=t.memoizedProps,r[an]=t,(s=r.nodeValue!==n)&&(e=Nt,e!==null))switch(e.tag){case 3:Ri(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ri(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[an]=t,t.stateNode=r}return Ze(t),null;case 13:if(Se(Ee),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(je&&bt!==null&&t.mode&1&&!(t.flags&128))Eg(),Bo(),t.flags|=98560,s=!1;else if(s=_i(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(A(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(A(317));s[an]=t}else Bo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Ze(t),s=!1}else Ht!==null&&(xu(Ht),Ht=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||Ee.current&1?Fe===0&&(Fe=3):Yd())),t.updateQueue!==null&&(t.flags|=4),Ze(t),null);case 4:return Ho(),du(e,t),e===null&&$s(t.stateNode.containerInfo),Ze(t),null;case 10:return Ad(t.type._context),Ze(t),null;case 17:return mt(t.type)&&ba(),Ze(t),null;case 19:if(Se(Ee),s=t.memoizedState,s===null)return Ze(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)ds(s,!1);else{if(Fe!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Pa(e),i!==null){for(t.flags|=128,ds(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ye(Ee,Ee.current&1|2),t.child}e=e.sibling}s.tail!==null&&Oe()>Qo&&(t.flags|=128,r=!0,ds(s,!1),t.lanes=4194304)}else{if(!r)if(e=Pa(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),ds(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!je)return Ze(t),null}else 2*Oe()-s.renderingStartTime>Qo&&n!==1073741824&&(t.flags|=128,r=!0,ds(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=Oe(),t.sibling=null,n=Ee.current,ye(Ee,r?n&1|2:n&1),t):(Ze(t),null);case 22:case 23:return Gd(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?xt&1073741824&&(Ze(t),t.subtreeFlags&6&&(t.flags|=8192)):Ze(t),null;case 24:return null;case 25:return null}throw Error(A(156,t.tag))}function lb(e,t){switch(Td(t),t.tag){case 1:return mt(t.type)&&ba(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Ho(),Se(ht),Se(tt),Fd(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Dd(t),null;case 13:if(Se(Ee),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(A(340));Bo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Se(Ee),null;case 4:return Ho(),null;case 10:return Ad(t.type._context),null;case 22:case 23:return Gd(),null;case 24:return null;default:return null}}var Li=!1,et=!1,cb=typeof WeakSet=="function"?WeakSet:Set,W=null;function ho(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){_e(e,t,r)}else n.current=null}function fu(e,t,n){try{n()}catch(r){_e(e,t,r)}}var Qp=!1;function ub(e,t){if(Gc=va,e=pg(),kd(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch(S){n=null;break e}var i=0,l=-1,c=-1,u=0,p=0,f=e,d=null;t:for(;;){for(var y;f!==n||o!==0&&f.nodeType!==3||(l=i+o),f!==s||r!==0&&f.nodeType!==3||(c=i+r),f.nodeType===3&&(i+=f.nodeValue.length),(y=f.firstChild)!==null;)d=f,f=y;for(;;){if(f===e)break t;if(d===n&&++u===o&&(l=i),d===s&&++p===r&&(c=i),(y=f.nextSibling)!==null)break;f=d,d=f.parentNode}f=y}n=l===-1||c===-1?null:{start:l,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Yc={focusedElem:e,selectionRange:n},va=!1,W=t;W!==null;)if(t=W,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,W=e;else for(;W!==null;){t=W;try{var b=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(b!==null){var v=b.memoizedProps,w=b.memoizedState,m=t.stateNode,g=m.getSnapshotBeforeUpdate(t.elementType===t.type?v:Ut(t.type,v),w);m.__reactInternalSnapshotBeforeUpdate=g}break;case 3:var x=t.stateNode.containerInfo;x.nodeType===1?x.textContent="":x.nodeType===9&&x.documentElement&&x.removeChild(x.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(A(163))}}catch(S){_e(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,W=e;break}W=t.return}return b=Qp,Qp=!1,b}function ks(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&fu(t,n,s)}o=o.next}while(o!==r)}}function rl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function pu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function c0(e){var t=e.alternate;t!==null&&(e.alternate=null,c0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[an],delete t[Us],delete t[Jc],delete t[Qw],delete t[Kw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function u0(e){return e.tag===5||e.tag===3||e.tag===4}function Kp(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||u0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function hu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=wa));else if(r!==4&&(e=e.child,e!==null))for(hu(e,t,n),e=e.sibling;e!==null;)hu(e,t,n),e=e.sibling}function mu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(mu(e,t,n),e=e.sibling;e!==null;)mu(e,t,n),e=e.sibling}var We=null,Wt=!1;function On(e,t,n){for(n=n.child;n!==null;)d0(e,t,n),n=n.sibling}function d0(e,t,n){if(cn&&typeof cn.onCommitFiberUnmount=="function")try{cn.onCommitFiberUnmount(Ga,n)}catch(l){}switch(n.tag){case 5:et||ho(n,t);case 6:var r=We,o=Wt;We=null,On(e,t,n),We=r,Wt=o,We!==null&&(Wt?(e=We,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):We.removeChild(n.stateNode));break;case 18:We!==null&&(Wt?(e=We,n=n.stateNode,e.nodeType===8?nc(e.parentNode,n):e.nodeType===1&&nc(e,n),Is(e)):nc(We,n.stateNode));break;case 4:r=We,o=Wt,We=n.stateNode.containerInfo,Wt=!0,On(e,t,n),We=r,Wt=o;break;case 0:case 11:case 14:case 15:if(!et&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&fu(n,t,i),o=o.next}while(o!==r)}On(e,t,n);break;case 1:if(!et&&(ho(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){_e(n,t,l)}On(e,t,n);break;case 21:On(e,t,n);break;case 22:n.mode&1?(et=(r=et)||n.memoizedState!==null,On(e,t,n),et=r):On(e,t,n);break;default:On(e,t,n)}}function qp(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new cb),t.forEach(function(r){var o=xb.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function Ft(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,l=i;e:for(;l!==null;){switch(l.tag){case 5:We=l.stateNode,Wt=!1;break e;case 3:We=l.stateNode.containerInfo,Wt=!0;break e;case 4:We=l.stateNode.containerInfo,Wt=!0;break e}l=l.return}if(We===null)throw Error(A(160));d0(s,i,o),We=null,Wt=!1;var c=o.alternate;c!==null&&(c.return=null),o.return=null}catch(u){_e(o,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)f0(t,e),t=t.sibling}function f0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Ft(t,e),en(e),r&4){try{ks(3,e,e.return),rl(3,e)}catch(v){_e(e,e.return,v)}try{ks(5,e,e.return)}catch(v){_e(e,e.return,v)}}break;case 1:Ft(t,e),en(e),r&512&&n!==null&&ho(n,n.return);break;case 5:if(Ft(t,e),en(e),r&512&&n!==null&&ho(n,n.return),e.flags&32){var o=e.stateNode;try{Os(o,"")}catch(v){_e(e,e.return,v)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,l=e.type,c=e.updateQueue;if(e.updateQueue=null,c!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&Lm(o,s),$c(l,i);var u=$c(l,s);for(i=0;i<c.length;i+=2){var p=c[i],f=c[i+1];p==="style"?$m(o,f):p==="dangerouslySetInnerHTML"?Dm(o,f):p==="children"?Os(o,f):hd(o,p,f,u)}switch(l){case"input":Lc(o,s);break;case"textarea":Mm(o,s);break;case"select":var d=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var y=s.value;y!=null?go(o,!!s.multiple,y,!1):d!==!!s.multiple&&(s.defaultValue!=null?go(o,!!s.multiple,s.defaultValue,!0):go(o,!!s.multiple,s.multiple?[]:"",!1))}o[Us]=s}catch(v){_e(e,e.return,v)}}break;case 6:if(Ft(t,e),en(e),r&4){if(e.stateNode===null)throw Error(A(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(v){_e(e,e.return,v)}}break;case 3:if(Ft(t,e),en(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{Is(t.containerInfo)}catch(v){_e(e,e.return,v)}break;case 4:Ft(t,e),en(e);break;case 13:Ft(t,e),en(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Kd=Oe())),r&4&&qp(e);break;case 22:if(p=n!==null&&n.memoizedState!==null,e.mode&1?(et=(u=et)||p,Ft(t,e),et=u):Ft(t,e),en(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!p&&e.mode&1)for(W=e,p=e.child;p!==null;){for(f=W=p;W!==null;){switch(d=W,y=d.child,d.tag){case 0:case 11:case 14:case 15:ks(4,d,d.return);break;case 1:ho(d,d.return);var b=d.stateNode;if(typeof b.componentWillUnmount=="function"){r=d,n=d.return;try{t=r,b.props=t.memoizedProps,b.state=t.memoizedState,b.componentWillUnmount()}catch(v){_e(r,n,v)}}break;case 5:ho(d,d.return);break;case 22:if(d.memoizedState!==null){Yp(f);continue}}y!==null?(y.return=d,W=y):Yp(f)}p=p.sibling}e:for(p=null,f=e;;){if(f.tag===5){if(p===null){p=f;try{o=f.stateNode,u?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=f.stateNode,c=f.memoizedProps.style,i=c!=null&&c.hasOwnProperty("display")?c.display:null,l.style.display=Fm("display",i))}catch(v){_e(e,e.return,v)}}}else if(f.tag===6){if(p===null)try{f.stateNode.nodeValue=u?"":f.memoizedProps}catch(v){_e(e,e.return,v)}}else if((f.tag!==22&&f.tag!==23||f.memoizedState===null||f===e)&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;f.sibling===null;){if(f.return===null||f.return===e)break e;p===f&&(p=null),f=f.return}p===f&&(p=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:Ft(t,e),en(e),r&4&&qp(e);break;case 21:break;default:Ft(t,e),en(e)}}function en(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(u0(n)){var r=n;break e}n=n.return}throw Error(A(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(Os(o,""),r.flags&=-33);var s=Kp(e);mu(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,l=Kp(e);hu(e,l,i);break;default:throw Error(A(161))}}catch(c){_e(e,e.return,c)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function db(e,t,n){W=e,p0(e)}function p0(e,t,n){for(var r=(e.mode&1)!==0;W!==null;){var o=W,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||Li;if(!i){var l=o.alternate,c=l!==null&&l.memoizedState!==null||et;l=Li;var u=et;if(Li=i,(et=c)&&!u)for(W=o;W!==null;)i=W,c=i.child,i.tag===22&&i.memoizedState!==null?Xp(o):c!==null?(c.return=i,W=c):Xp(o);for(;s!==null;)W=s,p0(s),s=s.sibling;W=o,Li=l,et=u}Gp(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,W=s):Gp(e)}}function Gp(e){for(;W!==null;){var t=W;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:et||rl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!et)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ut(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Ap(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Ap(t,i,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var c=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":c.autoFocus&&n.focus();break;case"img":c.src&&(n.src=c.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var p=u.memoizedState;if(p!==null){var f=p.dehydrated;f!==null&&Is(f)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(A(163))}et||t.flags&512&&pu(t)}catch(d){_e(t,t.return,d)}}if(t===e){W=null;break}if(n=t.sibling,n!==null){n.return=t.return,W=n;break}W=t.return}}function Yp(e){for(;W!==null;){var t=W;if(t===e){W=null;break}var n=t.sibling;if(n!==null){n.return=t.return,W=n;break}W=t.return}}function Xp(e){for(;W!==null;){var t=W;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(c){_e(t,n,c)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(c){_e(t,o,c)}}var s=t.return;try{pu(t)}catch(c){_e(t,s,c)}break;case 5:var i=t.return;try{pu(t)}catch(c){_e(t,i,c)}}}catch(c){_e(t,t.return,c)}if(t===e){W=null;break}var l=t.sibling;if(l!==null){l.return=t.return,W=l;break}W=t.return}}var fb=Math.ceil,_a=Pn.ReactCurrentDispatcher,Vd=Pn.ReactCurrentOwner,Mt=Pn.ReactCurrentBatchConfig,ae=0,Ue=null,Me=null,Qe=0,xt=0,mo=pr(0),Fe=0,Ks=null,$r=0,ol=0,Qd=0,Ps=null,ft=null,Kd=0,Qo=1/0,vn=null,Oa=!1,gu=null,nr=null,Mi=!1,Gn=null,Aa=0,Ts=0,vu=null,ra=-1,oa=0;function it(){return ae&6?Oe():ra!==-1?ra:ra=Oe()}function rr(e){return e.mode&1?ae&2&&Qe!==0?Qe&-Qe:Gw.transition!==null?(oa===0&&(oa=Xm()),oa):(e=he,e!==0||(e=window.event,e=e===void 0?16:og(e.type)),e):1}function Kt(e,t,n,r){if(50<Ts)throw Ts=0,vu=null,Error(A(185));si(e,n,r),(!(ae&2)||e!==Ue)&&(e===Ue&&(!(ae&2)&&(ol|=n),Fe===4&&zn(e,Qe)),gt(e,r),n===1&&ae===0&&!(t.mode&1)&&(Qo=Oe()+500,el&&hr()))}function gt(e,t){var n=e.callbackNode;G1(e,t);var r=ga(e,e===Ue?Qe:0);if(r===0)n!==null&&ip(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&ip(n),t===1)e.tag===0?qw(Zp.bind(null,e)):Ng(Zp.bind(null,e)),Hw(function(){!(ae&6)&&hr()}),n=null;else{switch(Zm(r)){case 1:n=xd;break;case 4:n=Gm;break;case 16:n=ma;break;case 536870912:n=Ym;break;default:n=ma}n=b0(n,h0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function h0(e,t){if(ra=-1,oa=0,ae&6)throw Error(A(327));var n=e.callbackNode;if(bo()&&e.callbackNode!==n)return null;var r=ga(e,e===Ue?Qe:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=La(e,r);else{t=r;var o=ae;ae|=2;var s=g0();(Ue!==e||Qe!==t)&&(vn=null,Qo=Oe()+500,Lr(e,t));do try{mb();break}catch(l){m0(e,l)}while(!0);Od(),_a.current=s,ae=o,Me!==null?t=0:(Ue=null,Qe=0,t=Fe)}if(t!==0){if(t===2&&(o=Hc(e),o!==0&&(r=o,t=yu(e,o))),t===1)throw n=Ks,Lr(e,0),zn(e,r),gt(e,Oe()),n;if(t===6)zn(e,r);else{if(o=e.current.alternate,!(r&30)&&!pb(o)&&(t=La(e,r),t===2&&(s=Hc(e),s!==0&&(r=s,t=yu(e,s))),t===1))throw n=Ks,Lr(e,0),zn(e,r),gt(e,Oe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(A(345));case 2:br(e,ft,vn);break;case 3:if(zn(e,r),(r&130023424)===r&&(t=Kd+500-Oe(),10<t)){if(ga(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){it(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=Zc(br.bind(null,e,ft,vn),t);break}br(e,ft,vn);break;case 4:if(zn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Qt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=Oe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*fb(r/1960))-r,10<r){e.timeoutHandle=Zc(br.bind(null,e,ft,vn),r);break}br(e,ft,vn);break;case 5:br(e,ft,vn);break;default:throw Error(A(329))}}}return gt(e,Oe()),e.callbackNode===n?h0.bind(null,e):null}function yu(e,t){var n=Ps;return e.current.memoizedState.isDehydrated&&(Lr(e,t).flags|=256),e=La(e,t),e!==2&&(t=ft,ft=n,t!==null&&xu(t)),e}function xu(e){ft===null?ft=e:ft.push.apply(ft,e)}function pb(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!qt(s(),o))return!1}catch(i){return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function zn(e,t){for(t&=~Qd,t&=~ol,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Qt(t),r=1<<n;e[n]=-1,t&=~r}}function Zp(e){if(ae&6)throw Error(A(327));bo();var t=ga(e,0);if(!(t&1))return gt(e,Oe()),null;var n=La(e,t);if(e.tag!==0&&n===2){var r=Hc(e);r!==0&&(t=r,n=yu(e,r))}if(n===1)throw n=Ks,Lr(e,0),zn(e,t),gt(e,Oe()),n;if(n===6)throw Error(A(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,br(e,ft,vn),gt(e,Oe()),null}function qd(e,t){var n=ae;ae|=1;try{return e(t)}finally{ae=n,ae===0&&(Qo=Oe()+500,el&&hr())}}function zr(e){Gn!==null&&Gn.tag===0&&!(ae&6)&&bo();var t=ae;ae|=1;var n=Mt.transition,r=he;try{if(Mt.transition=null,he=1,e)return e()}finally{he=r,Mt.transition=n,ae=t,!(ae&6)&&hr()}}function Gd(){xt=mo.current,Se(mo)}function Lr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Ww(n)),Me!==null)for(n=Me.return;n!==null;){var r=n;switch(Td(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&ba();break;case 3:Ho(),Se(ht),Se(tt),Fd();break;case 5:Dd(r);break;case 4:Ho();break;case 13:Se(Ee);break;case 19:Se(Ee);break;case 10:Ad(r.type._context);break;case 22:case 23:Gd()}n=n.return}if(Ue=e,Me=e=or(e.current,null),Qe=xt=t,Fe=0,Ks=null,Qd=ol=$r=0,ft=Ps=null,Cr!==null){for(t=0;t<Cr.length;t++)if(n=Cr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}Cr=null}return e}function m0(e,t){do{var n=Me;try{if(Od(),ea.current=Ra,Ta){for(var r=ke.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}Ta=!1}if(Fr=0,ze=De=ke=null,Es=!1,Hs=0,Vd.current=null,n===null||n.return===null){Fe=1,Ks=t,Me=null;break}e:{var s=e,i=n.return,l=n,c=t;if(t=Qe,l.flags|=32768,c!==null&&typeof c=="object"&&typeof c.then=="function"){var u=c,p=l,f=p.tag;if(!(p.mode&1)&&(f===0||f===11||f===15)){var d=p.alternate;d?(p.updateQueue=d.updateQueue,p.memoizedState=d.memoizedState,p.lanes=d.lanes):(p.updateQueue=null,p.memoizedState=null)}var y=$p(i);if(y!==null){y.flags&=-257,zp(y,i,l,s,t),y.mode&1&&Fp(s,u,t),t=y,c=u;var b=t.updateQueue;if(b===null){var v=new Set;v.add(c),t.updateQueue=v}else b.add(c);break e}else{if(!(t&1)){Fp(s,u,t),Yd();break e}c=Error(A(426))}}else if(je&&l.mode&1){var w=$p(i);if(w!==null){!(w.flags&65536)&&(w.flags|=256),zp(w,i,l,s,t),Rd(Vo(c,l));break e}}s=c=Vo(c,l),Fe!==4&&(Fe=2),Ps===null?Ps=[s]:Ps.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var m=Zg(s,c,t);Op(s,m);break e;case 1:l=c;var g=s.type,x=s.stateNode;if(!(s.flags&128)&&(typeof g.getDerivedStateFromError=="function"||x!==null&&typeof x.componentDidCatch=="function"&&(nr===null||!nr.has(x)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=Jg(s,l,t);Op(s,S);break e}}s=s.return}while(s!==null)}y0(n)}catch(C){t=C,Me===n&&n!==null&&(Me=n=n.return);continue}break}while(!0)}function g0(){var e=_a.current;return _a.current=Ra,e===null?Ra:e}function Yd(){(Fe===0||Fe===3||Fe===2)&&(Fe=4),Ue===null||!($r&268435455)&&!(ol&268435455)||zn(Ue,Qe)}function La(e,t){var n=ae;ae|=2;var r=g0();(Ue!==e||Qe!==t)&&(vn=null,Lr(e,t));do try{hb();break}catch(o){m0(e,o)}while(!0);if(Od(),ae=n,_a.current=r,Me!==null)throw Error(A(261));return Ue=null,Qe=0,Fe}function hb(){for(;Me!==null;)v0(Me)}function mb(){for(;Me!==null&&!z1();)v0(Me)}function v0(e){var t=w0(e.alternate,e,xt);e.memoizedProps=e.pendingProps,t===null?y0(e):Me=t,Vd.current=null}function y0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=lb(n,t),n!==null){n.flags&=32767,Me=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Fe=6,Me=null;return}}else if(n=ab(n,t,xt),n!==null){Me=n;return}if(t=t.sibling,t!==null){Me=t;return}Me=t=e}while(t!==null);Fe===0&&(Fe=5)}function br(e,t,n){var r=he,o=Mt.transition;try{Mt.transition=null,he=1,gb(e,t,n,r)}finally{Mt.transition=o,he=r}return null}function gb(e,t,n,r){do bo();while(Gn!==null);if(ae&6)throw Error(A(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(A(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Y1(e,s),e===Ue&&(Me=Ue=null,Qe=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Mi||(Mi=!0,b0(ma,function(){return bo(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=Mt.transition,Mt.transition=null;var i=he;he=1;var l=ae;ae|=4,Vd.current=null,ub(e,n),f0(n,e),Iw(Yc),va=!!Gc,Yc=Gc=null,e.current=n,db(n),U1(),ae=l,he=i,Mt.transition=s}else e.current=n;if(Mi&&(Mi=!1,Gn=e,Aa=o),s=e.pendingLanes,s===0&&(nr=null),H1(n.stateNode),gt(e,Oe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Oa)throw Oa=!1,e=gu,gu=null,e;return Aa&1&&e.tag!==0&&bo(),s=e.pendingLanes,s&1?e===vu?Ts++:(Ts=0,vu=e):Ts=0,hr(),null}function bo(){if(Gn!==null){var e=Zm(Aa),t=Mt.transition,n=he;try{if(Mt.transition=null,he=16>e?16:e,Gn===null)var r=!1;else{if(e=Gn,Gn=null,Aa=0,ae&6)throw Error(A(331));var o=ae;for(ae|=4,W=e.current;W!==null;){var s=W,i=s.child;if(W.flags&16){var l=s.deletions;if(l!==null){for(var c=0;c<l.length;c++){var u=l[c];for(W=u;W!==null;){var p=W;switch(p.tag){case 0:case 11:case 15:ks(8,p,s)}var f=p.child;if(f!==null)f.return=p,W=f;else for(;W!==null;){p=W;var d=p.sibling,y=p.return;if(c0(p),p===u){W=null;break}if(d!==null){d.return=y,W=d;break}W=y}}}var b=s.alternate;if(b!==null){var v=b.child;if(v!==null){b.child=null;do{var w=v.sibling;v.sibling=null,v=w}while(v!==null)}}W=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,W=i;else e:for(;W!==null;){if(s=W,s.flags&2048)switch(s.tag){case 0:case 11:case 15:ks(9,s,s.return)}var m=s.sibling;if(m!==null){m.return=s.return,W=m;break e}W=s.return}}var g=e.current;for(W=g;W!==null;){i=W;var x=i.child;if(i.subtreeFlags&2064&&x!==null)x.return=i,W=x;else e:for(i=g;W!==null;){if(l=W,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(C){_e(l,l.return,C)}if(l===i){W=null;break e}var S=l.sibling;if(S!==null){S.return=l.return,W=S;break e}W=l.return}}if(ae=o,hr(),cn&&typeof cn.onPostCommitFiberRoot=="function")try{cn.onPostCommitFiberRoot(Ga,e)}catch(C){}r=!0}return r}finally{he=n,Mt.transition=t}}return!1}function Jp(e,t,n){t=Vo(n,t),t=Zg(e,t,1),e=tr(e,t,1),t=it(),e!==null&&(si(e,1,t),gt(e,t))}function _e(e,t,n){if(e.tag===3)Jp(e,e,n);else for(;t!==null;){if(t.tag===3){Jp(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(nr===null||!nr.has(r))){e=Vo(n,e),e=Jg(t,e,1),t=tr(t,e,1),e=it(),t!==null&&(si(t,1,e),gt(t,e));break}}t=t.return}}function vb(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=it(),e.pingedLanes|=e.suspendedLanes&n,Ue===e&&(Qe&n)===n&&(Fe===4||Fe===3&&(Qe&130023424)===Qe&&500>Oe()-Kd?Lr(e,0):Qd|=n),gt(e,t)}function x0(e,t){t===0&&(e.mode&1?(t=ji,ji<<=1,!(ji&130023424)&&(ji=4194304)):t=1);var n=it();e=Cn(e,t),e!==null&&(si(e,t,n),gt(e,n))}function yb(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),x0(e,n)}function xb(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(A(314))}r!==null&&r.delete(t),x0(e,n)}var w0;w0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||ht.current)pt=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return pt=!1,ib(e,t,n);pt=!!(e.flags&131072)}else pt=!1,je&&t.flags&1048576&&Cg(t,Ca,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;na(e,t),e=t.pendingProps;var o=Uo(t,tt.current);wo(t,n),o=zd(null,t,r,e,o,n);var s=Ud();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,mt(r)?(s=!0,Sa(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Md(t),o.updater=nl,t.stateNode=o,o._reactInternals=t,su(t,r,e,n),t=lu(null,t,r,!0,s,n)):(t.tag=0,je&&s&&Pd(t),st(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(na(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=bb(r),e=Ut(r,e),o){case 0:t=au(null,t,r,e,n);break e;case 1:t=Wp(null,t,r,e,n);break e;case 11:t=Up(null,t,r,e,n);break e;case 14:t=Bp(null,t,r,Ut(r.type,e),n);break e}throw Error(A(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ut(r,o),au(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ut(r,o),Wp(e,t,r,o,n);case 3:e:{if(r0(t),e===null)throw Error(A(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Rg(e,t),ka(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=Vo(Error(A(423)),t),t=Hp(e,t,r,n,o);break e}else if(r!==o){o=Vo(Error(A(424)),t),t=Hp(e,t,r,n,o);break e}else for(bt=er(t.stateNode.containerInfo.firstChild),Nt=t,je=!0,Ht=null,n=Pg(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Bo(),r===o){t=jn(e,t,n);break e}st(e,t,r,n)}t=t.child}return t;case 5:return _g(t),e===null&&nu(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,Xc(r,o)?i=null:s!==null&&Xc(r,s)&&(t.flags|=32),n0(e,t),st(e,t,i,n),t.child;case 6:return e===null&&nu(t),null;case 13:return o0(e,t,n);case 4:return Id(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Wo(t,null,r,n):st(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ut(r,o),Up(e,t,r,o,n);case 7:return st(e,t,t.pendingProps,n),t.child;case 8:return st(e,t,t.pendingProps.children,n),t.child;case 12:return st(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ye(ja,r._currentValue),r._currentValue=i,s!==null)if(qt(s.value,i)){if(s.children===o.children&&!ht.current){t=jn(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){i=s.child;for(var c=l.firstContext;c!==null;){if(c.context===r){if(s.tag===1){c=bn(-1,n&-n),c.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var p=u.pending;p===null?c.next=c:(c.next=p.next,p.next=c),u.pending=c}}s.lanes|=n,c=s.alternate,c!==null&&(c.lanes|=n),ru(s.return,n,t),l.lanes|=n;break}c=c.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(A(341));i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),ru(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}st(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,wo(t,n),o=It(o),r=r(o),t.flags|=1,st(e,t,r,n),t.child;case 14:return r=t.type,o=Ut(r,t.pendingProps),o=Ut(r.type,o),Bp(e,t,r,o,n);case 15:return e0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ut(r,o),na(e,t),t.tag=1,mt(r)?(e=!0,Sa(t)):e=!1,wo(t,n),Xg(t,r,o),su(t,r,o,n),lu(null,t,r,!0,e,n);case 19:return s0(e,t,n);case 22:return t0(e,t,n)}throw Error(A(156,t.tag))};function b0(e,t){return qm(e,t)}function wb(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Lt(e,t,n,r){return new wb(e,t,n,r)}function Xd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function bb(e){if(typeof e=="function")return Xd(e)?1:0;if(e!=null){if(e=e.$$typeof,e===gd)return 11;if(e===vd)return 14}return 2}function or(e,t){var n=e.alternate;return n===null?(n=Lt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function sa(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")Xd(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case oo:return Mr(n.children,o,s,t);case md:i=8,o|=8;break;case Tc:return e=Lt(12,n,t,o|2),e.elementType=Tc,e.lanes=s,e;case Rc:return e=Lt(13,n,t,o),e.elementType=Rc,e.lanes=s,e;case _c:return e=Lt(19,n,t,o),e.elementType=_c,e.lanes=s,e;case _m:return sl(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Tm:i=10;break e;case Rm:i=9;break e;case gd:i=11;break e;case vd:i=14;break e;case Dn:i=16,r=null;break e}throw Error(A(130,e==null?e:typeof e,""))}return t=Lt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function Mr(e,t,n,r){return e=Lt(7,e,r,t),e.lanes=n,e}function sl(e,t,n,r){return e=Lt(22,e,r,t),e.elementType=_m,e.lanes=n,e.stateNode={isHidden:!1},e}function uc(e,t,n){return e=Lt(6,e,null,t),e.lanes=n,e}function dc(e,t,n){return t=Lt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Sb(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Vl(0),this.expirationTimes=Vl(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Vl(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Zd(e,t,n,r,o,s,i,l,c){return e=new Sb(e,t,n,l,c),t===1?(t=1,s===!0&&(t|=8)):t=0,s=Lt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Md(s),e}function Nb(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:ro,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function S0(e){if(!e)return ar;e=e._reactInternals;e:{if(Qr(e)!==e||e.tag!==1)throw Error(A(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(mt(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(A(171))}if(e.tag===1){var n=e.type;if(mt(n))return Sg(e,n,t)}return t}function N0(e,t,n,r,o,s,i,l,c){return e=Zd(n,r,!0,e,o,s,i,l,c),e.context=S0(null),n=e.current,r=it(),o=rr(n),s=bn(r,o),s.callback=t!=null?t:null,tr(n,s,o),e.current.lanes=o,si(e,o,r),gt(e,r),e}function il(e,t,n,r){var o=t.current,s=it(),i=rr(o);return n=S0(n),t.context===null?t.context=n:t.pendingContext=n,t=bn(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=tr(o,t,i),e!==null&&(Kt(e,o,i,s),Ji(e,o,i)),i}function Ma(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function eh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Jd(e,t){eh(e,t),(e=e.alternate)&&eh(e,t)}function Cb(){return null}var C0=typeof reportError=="function"?reportError:function(e){console.error(e)};function ef(e){this._internalRoot=e}al.prototype.render=ef.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(A(409));il(e,t,null,null)};al.prototype.unmount=ef.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;zr(function(){il(null,e,null,null)}),t[Nn]=null}};function al(e){this._internalRoot=e}al.prototype.unstable_scheduleHydration=function(e){if(e){var t=tg();e={blockedOn:null,target:e,priority:t};for(var n=0;n<$n.length&&t!==0&&t<$n[n].priority;n++);$n.splice(n,0,e),n===0&&rg(e)}};function tf(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ll(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function th(){}function jb(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var u=Ma(i);s.call(u)}}var i=N0(t,r,e,0,null,!1,!1,"",th);return e._reactRootContainer=i,e[Nn]=i.current,$s(e.nodeType===8?e.parentNode:e),zr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var u=Ma(c);l.call(u)}}var c=Zd(e,0,!1,null,null,!1,!1,"",th);return e._reactRootContainer=c,e[Nn]=c.current,$s(e.nodeType===8?e.parentNode:e),zr(function(){il(t,c,n,r)}),c}function cl(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var l=o;o=function(){var c=Ma(i);l.call(c)}}il(t,i,e,o)}else i=jb(n,t,e,o,r);return Ma(i)}Jm=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=vs(t.pendingLanes);n!==0&&(wd(t,n|1),gt(t,Oe()),!(ae&6)&&(Qo=Oe()+500,hr()))}break;case 13:zr(function(){var r=Cn(e,1);if(r!==null){var o=it();Kt(r,e,1,o)}}),Jd(e,1)}};bd=function(e){if(e.tag===13){var t=Cn(e,134217728);if(t!==null){var n=it();Kt(t,e,134217728,n)}Jd(e,134217728)}};eg=function(e){if(e.tag===13){var t=rr(e),n=Cn(e,t);if(n!==null){var r=it();Kt(n,e,t,r)}Jd(e,t)}};tg=function(){return he};ng=function(e,t){var n=he;try{return he=e,t()}finally{he=n}};Uc=function(e,t,n){switch(t){case"input":if(Lc(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Ja(r);if(!o)throw Error(A(90));Am(r),Lc(r,o)}}}break;case"textarea":Mm(e,n);break;case"select":t=n.value,t!=null&&go(e,!!n.multiple,t,!1)}};Bm=qd;Wm=zr;var Eb={usingClientEntryPoint:!1,Events:[ai,lo,Ja,zm,Um,qd]},fs={findFiberByHostInstance:Nr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},kb={bundleType:fs.bundleType,version:fs.version,rendererPackageName:fs.rendererPackageName,rendererConfig:fs.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Pn.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Qm(e),e===null?null:e.stateNode},findFiberByHostInstance:fs.findFiberByHostInstance||Cb,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__!="undefined"){var Ii=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ii.isDisabled&&Ii.supportsFiber)try{Ga=Ii.inject(kb),cn=Ii}catch(e){}}kt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Eb;kt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!tf(t))throw Error(A(200));return Nb(e,t,null,n)};kt.createRoot=function(e,t){if(!tf(e))throw Error(A(299));var n=!1,r="",o=C0;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=Zd(e,1,!1,null,null,n,!1,r,o),e[Nn]=t.current,$s(e.nodeType===8?e.parentNode:e),new ef(t)};kt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(A(188)):(e=Object.keys(e).join(","),Error(A(268,e)));return e=Qm(t),e=e===null?null:e.stateNode,e};kt.flushSync=function(e){return zr(e)};kt.hydrate=function(e,t,n){if(!ll(t))throw Error(A(200));return cl(null,e,t,!0,n)};kt.hydrateRoot=function(e,t,n){if(!tf(e))throw Error(A(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=C0;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=N0(t,null,e,1,n!=null?n:null,o,!1,s,i),e[Nn]=t.current,$s(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new al(t)};kt.render=function(e,t,n){if(!ll(t))throw Error(A(200));return cl(null,e,t,!1,n)};kt.unmountComponentAtNode=function(e){if(!ll(e))throw Error(A(40));return e._reactRootContainer?(zr(function(){cl(null,null,e,!1,function(){e._reactRootContainer=null,e[Nn]=null})}),!0):!1};kt.unstable_batchedUpdates=qd;kt.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!ll(n))throw Error(A(200));if(e==null||e._reactInternals===void 0)throw Error(A(38));return cl(e,t,n,!1,r)};kt.version="18.3.1-next-f1338f8080-20240426";function j0(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__=="undefined"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(j0)}catch(e){console.error(e)}}j0(),jm.exports=kt;var Kr=jm.exports;const E0=Ka(Kr);var k0,nh=Kr;k0=nh.createRoot,nh.hydrateRoot;const Pb=1,Tb=1e6;let fc=0;function Rb(){return fc=(fc+1)%Number.MAX_SAFE_INTEGER,fc.toString()}const pc=new Map,rh=e=>{if(pc.has(e))return;const t=setTimeout(()=>{pc.delete(e),Rs({type:"REMOVE_TOAST",toastId:e})},Tb);pc.set(e,t)},_b=(e,t)=>{switch(t.type){case"ADD_TOAST":return E(N({},e),{toasts:[t.toast,...e.toasts].slice(0,Pb)});case"UPDATE_TOAST":return E(N({},e),{toasts:e.toasts.map(n=>n.id===t.toast.id?N(N({},n),t.toast):n)});case"DISMISS_TOAST":{const{toastId:n}=t;return n?rh(n):e.toasts.forEach(r=>{rh(r.id)}),E(N({},e),{toasts:e.toasts.map(r=>r.id===n||n===void 0?E(N({},r),{open:!1}):r)})}case"REMOVE_TOAST":return t.toastId===void 0?E(N({},e),{toasts:[]}):E(N({},e),{toasts:e.toasts.filter(n=>n.id!==t.toastId)})}},ia=[];let aa={toasts:[]};function Rs(e){aa=_b(aa,e),ia.forEach(t=>{t(aa)})}function Ob(t){var e=D(t,[]);const n=Rb(),r=s=>Rs({type:"UPDATE_TOAST",toast:E(N({},s),{id:n})}),o=()=>Rs({type:"DISMISS_TOAST",toastId:n});return Rs({type:"ADD_TOAST",toast:E(N({},e),{id:n,open:!0,onOpenChange:s=>{s||o()}})}),{id:n,dismiss:o,update:r}}function Ab(){const[e,t]=h.useState(aa);return h.useEffect(()=>(ia.push(t),()=>{const n=ia.indexOf(t);n>-1&&ia.splice(n,1)}),[e]),E(N({},e),{toast:Ob,dismiss:n=>Rs({type:"DISMISS_TOAST",toastId:n})})}function J(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function Lb(e,t){typeof e=="function"?e(t):e!=null&&(e.current=t)}function P0(...e){return t=>e.forEach(n=>Lb(n,t))}function Ne(...e){return h.useCallback(P0(...e),e)}function Mb(e,t=[]){let n=[];function r(s,i){const l=h.createContext(i),c=n.length;n=[...n,i];function u(f){const m=f,{scope:d,children:y}=m,b=D(m,["scope","children"]),v=(d==null?void 0:d[e][c])||l,w=h.useMemo(()=>b,Object.values(b));return a.jsx(v.Provider,{value:w,children:y})}function p(f,d){const y=(d==null?void 0:d[e][c])||l,b=h.useContext(y);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,p]}const o=()=>{const s=n.map(i=>h.createContext(i));return function(l){const c=(l==null?void 0:l[e])||s;return h.useMemo(()=>({[`__scope${e}`]:E(N({},l),{[e]:c})}),[l,c])}};return o.scopeName=e,[r,Ib(o,...t)]}function Ib(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((l,{useScope:c,scopeName:u})=>{const f=c(s)[`__scope${u}`];return N(N({},l),f)},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Ko=h.forwardRef((e,t)=>{const i=e,{children:n}=i,r=D(i,["children"]),o=h.Children.toArray(n),s=o.find(Db);if(s){const l=s.props.children,c=o.map(u=>u===s?h.Children.count(l)>1?h.Children.only(null):h.isValidElement(l)?l.props.children:null:u);return a.jsx(wu,E(N({},r),{ref:t,children:h.isValidElement(l)?h.cloneElement(l,void 0,c):null}))}return a.jsx(wu,E(N({},r),{ref:t,children:n}))});Ko.displayName="Slot";var wu=h.forwardRef((e,t)=>{const o=e,{children:n}=o,r=D(o,["children"]);if(h.isValidElement(n)){const s=$b(n);return h.cloneElement(n,E(N({},Fb(r,n.props)),{ref:t?P0(t,s):s}))}return h.Children.count(n)>1?h.Children.only(null):null});wu.displayName="SlotClone";var T0=({children:e})=>a.jsx(a.Fragment,{children:e});function Db(e){return h.isValidElement(e)&&e.type===T0}function Fb(e,t){const n=N({},t);for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...l)=>{s(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]=N(N({},o),s):r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return N(N({},e),n)}function $b(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function R0(e){const t=e+"CollectionProvider",[n,r]=Mb(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=y=>{const{scope:b,children:v}=y,w=F.useRef(null),m=F.useRef(new Map).current;return a.jsx(o,{scope:b,itemMap:m,collectionRef:w,children:v})};i.displayName=t;const l=e+"CollectionSlot",c=F.forwardRef((y,b)=>{const{scope:v,children:w}=y,m=s(l,v),g=Ne(b,m.collectionRef);return a.jsx(Ko,{ref:g,children:w})});c.displayName=l;const u=e+"CollectionItemSlot",p="data-radix-collection-item",f=F.forwardRef((y,b)=>{const C=y,{scope:v,children:w}=C,m=D(C,["scope","children"]),g=F.useRef(null),x=Ne(b,g),S=s(u,v);return F.useEffect(()=>(S.itemMap.set(g,N({ref:g},m)),()=>void S.itemMap.delete(g))),a.jsx(Ko,{[p]:"",ref:x,children:w})});f.displayName=u;function d(y){const b=s(e+"CollectionConsumer",y);return F.useCallback(()=>{const w=b.collectionRef.current;if(!w)return[];const m=Array.from(w.querySelectorAll(`[${p}]`));return Array.from(b.itemMap.values()).sort((S,C)=>m.indexOf(S.ref.current)-m.indexOf(C.ref.current))},[b.collectionRef,b.itemMap])}return[{Provider:i,Slot:c,ItemSlot:f},d,r]}function zb(e,t){const n=h.createContext(t),r=s=>{const u=s,{children:i}=u,l=D(u,["children"]),c=h.useMemo(()=>l,Object.values(l));return a.jsx(n.Provider,{value:c,children:i})};r.displayName=e+"Provider";function o(s){const i=h.useContext(n);if(i)return i;if(t!==void 0)return t;throw new Error(`\`${s}\` must be used within \`${e}\``)}return[r,o]}function ul(e,t=[]){let n=[];function r(s,i){const l=h.createContext(i),c=n.length;n=[...n,i];const u=f=>{var g;const m=f,{scope:d,children:y}=m,b=D(m,["scope","children"]),v=((g=d==null?void 0:d[e])==null?void 0:g[c])||l,w=h.useMemo(()=>b,Object.values(b));return a.jsx(v.Provider,{value:w,children:y})};u.displayName=s+"Provider";function p(f,d){var v;const y=((v=d==null?void 0:d[e])==null?void 0:v[c])||l,b=h.useContext(y);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return[u,p]}const o=()=>{const s=n.map(i=>h.createContext(i));return function(l){const c=(l==null?void 0:l[e])||s;return h.useMemo(()=>({[`__scope${e}`]:E(N({},l),{[e]:c})}),[l,c])}};return o.scopeName=e,[r,Ub(o,...t)]}function Ub(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((l,{useScope:c,scopeName:u})=>{const f=c(s)[`__scope${u}`];return N(N({},l),f)},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}var Bb=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ce=Bb.reduce((e,t)=>{const n=h.forwardRef((r,o)=>{const c=r,{asChild:s}=c,i=D(c,["asChild"]),l=s?Ko:t;return typeof window!="undefined"&&(window[Symbol.for("radix-ui")]=!0),a.jsx(l,E(N({},i),{ref:o}))});return n.displayName=`Primitive.${t}`,E(N({},e),{[t]:n})},{});function _0(e,t){e&&Kr.flushSync(()=>e.dispatchEvent(t))}function Et(e){const t=h.useRef(e);return h.useEffect(()=>{t.current=e}),h.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function Wb(e,t=globalThis==null?void 0:globalThis.document){const n=Et(e);h.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var Hb="DismissableLayer",bu="dismissableLayer.update",Vb="dismissableLayer.pointerDownOutside",Qb="dismissableLayer.focusOutside",oh,O0=h.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),ci=h.forwardRef((e,t)=>{var R;const k=e,{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:l}=k,c=D(k,["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"]),u=h.useContext(O0),[p,f]=h.useState(null),d=(R=p==null?void 0:p.ownerDocument)!=null?R:globalThis==null?void 0:globalThis.document,[,y]=h.useState({}),b=Ne(t,_=>f(_)),v=Array.from(u.layers),[w]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),m=v.indexOf(w),g=p?v.indexOf(p):-1,x=u.layersWithOutsidePointerEventsDisabled.size>0,S=g>=m,C=qb(_=>{const O=_.target,B=[...u.branches].some(L=>L.contains(O));!S||B||(o==null||o(_),i==null||i(_),_.defaultPrevented||l==null||l())},d),T=Gb(_=>{const O=_.target;[...u.branches].some(L=>L.contains(O))||(s==null||s(_),i==null||i(_),_.defaultPrevented||l==null||l())},d);return Wb(_=>{g===u.layers.size-1&&(r==null||r(_),!_.defaultPrevented&&l&&(_.preventDefault(),l()))},d),h.useEffect(()=>{if(p)return n&&(u.layersWithOutsidePointerEventsDisabled.size===0&&(oh=d.body.style.pointerEvents,d.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(p)),u.layers.add(p),sh(),()=>{n&&u.layersWithOutsidePointerEventsDisabled.size===1&&(d.body.style.pointerEvents=oh)}},[p,d,n,u]),h.useEffect(()=>()=>{p&&(u.layers.delete(p),u.layersWithOutsidePointerEventsDisabled.delete(p),sh())},[p,u]),h.useEffect(()=>{const _=()=>y({});return document.addEventListener(bu,_),()=>document.removeEventListener(bu,_)},[]),a.jsx(ce.div,E(N({},c),{ref:b,style:N({pointerEvents:x?S?"auto":"none":void 0},e.style),onFocusCapture:J(e.onFocusCapture,T.onFocusCapture),onBlurCapture:J(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:J(e.onPointerDownCapture,C.onPointerDownCapture)}))});ci.displayName=Hb;var Kb="DismissableLayerBranch",A0=h.forwardRef((e,t)=>{const n=h.useContext(O0),r=h.useRef(null),o=Ne(t,r);return h.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),a.jsx(ce.div,E(N({},e),{ref:o}))});A0.displayName=Kb;function qb(e,t=globalThis==null?void 0:globalThis.document){const n=Et(e),r=h.useRef(!1),o=h.useRef(()=>{});return h.useEffect(()=>{const s=l=>{if(l.target&&!r.current){let c=function(){L0(Vb,n,u,{discrete:!0})};const u={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=c,t.addEventListener("click",o.current,{once:!0})):c()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function Gb(e,t=globalThis==null?void 0:globalThis.document){const n=Et(e),r=h.useRef(!1);return h.useEffect(()=>{const o=s=>{s.target&&!r.current&&L0(Qb,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function sh(){const e=new CustomEvent(bu);document.dispatchEvent(e)}function L0(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?_0(o,s):o.dispatchEvent(s)}var Yb=ci,Xb=A0,nt=globalThis!=null&&globalThis.document?h.useLayoutEffect:()=>{},Zb="Portal",dl=h.forwardRef((e,t)=>{var c;const l=e,{container:n}=l,r=D(l,["container"]),[o,s]=h.useState(!1);nt(()=>s(!0),[]);const i=n||o&&((c=globalThis==null?void 0:globalThis.document)==null?void 0:c.body);return i?E0.createPortal(a.jsx(ce.div,E(N({},r),{ref:t})),i):null});dl.displayName=Zb;function Jb(e,t){return h.useReducer((n,r)=>{const o=t[n][r];return o!=null?o:n},e)}var Jo=e=>{const{present:t,children:n}=e,r=eS(t),o=typeof n=="function"?n({present:r.isPresent}):h.Children.only(n),s=Ne(r.ref,tS(o));return typeof n=="function"||r.isPresent?h.cloneElement(o,{ref:s}):null};Jo.displayName="Presence";function eS(e){const[t,n]=h.useState(),r=h.useRef({}),o=h.useRef(e),s=h.useRef("none"),i=e?"mounted":"unmounted",[l,c]=Jb(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return h.useEffect(()=>{const u=Di(r.current);s.current=l==="mounted"?u:"none"},[l]),nt(()=>{const u=r.current,p=o.current;if(p!==e){const d=s.current,y=Di(u);e?c("MOUNT"):y==="none"||(u==null?void 0:u.display)==="none"?c("UNMOUNT"):c(p&&d!==y?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,c]),nt(()=>{var u;if(t){let p;const f=(u=t.ownerDocument.defaultView)!=null?u:window,d=b=>{const w=Di(r.current).includes(b.animationName);if(b.target===t&&w&&(c("ANIMATION_END"),!o.current)){const m=t.style.animationFillMode;t.style.animationFillMode="forwards",p=f.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=m)})}},y=b=>{b.target===t&&(s.current=Di(r.current))};return t.addEventListener("animationstart",y),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{f.clearTimeout(p),t.removeEventListener("animationstart",y),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else c("ANIMATION_END")},[t,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:h.useCallback(u=>{u&&(r.current=getComputedStyle(u)),n(u)},[])}}function Di(e){return(e==null?void 0:e.animationName)||"none"}function tS(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function Ia({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=nS({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,l=Et(n),c=h.useCallback(u=>{if(s){const f=typeof u=="function"?u(e):u;f!==e&&l(f)}else o(u)},[s,e,o,l]);return[i,c]}function nS({defaultProp:e,onChange:t}){const n=h.useState(e),[r]=n,o=h.useRef(r),s=Et(t);return h.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var rS="VisuallyHidden",ui=h.forwardRef((e,t)=>a.jsx(ce.span,E(N({},e),{ref:t,style:N({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"},e.style)})));ui.displayName=rS;var oS=ui,nf="ToastProvider",[rf,sS,iS]=R0("Toast"),[M0,l4]=ul("Toast",[iS]),[aS,fl]=M0(nf),I0=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[l,c]=h.useState(null),[u,p]=h.useState(0),f=h.useRef(!1),d=h.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${nf}\`. Expected non-empty \`string\`.`),a.jsx(rf.Provider,{scope:t,children:a.jsx(aS,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:u,viewport:l,onViewportChange:c,onToastAdd:h.useCallback(()=>p(y=>y+1),[]),onToastRemove:h.useCallback(()=>p(y=>y-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:d,children:i})})};I0.displayName=nf;var D0="ToastViewport",lS=["F8"],Su="toast.viewportPause",Nu="toast.viewportResume",F0=h.forwardRef((e,t)=>{const w=e,{__scopeToast:n,hotkey:r=lS,label:o="Notifications ({hotkey})"}=w,s=D(w,["__scopeToast","hotkey","label"]),i=fl(D0,n),l=sS(n),c=h.useRef(null),u=h.useRef(null),p=h.useRef(null),f=h.useRef(null),d=Ne(t,f,i.onViewportChange),y=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=i.toastCount>0;h.useEffect(()=>{const m=g=>{var S;r.length!==0&&r.every(C=>g[C]||g.code===C)&&((S=f.current)==null||S.focus())};return document.addEventListener("keydown",m),()=>document.removeEventListener("keydown",m)},[r]),h.useEffect(()=>{const m=c.current,g=f.current;if(b&&m&&g){const x=()=>{if(!i.isClosePausedRef.current){const k=new CustomEvent(Su);g.dispatchEvent(k),i.isClosePausedRef.current=!0}},S=()=>{if(i.isClosePausedRef.current){const k=new CustomEvent(Nu);g.dispatchEvent(k),i.isClosePausedRef.current=!1}},C=k=>{!m.contains(k.relatedTarget)&&S()},T=()=>{m.contains(document.activeElement)||S()};return m.addEventListener("focusin",x),m.addEventListener("focusout",C),m.addEventListener("pointermove",x),m.addEventListener("pointerleave",T),window.addEventListener("blur",x),window.addEventListener("focus",S),()=>{m.removeEventListener("focusin",x),m.removeEventListener("focusout",C),m.removeEventListener("pointermove",x),m.removeEventListener("pointerleave",T),window.removeEventListener("blur",x),window.removeEventListener("focus",S)}}},[b,i.isClosePausedRef]);const v=h.useCallback(({tabbingDirection:m})=>{const x=l().map(S=>{const C=S.ref.current,T=[C,...bS(C)];return m==="forwards"?T:T.reverse()});return(m==="forwards"?x.reverse():x).flat()},[l]);return h.useEffect(()=>{const m=f.current;if(m){const g=x=>{var T,k,R;const S=x.altKey||x.ctrlKey||x.metaKey;if(x.key==="Tab"&&!S){const _=document.activeElement,O=x.shiftKey;if(x.target===m&&O){(T=u.current)==null||T.focus();return}const V=v({tabbingDirection:O?"backwards":"forwards"}),Y=V.findIndex(M=>M===_);hc(V.slice(Y+1))?x.preventDefault():O?(k=u.current)==null||k.focus():(R=p.current)==null||R.focus()}};return m.addEventListener("keydown",g),()=>m.removeEventListener("keydown",g)}},[l,v]),a.jsxs(Xb,{ref:c,role:"region","aria-label":o.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&a.jsx(Cu,{ref:u,onFocusFromOutsideViewport:()=>{const m=v({tabbingDirection:"forwards"});hc(m)}}),a.jsx(rf.Slot,{scope:n,children:a.jsx(ce.ol,E(N({tabIndex:-1},s),{ref:d}))}),b&&a.jsx(Cu,{ref:p,onFocusFromOutsideViewport:()=>{const m=v({tabbingDirection:"backwards"});hc(m)}})]})});F0.displayName=D0;var $0="ToastFocusProxy",Cu=h.forwardRef((e,t)=>{const i=e,{__scopeToast:n,onFocusFromOutsideViewport:r}=i,o=D(i,["__scopeToast","onFocusFromOutsideViewport"]),s=fl($0,n);return a.jsx(ui,E(N({"aria-hidden":!0,tabIndex:0},o),{ref:t,style:{position:"fixed"},onFocus:l=>{var p;const c=l.relatedTarget;!((p=s.viewport)!=null&&p.contains(c))&&r()}}))});Cu.displayName=$0;var pl="Toast",cS="toast.swipeStart",uS="toast.swipeMove",dS="toast.swipeCancel",fS="toast.swipeEnd",z0=h.forwardRef((e,t)=>{const u=e,{forceMount:n,open:r,defaultOpen:o,onOpenChange:s}=u,i=D(u,["forceMount","open","defaultOpen","onOpenChange"]),[l=!0,c]=Ia({prop:r,defaultProp:o,onChange:s});return a.jsx(Jo,{present:n||l,children:a.jsx(mS,E(N({open:l},i),{ref:t,onClose:()=>c(!1),onPause:Et(e.onPause),onResume:Et(e.onResume),onSwipeStart:J(e.onSwipeStart,p=>{p.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:J(e.onSwipeMove,p=>{const{x:f,y:d}=p.detail.delta;p.currentTarget.setAttribute("data-swipe","move"),p.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${f}px`),p.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:J(e.onSwipeCancel,p=>{p.currentTarget.setAttribute("data-swipe","cancel"),p.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),p.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),p.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),p.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:J(e.onSwipeEnd,p=>{const{x:f,y:d}=p.detail.delta;p.currentTarget.setAttribute("data-swipe","end"),p.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),p.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),p.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${f}px`),p.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),c(!1)})}))})});z0.displayName=pl;var[pS,hS]=M0(pl,{onClose(){}}),mS=h.forwardRef((e,t)=>{const Y=e,{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:l,onPause:c,onResume:u,onSwipeStart:p,onSwipeMove:f,onSwipeCancel:d,onSwipeEnd:y}=Y,b=D(Y,["__scopeToast","type","duration","open","onClose","onEscapeKeyDown","onPause","onResume","onSwipeStart","onSwipeMove","onSwipeCancel","onSwipeEnd"]),v=fl(pl,n),[w,m]=h.useState(null),g=Ne(t,M=>m(M)),x=h.useRef(null),S=h.useRef(null),C=o||v.duration,T=h.useRef(0),k=h.useRef(C),R=h.useRef(0),{onToastAdd:_,onToastRemove:O}=v,B=Et(()=>{var z;(w==null?void 0:w.contains(document.activeElement))&&((z=v.viewport)==null||z.focus()),i()}),L=h.useCallback(M=>{!M||M===1/0||(window.clearTimeout(R.current),T.current=new Date().getTime(),R.current=window.setTimeout(B,M))},[B]);h.useEffect(()=>{const M=v.viewport;if(M){const z=()=>{L(k.current),u==null||u()},q=()=>{const P=new Date().getTime()-T.current;k.current=k.current-P,window.clearTimeout(R.current),c==null||c()};return M.addEventListener(Su,q),M.addEventListener(Nu,z),()=>{M.removeEventListener(Su,q),M.removeEventListener(Nu,z)}}},[v.viewport,C,c,u,L]),h.useEffect(()=>{s&&!v.isClosePausedRef.current&&L(C)},[s,C,v.isClosePausedRef,L]),h.useEffect(()=>(_(),()=>O()),[_,O]);const V=h.useMemo(()=>w?K0(w):null,[w]);return v.viewport?a.jsxs(a.Fragment,{children:[V&&a.jsx(gS,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:V}),a.jsx(pS,{scope:n,onClose:B,children:Kr.createPortal(a.jsx(rf.ItemSlot,{scope:n,children:a.jsx(Yb,{asChild:!0,onEscapeKeyDown:J(l,()=>{v.isFocusedToastEscapeKeyDownRef.current||B(),v.isFocusedToastEscapeKeyDownRef.current=!1}),children:a.jsx(ce.li,E(N({role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":v.swipeDirection},b),{ref:g,style:N({userSelect:"none",touchAction:"none"},e.style),onKeyDown:J(e.onKeyDown,M=>{M.key==="Escape"&&(l==null||l(M.nativeEvent),M.nativeEvent.defaultPrevented||(v.isFocusedToastEscapeKeyDownRef.current=!0,B()))}),onPointerDown:J(e.onPointerDown,M=>{M.button===0&&(x.current={x:M.clientX,y:M.clientY})}),onPointerMove:J(e.onPointerMove,M=>{if(!x.current)return;const z=M.clientX-x.current.x,q=M.clientY-x.current.y,P=!!S.current,I=["left","right"].includes(v.swipeDirection),U=["left","up"].includes(v.swipeDirection)?Math.min:Math.max,$=I?U(0,z):0,Q=I?0:U(0,q),te=M.pointerType==="touch"?10:2,pe={x:$,y:Q},Ce={originalEvent:M,delta:pe};P?(S.current=pe,Fi(uS,f,Ce,{discrete:!1})):ih(pe,v.swipeDirection,te)?(S.current=pe,Fi(cS,p,Ce,{discrete:!1}),M.target.setPointerCapture(M.pointerId)):(Math.abs(z)>te||Math.abs(q)>te)&&(x.current=null)}),onPointerUp:J(e.onPointerUp,M=>{const z=S.current,q=M.target;if(q.hasPointerCapture(M.pointerId)&&q.releasePointerCapture(M.pointerId),S.current=null,x.current=null,z){const P=M.currentTarget,I={originalEvent:M,delta:z};ih(z,v.swipeDirection,v.swipeThreshold)?Fi(fS,y,I,{discrete:!0}):Fi(dS,d,I,{discrete:!0}),P.addEventListener("click",U=>U.preventDefault(),{once:!0})}})}))})}),v.viewport)})]}):null}),gS=e=>{const u=e,{__scopeToast:t,children:n}=u,r=D(u,["__scopeToast","children"]),o=fl(pl,t),[s,i]=h.useState(!1),[l,c]=h.useState(!1);return xS(()=>i(!0)),h.useEffect(()=>{const p=window.setTimeout(()=>c(!0),1e3);return()=>window.clearTimeout(p)},[]),l?null:a.jsx(dl,{asChild:!0,children:a.jsx(ui,E(N({},r),{children:s&&a.jsxs(a.Fragment,{children:[o.label," ",n]})}))})},vS="ToastTitle",U0=h.forwardRef((e,t)=>{const o=e,{__scopeToast:n}=o,r=D(o,["__scopeToast"]);return a.jsx(ce.div,E(N({},r),{ref:t}))});U0.displayName=vS;var yS="ToastDescription",B0=h.forwardRef((e,t)=>{const o=e,{__scopeToast:n}=o,r=D(o,["__scopeToast"]);return a.jsx(ce.div,E(N({},r),{ref:t}))});B0.displayName=yS;var W0="ToastAction",H0=h.forwardRef((e,t)=>{const o=e,{altText:n}=o,r=D(o,["altText"]);return n.trim()?a.jsx(Q0,{altText:n,asChild:!0,children:a.jsx(of,E(N({},r),{ref:t}))}):(console.error(`Invalid prop \`altText\` supplied to \`${W0}\`. Expected non-empty \`string\`.`),null)});H0.displayName=W0;var V0="ToastClose",of=h.forwardRef((e,t)=>{const s=e,{__scopeToast:n}=s,r=D(s,["__scopeToast"]),o=hS(V0,n);return a.jsx(Q0,{asChild:!0,children:a.jsx(ce.button,E(N({type:"button"},r),{ref:t,onClick:J(e.onClick,o.onClose)}))})});of.displayName=V0;var Q0=h.forwardRef((e,t)=>{const s=e,{__scopeToast:n,altText:r}=s,o=D(s,["__scopeToast","altText"]);return a.jsx(ce.div,E(N({"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0},o),{ref:t}))});function K0(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),wS(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...K0(r))}}),t}function Fi(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?_0(o,s):o.dispatchEvent(s)}var ih=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function xS(e=()=>{}){const t=Et(e);nt(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function wS(e){return e.nodeType===e.ELEMENT_NODE}function bS(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function hc(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var SS=I0,q0=F0,G0=z0,Y0=U0,X0=B0,Z0=H0,J0=of;function ev(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=ev(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function tv(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=ev(e))&&(r&&(r+=" "),r+=t);return r}const ah=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,lh=tv,nv=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return lh(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(u=>{const p=n==null?void 0:n[u],f=s==null?void 0:s[u];if(p===null)return null;const d=ah(p)||ah(f);return o[u][d]}),l=n&&Object.entries(n).reduce((u,p)=>{let[f,d]=p;return d===void 0||(u[f]=d),u},{}),c=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((u,p)=>{let b=p,{class:f,className:d}=b,y=D(b,["class","className"]);return Object.entries(y).every(v=>{let[w,m]=v;return Array.isArray(m)?m.includes(N(N({},s),l)[w]):N(N({},s),l)[w]===m})?[...u,f,d]:u},[]);return lh(e,i,c,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NS=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),rv=(...e)=>e.filter((t,n,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===n).join(" ").trim();/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var CS={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jS=h.forwardRef((u,c)=>{var p=u,{color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i}=p,l=D(p,["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"]);return h.createElement("svg",N(E(N({ref:c},CS),{width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:rv("lucide",o)}),l),[...i.map(([f,d])=>h.createElement(f,d)),...Array.isArray(s)?s:[s]])});/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ee=(e,t)=>{const n=h.forwardRef((i,s)=>{var l=i,{className:r}=l,o=D(l,["className"]);return h.createElement(jS,N({ref:s,iconNode:t,className:rv(`lucide-${NS(e)}`,r)},o))});return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ch=ee("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ES=ee("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uh=ee("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kS=ee("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ov=ee("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PS=ee("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TS=ee("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RS=ee("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _S=ee("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const OS=ee("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const AS=ee("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ju=ee("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const LS=ee("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const MS=ee("Facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const IS=ee("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const DS=ee("Handshake",[["path",{d:"m11 17 2 2a1 1 0 1 0 3-3",key:"efffak"}],["path",{d:"m14 14 2.5 2.5a1 1 0 1 0 3-3l-3.88-3.88a3 3 0 0 0-4.24 0l-.88.88a1 1 0 1 1-3-3l2.81-2.81a5.79 5.79 0 0 1 7.06-.87l.47.28a2 2 0 0 0 1.42.25L21 4",key:"9pr0kb"}],["path",{d:"m21 3 1 11h-2",key:"1tisrp"}],["path",{d:"M3 3 2 14l6.5 6.5a1 1 0 1 0 3-3",key:"1uvwmv"}],["path",{d:"M3 4h8",key:"1ep09j"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sr=ee("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const FS=ee("Instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dh=ee("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $S=ee("Linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zS=ee("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const la=ee("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sv=ee("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const US=ee("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const BS=ee("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $i=ee("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eu=ee("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Da=ee("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const WS=ee("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Er=ee("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const HS=ee("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const VS=ee("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const QS=ee("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const KS=ee("Twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const qS=ee("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fh=ee("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ph=ee("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const GS=ee("Wrench",[["path",{d:"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z",key:"cbrjhi"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sf=ee("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const YS=ee("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),af="-",XS=e=>{const t=JS(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const l=i.split(af);return l[0]===""&&l.length!==1&&l.shift(),iv(l,t)||ZS(i)},getConflictingClassGroupIds:(i,l)=>{const c=n[i]||[];return l&&r[i]?[...c,...r[i]]:c}}},iv=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?iv(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(af);return(i=t.validators.find(({validator:l})=>l(s)))==null?void 0:i.classGroupId},hh=/^\[(.+)\]$/,ZS=e=>{if(hh.test(e)){const t=hh.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},JS=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return tN(Object.entries(e.classGroups),n).forEach(([s,i])=>{ku(i,r,s,t)}),r},ku=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:mh(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(eN(o)){ku(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{ku(i,mh(t,s),n,r)})})},mh=(e,t)=>{let n=e;return t.split(af).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},eN=e=>e.isThemeGetter,tN=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,l])=>[t+i,l])):s);return[n,o]}):e,nN=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},av="!",rN=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=l=>{const c=[];let u=0,p=0,f;for(let w=0;w<l.length;w++){let m=l[w];if(u===0){if(m===o&&(r||l.slice(w,w+s)===t)){c.push(l.slice(p,w)),p=w+s;continue}if(m==="/"){f=w;continue}}m==="["?u++:m==="]"&&u--}const d=c.length===0?l:l.substring(p),y=d.startsWith(av),b=y?d.substring(1):d,v=f&&f>p?f-p:void 0;return{modifiers:c,hasImportantModifier:y,baseClassName:b,maybePostfixModifierPosition:v}};return n?l=>n({className:l,parseClassName:i}):i},oN=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},sN=e=>N({cache:nN(e.cacheSize),parseClassName:rN(e)},XS(e)),iN=/\s+/,aN=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(iN);let l="";for(let c=i.length-1;c>=0;c-=1){const u=i[c],{modifiers:p,hasImportantModifier:f,baseClassName:d,maybePostfixModifierPosition:y}=n(u);let b=!!y,v=r(b?d.substring(0,y):d);if(!v){if(!b){l=u+(l.length>0?" "+l:l);continue}if(v=r(d),!v){l=u+(l.length>0?" "+l:l);continue}b=!1}const w=oN(p).join(":"),m=f?w+av:w,g=m+v;if(s.includes(g))continue;s.push(g);const x=o(v,b);for(let S=0;S<x.length;++S){const C=x[S];s.push(m+C)}l=u+(l.length>0?" "+l:l)}return l};function lN(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=lv(t))&&(r&&(r+=" "),r+=n);return r}const lv=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=lv(e[r]))&&(n&&(n+=" "),n+=t);return n};function cN(e,...t){let n,r,o,s=i;function i(c){const u=t.reduce((p,f)=>f(p),e());return n=sN(u),r=n.cache.get,o=n.cache.set,s=l,l(c)}function l(c){const u=r(c);if(u)return u;const p=aN(c,n);return o(c,p),p}return function(){return s(lN.apply(null,arguments))}}const we=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},cv=/^\[(?:([a-z-]+):)?(.+)\]$/i,uN=/^\d+\/\d+$/,dN=new Set(["px","full","screen"]),fN=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,pN=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,hN=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,mN=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,gN=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,pn=e=>So(e)||dN.has(e)||uN.test(e),An=e=>es(e,"length",CN),So=e=>!!e&&!Number.isNaN(Number(e)),mc=e=>es(e,"number",So),ps=e=>!!e&&Number.isInteger(Number(e)),vN=e=>e.endsWith("%")&&So(e.slice(0,-1)),Z=e=>cv.test(e),Ln=e=>fN.test(e),yN=new Set(["length","size","percentage"]),xN=e=>es(e,yN,uv),wN=e=>es(e,"position",uv),bN=new Set(["image","url"]),SN=e=>es(e,bN,EN),NN=e=>es(e,"",jN),hs=()=>!0,es=(e,t,n)=>{const r=cv.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},CN=e=>pN.test(e)&&!hN.test(e),uv=()=>!1,jN=e=>mN.test(e),EN=e=>gN.test(e),kN=()=>{const e=we("colors"),t=we("spacing"),n=we("blur"),r=we("brightness"),o=we("borderColor"),s=we("borderRadius"),i=we("borderSpacing"),l=we("borderWidth"),c=we("contrast"),u=we("grayscale"),p=we("hueRotate"),f=we("invert"),d=we("gap"),y=we("gradientColorStops"),b=we("gradientColorStopPositions"),v=we("inset"),w=we("margin"),m=we("opacity"),g=we("padding"),x=we("saturate"),S=we("scale"),C=we("sepia"),T=we("skew"),k=we("space"),R=we("translate"),_=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto",Z,t],L=()=>[Z,t],V=()=>["",pn,An],Y=()=>["auto",So,Z],M=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],z=()=>["solid","dashed","dotted","double","none"],q=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],P=()=>["start","end","center","between","around","evenly","stretch"],I=()=>["","0",Z],U=()=>["auto","avoid","all","avoid-page","page","left","right","column"],$=()=>[So,Z];return{cacheSize:500,separator:":",theme:{colors:[hs],spacing:[pn,An],blur:["none","",Ln,Z],brightness:$(),borderColor:[e],borderRadius:["none","","full",Ln,Z],borderSpacing:L(),borderWidth:V(),contrast:$(),grayscale:I(),hueRotate:$(),invert:I(),gap:L(),gradientColorStops:[e],gradientColorStopPositions:[vN,An],inset:B(),margin:B(),opacity:$(),padding:L(),saturate:$(),scale:$(),sepia:I(),skew:$(),space:L(),translate:L()},classGroups:{aspect:[{aspect:["auto","square","video",Z]}],container:["container"],columns:[{columns:[Ln]}],"break-after":[{"break-after":U()}],"break-before":[{"break-before":U()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...M(),Z]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:_()}],"overscroll-x":[{"overscroll-x":_()}],"overscroll-y":[{"overscroll-y":_()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[v]}],"inset-x":[{"inset-x":[v]}],"inset-y":[{"inset-y":[v]}],start:[{start:[v]}],end:[{end:[v]}],top:[{top:[v]}],right:[{right:[v]}],bottom:[{bottom:[v]}],left:[{left:[v]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",ps,Z]}],basis:[{basis:B()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Z]}],grow:[{grow:I()}],shrink:[{shrink:I()}],order:[{order:["first","last","none",ps,Z]}],"grid-cols":[{"grid-cols":[hs]}],"col-start-end":[{col:["auto",{span:["full",ps,Z]},Z]}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":[hs]}],"row-start-end":[{row:["auto",{span:[ps,Z]},Z]}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Z]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Z]}],gap:[{gap:[d]}],"gap-x":[{"gap-x":[d]}],"gap-y":[{"gap-y":[d]}],"justify-content":[{justify:["normal",...P()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...P(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...P(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[w]}],mx:[{mx:[w]}],my:[{my:[w]}],ms:[{ms:[w]}],me:[{me:[w]}],mt:[{mt:[w]}],mr:[{mr:[w]}],mb:[{mb:[w]}],ml:[{ml:[w]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Z,t]}],"min-w":[{"min-w":[Z,t,"min","max","fit"]}],"max-w":[{"max-w":[Z,t,"none","full","min","max","fit","prose",{screen:[Ln]},Ln]}],h:[{h:[Z,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Z,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Z,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Z,t,"auto","min","max","fit"]}],"font-size":[{text:["base",Ln,An]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",mc]}],"font-family":[{font:[hs]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Z]}],"line-clamp":[{"line-clamp":["none",So,mc]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",pn,Z]}],"list-image":[{"list-image":["none",Z]}],"list-style-type":[{list:["none","disc","decimal",Z]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[m]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[m]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",pn,An]}],"underline-offset":[{"underline-offset":["auto",pn,Z]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Z]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Z]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[m]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...M(),wN]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",xN]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},SN]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[b]}],"gradient-via-pos":[{via:[b]}],"gradient-to-pos":[{to:[b]}],"gradient-from":[{from:[y]}],"gradient-via":[{via:[y]}],"gradient-to":[{to:[y]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[m]}],"border-style":[{border:[...z(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[m]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...z()]}],"outline-offset":[{"outline-offset":[pn,Z]}],"outline-w":[{outline:[pn,An]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:V()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[m]}],"ring-offset-w":[{"ring-offset":[pn,An]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",Ln,NN]}],"shadow-color":[{shadow:[hs]}],opacity:[{opacity:[m]}],"mix-blend":[{"mix-blend":[...q(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":q()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",Ln,Z]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[p]}],invert:[{invert:[f]}],saturate:[{saturate:[x]}],sepia:[{sepia:[C]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[p]}],"backdrop-invert":[{"backdrop-invert":[f]}],"backdrop-opacity":[{"backdrop-opacity":[m]}],"backdrop-saturate":[{"backdrop-saturate":[x]}],"backdrop-sepia":[{"backdrop-sepia":[C]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Z]}],duration:[{duration:$()}],ease:[{ease:["linear","in","out","in-out",Z]}],delay:[{delay:$()}],animate:[{animate:["none","spin","ping","pulse","bounce",Z]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[S]}],"scale-x":[{"scale-x":[S]}],"scale-y":[{"scale-y":[S]}],rotate:[{rotate:[ps,Z]}],"translate-x":[{"translate-x":[R]}],"translate-y":[{"translate-y":[R]}],"skew-x":[{"skew-x":[T]}],"skew-y":[{"skew-y":[T]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Z]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Z]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Z]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[pn,An,mc]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},PN=cN(kN);function Le(...e){return PN(tv(e))}const TN=SS,dv=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(q0,N({ref:n,className:Le("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e)},t))});dv.displayName=q0.displayName;const RN=nv("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),fv=h.forwardRef((o,r)=>{var s=o,{className:e,variant:t}=s,n=D(s,["className","variant"]);return a.jsx(G0,N({ref:r,className:Le(RN({variant:t}),e)},n))});fv.displayName=G0.displayName;const _N=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(Z0,N({ref:n,className:Le("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e)},t))});_N.displayName=Z0.displayName;const pv=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(J0,E(N({ref:n,className:Le("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":""},t),{children:a.jsx(sf,{className:"h-4 w-4"})}))});pv.displayName=J0.displayName;const hv=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(Y0,N({ref:n,className:Le("text-sm font-semibold",e)},t))});hv.displayName=Y0.displayName;const mv=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(X0,N({ref:n,className:Le("text-sm opacity-90",e)},t))});mv.displayName=X0.displayName;function ON(){const{toasts:e}=Ab();return a.jsxs(TN,{children:[e.map(function(i){var l=i,{id:t,title:n,description:r,action:o}=l,s=D(l,["id","title","description","action"]);return a.jsxs(fv,E(N({},s),{children:[a.jsxs("div",{className:"grid gap-1",children:[n&&a.jsx(hv,{children:n}),r&&a.jsx(mv,{children:r})]}),o,a.jsx(pv,{})]}),t)}),a.jsx(dv,{})]})}var gh=["light","dark"],AN="(prefers-color-scheme: dark)",LN=h.createContext(void 0),MN={setTheme:e=>{},themes:[]},IN=()=>{var e;return(e=h.useContext(LN))!=null?e:MN};h.memo(({forcedTheme:e,storageKey:t,attribute:n,enableSystem:r,enableColorScheme:o,defaultTheme:s,value:i,attrs:l,nonce:c})=>{let u=s==="system",p=n==="class"?`var d=document.documentElement,c=d.classList;${`c.remove(${l.map(b=>`'${b}'`).join(",")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,f=o?gh.includes(s)&&s?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${s}'`:"if(e==='light'||e==='dark')d.style.colorScheme=e":"",d=(b,v=!1,w=!0)=>{let m=i?i[b]:b,g=v?b+"|| ''":`'${m}'`,x="";return o&&w&&!v&&gh.includes(b)&&(x+=`d.style.colorScheme = '${b}';`),n==="class"?v||m?x+=`c.add(${g})`:x+="null":m&&(x+=`d[s](n,${g})`),x},y=e?`!function(){${p}${d(e)}}()`:r?`!function(){try{${p}var e=localStorage.getItem('${t}');if('system'===e||(!e&&${u})){var t='${AN}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d("dark")}}else{${d("light")}}}else if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}${u?"":"else{"+d(s,!1,!1)+"}"}${f}}catch(e){}}()`:`!function(){try{${p}var e=localStorage.getItem('${t}');if(e){${i?`var x=${JSON.stringify(i)};`:""}${d(i?"x[e]":"e",!0)}}else{${d(s,!1,!1)};}${f}}catch(t){}}();`;return h.createElement("script",{nonce:c,dangerouslySetInnerHTML:{__html:y}})});var DN=e=>{switch(e){case"success":return zN;case"info":return BN;case"warning":return UN;case"error":return WN;default:return null}},FN=Array(12).fill(0),$N=({visible:e})=>F.createElement("div",{className:"sonner-loading-wrapper","data-visible":e},F.createElement("div",{className:"sonner-spinner"},FN.map((t,n)=>F.createElement("div",{className:"sonner-loading-bar",key:`spinner-bar-${n}`})))),zN=F.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},F.createElement("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z",clipRule:"evenodd"})),UN=F.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",height:"20",width:"20"},F.createElement("path",{fillRule:"evenodd",d:"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z",clipRule:"evenodd"})),BN=F.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},F.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z",clipRule:"evenodd"})),WN=F.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",height:"20",width:"20"},F.createElement("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})),HN=()=>{let[e,t]=F.useState(document.hidden);return F.useEffect(()=>{let n=()=>{t(document.hidden)};return document.addEventListener("visibilitychange",n),()=>window.removeEventListener("visibilitychange",n)},[]),e},Pu=1,VN=class{constructor(){this.subscribe=e=>(this.subscribers.push(e),()=>{let t=this.subscribers.indexOf(e);this.subscribers.splice(t,1)}),this.publish=e=>{this.subscribers.forEach(t=>t(e))},this.addToast=e=>{this.publish(e),this.toasts=[...this.toasts,e]},this.create=e=>{var t;let l=e,{message:n}=l,r=D(l,["message"]),o=typeof(e==null?void 0:e.id)=="number"||((t=e.id)==null?void 0:t.length)>0?e.id:Pu++,s=this.toasts.find(c=>c.id===o),i=e.dismissible===void 0?!0:e.dismissible;return s?this.toasts=this.toasts.map(c=>c.id===o?(this.publish(E(N(N({},c),e),{id:o,title:n})),E(N(N({},c),e),{id:o,dismissible:i,title:n})):c):this.addToast(E(N({title:n},r),{dismissible:i,id:o})),o},this.dismiss=e=>(e||this.toasts.forEach(t=>{this.subscribers.forEach(n=>n({id:t.id,dismiss:!0}))}),this.subscribers.forEach(t=>t({id:e,dismiss:!0})),e),this.message=(e,t)=>this.create(E(N({},t),{message:e})),this.error=(e,t)=>this.create(E(N({},t),{message:e,type:"error"})),this.success=(e,t)=>this.create(E(N({},t),{type:"success",message:e})),this.info=(e,t)=>this.create(E(N({},t),{type:"info",message:e})),this.warning=(e,t)=>this.create(E(N({},t),{type:"warning",message:e})),this.loading=(e,t)=>this.create(E(N({},t),{type:"loading",message:e})),this.promise=(e,t)=>{if(!t)return;let n;t.loading!==void 0&&(n=this.create(E(N({},t),{promise:e,type:"loading",message:t.loading,description:typeof t.description!="function"?t.description:void 0})));let r=e instanceof Promise?e:e(),o=n!==void 0;return r.then(s=>se(this,null,function*(){if(KN(s)&&!s.ok){o=!1;let i=typeof t.error=="function"?yield t.error(`HTTP error! status: ${s.status}`):t.error,l=typeof t.description=="function"?yield t.description(`HTTP error! status: ${s.status}`):t.description;this.create({id:n,type:"error",message:i,description:l})}else if(t.success!==void 0){o=!1;let i=typeof t.success=="function"?yield t.success(s):t.success,l=typeof t.description=="function"?yield t.description(s):t.description;this.create({id:n,type:"success",message:i,description:l})}})).catch(s=>se(this,null,function*(){if(t.error!==void 0){o=!1;let i=typeof t.error=="function"?yield t.error(s):t.error,l=typeof t.description=="function"?yield t.description(s):t.description;this.create({id:n,type:"error",message:i,description:l})}})).finally(()=>{var s;o&&(this.dismiss(n),n=void 0),(s=t.finally)==null||s.call(t)}),n},this.custom=(e,t)=>{let n=(t==null?void 0:t.id)||Pu++;return this.create(N({jsx:e(n),id:n},t)),n},this.subscribers=[],this.toasts=[]}},yt=new VN,QN=(e,t)=>{let n=(t==null?void 0:t.id)||Pu++;return yt.addToast(E(N({title:e},t),{id:n})),n},KN=e=>e&&typeof e=="object"&&"ok"in e&&typeof e.ok=="boolean"&&"status"in e&&typeof e.status=="number",qN=QN,GN=()=>yt.toasts;Object.assign(qN,{success:yt.success,info:yt.info,warning:yt.warning,error:yt.error,custom:yt.custom,message:yt.message,promise:yt.promise,dismiss:yt.dismiss,loading:yt.loading},{getHistory:GN});function YN(e,{insertAt:t}={}){if(typeof document=="undefined")return;let n=document.head||document.getElementsByTagName("head")[0],r=document.createElement("style");r.type="text/css",t==="top"&&n.firstChild?n.insertBefore(r,n.firstChild):n.appendChild(r),r.styleSheet?r.styleSheet.cssText=e:r.appendChild(document.createTextNode(e))}YN(`:where(html[dir="ltr"]),:where([data-sonner-toaster][dir="ltr"]){--toast-icon-margin-start: -3px;--toast-icon-margin-end: 4px;--toast-svg-margin-start: -1px;--toast-svg-margin-end: 0px;--toast-button-margin-start: auto;--toast-button-margin-end: 0;--toast-close-button-start: 0;--toast-close-button-end: unset;--toast-close-button-transform: translate(-35%, -35%)}:where(html[dir="rtl"]),:where([data-sonner-toaster][dir="rtl"]){--toast-icon-margin-start: 4px;--toast-icon-margin-end: -3px;--toast-svg-margin-start: 0px;--toast-svg-margin-end: -1px;--toast-button-margin-start: 0;--toast-button-margin-end: auto;--toast-close-button-start: unset;--toast-close-button-end: 0;--toast-close-button-transform: translate(35%, -35%)}:where([data-sonner-toaster]){position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1: hsl(0, 0%, 99%);--gray2: hsl(0, 0%, 97.3%);--gray3: hsl(0, 0%, 95.1%);--gray4: hsl(0, 0%, 93%);--gray5: hsl(0, 0%, 90.9%);--gray6: hsl(0, 0%, 88.7%);--gray7: hsl(0, 0%, 85.8%);--gray8: hsl(0, 0%, 78%);--gray9: hsl(0, 0%, 56.1%);--gray10: hsl(0, 0%, 52.3%);--gray11: hsl(0, 0%, 43.5%);--gray12: hsl(0, 0%, 9%);--border-radius: 8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:none;z-index:999999999}:where([data-sonner-toaster][data-x-position="right"]){right:max(var(--offset),env(safe-area-inset-right))}:where([data-sonner-toaster][data-x-position="left"]){left:max(var(--offset),env(safe-area-inset-left))}:where([data-sonner-toaster][data-x-position="center"]){left:50%;transform:translate(-50%)}:where([data-sonner-toaster][data-y-position="top"]){top:max(var(--offset),env(safe-area-inset-top))}:where([data-sonner-toaster][data-y-position="bottom"]){bottom:max(var(--offset),env(safe-area-inset-bottom))}:where([data-sonner-toast]){--y: translateY(100%);--lift-amount: calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);filter:blur(0);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:none;overflow-wrap:anywhere}:where([data-sonner-toast][data-styled="true"]){padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px #0000001a;width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}:where([data-sonner-toast]:focus-visible){box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast][data-y-position="top"]){top:0;--y: translateY(-100%);--lift: 1;--lift-amount: calc(1 * var(--gap))}:where([data-sonner-toast][data-y-position="bottom"]){bottom:0;--y: translateY(100%);--lift: -1;--lift-amount: calc(var(--lift) * var(--gap))}:where([data-sonner-toast]) :where([data-description]){font-weight:400;line-height:1.4;color:inherit}:where([data-sonner-toast]) :where([data-title]){font-weight:500;line-height:1.5;color:inherit}:where([data-sonner-toast]) :where([data-icon]){display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}:where([data-sonner-toast][data-promise="true"]) :where([data-icon])>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}:where([data-sonner-toast]) :where([data-icon])>*{flex-shrink:0}:where([data-sonner-toast]) :where([data-icon]) svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}:where([data-sonner-toast]) :where([data-content]){display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;cursor:pointer;outline:none;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}:where([data-sonner-toast]) :where([data-button]):focus-visible{box-shadow:0 0 0 2px #0006}:where([data-sonner-toast]) :where([data-button]):first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}:where([data-sonner-toast]) :where([data-cancel]){color:var(--normal-text);background:rgba(0,0,0,.08)}:where([data-sonner-toast][data-theme="dark"]) :where([data-cancel]){background:rgba(255,255,255,.3)}:where([data-sonner-toast]) :where([data-close-button]){position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;background:var(--gray1);color:var(--gray12);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}:where([data-sonner-toast]) :where([data-close-button]):focus-visible{box-shadow:0 4px 12px #0000001a,0 0 0 2px #0003}:where([data-sonner-toast]) :where([data-disabled="true"]){cursor:not-allowed}:where([data-sonner-toast]):hover :where([data-close-button]):hover{background:var(--gray2);border-color:var(--gray5)}:where([data-sonner-toast][data-swiping="true"]):before{content:"";position:absolute;left:0;right:0;height:100%;z-index:-1}:where([data-sonner-toast][data-y-position="top"][data-swiping="true"]):before{bottom:50%;transform:scaleY(3) translateY(50%)}:where([data-sonner-toast][data-y-position="bottom"][data-swiping="true"]):before{top:50%;transform:scaleY(3) translateY(-50%)}:where([data-sonner-toast][data-swiping="false"][data-removed="true"]):before{content:"";position:absolute;inset:0;transform:scaleY(2)}:where([data-sonner-toast]):after{content:"";position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}:where([data-sonner-toast][data-mounted="true"]){--y: translateY(0);opacity:1}:where([data-sonner-toast][data-expanded="false"][data-front="false"]){--scale: var(--toasts-before) * .05 + 1;--y: translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}:where([data-sonner-toast])>*{transition:opacity .4s}:where([data-sonner-toast][data-expanded="false"][data-front="false"][data-styled="true"])>*{opacity:0}:where([data-sonner-toast][data-visible="false"]){opacity:0;pointer-events:none}:where([data-sonner-toast][data-mounted="true"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}:where([data-sonner-toast][data-removed="true"][data-front="true"][data-swipe-out="false"]){--y: translateY(calc(var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="true"]){--y: translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}:where([data-sonner-toast][data-removed="true"][data-front="false"][data-swipe-out="false"][data-expanded="false"]){--y: translateY(40%);opacity:0;transition:transform .5s,opacity .2s}:where([data-sonner-toast][data-removed="true"][data-front="false"]):before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount, 0px));transition:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation:swipe-out .2s ease-out forwards}@keyframes swipe-out{0%{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount)));opacity:1}to{transform:translateY(calc(var(--lift) * var(--offset) + var(--swipe-amount) + var(--lift) * -100%));opacity:0}}@media (max-width: 600px){[data-sonner-toaster]{position:fixed;--mobile-offset: 16px;right:var(--mobile-offset);left:var(--mobile-offset);width:100%}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset)}[data-sonner-toaster][data-y-position=bottom]{bottom:20px}[data-sonner-toaster][data-y-position=top]{top:20px}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset);right:var(--mobile-offset);transform:none}}[data-sonner-toaster][data-theme=light]{--normal-bg: #fff;--normal-border: var(--gray4);--normal-text: var(--gray12);--success-bg: hsl(143, 85%, 96%);--success-border: hsl(145, 92%, 91%);--success-text: hsl(140, 100%, 27%);--info-bg: hsl(208, 100%, 97%);--info-border: hsl(221, 91%, 91%);--info-text: hsl(210, 92%, 45%);--warning-bg: hsl(49, 100%, 97%);--warning-border: hsl(49, 91%, 91%);--warning-text: hsl(31, 92%, 45%);--error-bg: hsl(359, 100%, 97%);--error-border: hsl(359, 100%, 94%);--error-text: hsl(360, 100%, 45%)}[data-sonner-toaster][data-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1)}[data-sonner-toaster][data-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg: #fff;--normal-border: var(--gray3);--normal-text: var(--gray12)}[data-sonner-toaster][data-theme=dark]{--normal-bg: #000;--normal-border: hsl(0, 0%, 20%);--normal-text: var(--gray1);--success-bg: hsl(150, 100%, 6%);--success-border: hsl(147, 100%, 12%);--success-text: hsl(150, 86%, 65%);--info-bg: hsl(215, 100%, 6%);--info-border: hsl(223, 100%, 12%);--info-text: hsl(216, 87%, 65%);--warning-bg: hsl(64, 100%, 6%);--warning-border: hsl(60, 100%, 12%);--warning-text: hsl(46, 87%, 65%);--error-bg: hsl(358, 76%, 10%);--error-border: hsl(357, 89%, 16%);--error-text: hsl(358, 100%, 81%)}[data-rich-colors=true][data-sonner-toast][data-type=success],[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info],[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning],[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error],[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size: 16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:nth-child(1){animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}to{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}to{opacity:.15}}@media (prefers-reduced-motion){[data-sonner-toast],[data-sonner-toast]>*,.sonner-loading-bar{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}
`);function zi(e){return e.label!==void 0}var XN=3,ZN="32px",JN=4e3,eC=356,tC=14,nC=20,rC=200;function oC(...e){return e.filter(Boolean).join(" ")}var sC=e=>{var t,n,r,o,s,i,l,c,u,p;let{invert:f,toast:d,unstyled:y,interacting:b,setHeights:v,visibleToasts:w,heights:m,index:g,toasts:x,expanded:S,removeToast:C,defaultRichColors:T,closeButton:k,style:R,cancelButtonStyle:_,actionButtonStyle:O,className:B="",descriptionClassName:L="",duration:V,position:Y,gap:M,loadingIcon:z,expandByDefault:q,classNames:P,icons:I,closeButtonAriaLabel:U="Close toast",pauseWhenPageIsHidden:$,cn:Q}=e,[te,pe]=F.useState(!1),[Ce,ie]=F.useState(!1),[$e,K]=F.useState(!1),[ue,Te]=F.useState(!1),[ge,le]=F.useState(0),[de,qe]=F.useState(0),Ge=F.useRef(null),Ye=F.useRef(null),Tn=g===0,xr=g+1<=w,ct=d.type,Rn=d.dismissible!==!1,Il=d.className||"",Gr=d.descriptionClassName||"",gi=F.useMemo(()=>m.findIndex(X=>X.toastId===d.id)||0,[m,d.id]),Kx=F.useMemo(()=>{var X;return(X=d.closeButton)!=null?X:k},[d.closeButton,k]),Ff=F.useMemo(()=>d.duration||V||JN,[d.duration,V]),Dl=F.useRef(0),Yr=F.useRef(0),$f=F.useRef(0),Xr=F.useRef(null),[zf,qx]=Y.split("-"),Uf=F.useMemo(()=>m.reduce((X,xe,ve)=>ve>=gi?X:X+xe.height,0),[m,gi]),Bf=HN(),Gx=d.invert||f,Fl=ct==="loading";Yr.current=F.useMemo(()=>gi*M+Uf,[gi,Uf]),F.useEffect(()=>{pe(!0)},[]),F.useLayoutEffect(()=>{if(!te)return;let X=Ye.current,xe=X.style.height;X.style.height="auto";let ve=X.getBoundingClientRect().height;X.style.height=xe,qe(ve),v(Zt=>Zt.find(Jt=>Jt.toastId===d.id)?Zt.map(Jt=>Jt.toastId===d.id?E(N({},Jt),{height:ve}):Jt):[{toastId:d.id,height:ve,position:d.position},...Zt])},[te,d.title,d.description,v,d.id]);let _n=F.useCallback(()=>{ie(!0),le(Yr.current),v(X=>X.filter(xe=>xe.toastId!==d.id)),setTimeout(()=>{C(d)},rC)},[d,C,v,Yr]);F.useEffect(()=>{if(d.promise&&ct==="loading"||d.duration===1/0||d.type==="loading")return;let X,xe=Ff;return S||b||$&&Bf?(()=>{if($f.current<Dl.current){let ve=new Date().getTime()-Dl.current;xe=xe-ve}$f.current=new Date().getTime()})():xe!==1/0&&(Dl.current=new Date().getTime(),X=setTimeout(()=>{var ve;(ve=d.onAutoClose)==null||ve.call(d,d),_n()},xe)),()=>clearTimeout(X)},[S,b,q,d,Ff,_n,d.promise,ct,$,Bf]),F.useEffect(()=>{let X=Ye.current;if(X){let xe=X.getBoundingClientRect().height;return qe(xe),v(ve=>[{toastId:d.id,height:xe,position:d.position},...ve]),()=>v(ve=>ve.filter(Zt=>Zt.toastId!==d.id))}},[v,d.id]),F.useEffect(()=>{d.delete&&_n()},[_n,d.delete]);function Yx(){return I!=null&&I.loading?F.createElement("div",{className:"sonner-loader","data-visible":ct==="loading"},I.loading):z?F.createElement("div",{className:"sonner-loader","data-visible":ct==="loading"},z):F.createElement($N,{visible:ct==="loading"})}return F.createElement("li",{"aria-live":d.important?"assertive":"polite","aria-atomic":"true",role:"status",tabIndex:0,ref:Ye,className:Q(B,Il,P==null?void 0:P.toast,(t=d==null?void 0:d.classNames)==null?void 0:t.toast,P==null?void 0:P.default,P==null?void 0:P[ct],(n=d==null?void 0:d.classNames)==null?void 0:n[ct]),"data-sonner-toast":"","data-rich-colors":(r=d.richColors)!=null?r:T,"data-styled":!(d.jsx||d.unstyled||y),"data-mounted":te,"data-promise":!!d.promise,"data-removed":Ce,"data-visible":xr,"data-y-position":zf,"data-x-position":qx,"data-index":g,"data-front":Tn,"data-swiping":$e,"data-dismissible":Rn,"data-type":ct,"data-invert":Gx,"data-swipe-out":ue,"data-expanded":!!(S||q&&te),style:N(N({"--index":g,"--toasts-before":g,"--z-index":x.length-g,"--offset":`${Ce?ge:Yr.current}px`,"--initial-height":q?"auto":`${de}px`},R),d.style),onPointerDown:X=>{Fl||!Rn||(Ge.current=new Date,le(Yr.current),X.target.setPointerCapture(X.pointerId),X.target.tagName!=="BUTTON"&&(K(!0),Xr.current={x:X.clientX,y:X.clientY}))},onPointerUp:()=>{var X,xe,ve,Zt;if(ue||!Rn)return;Xr.current=null;let Jt=Number(((X=Ye.current)==null?void 0:X.style.getPropertyValue("--swipe-amount").replace("px",""))||0),vi=new Date().getTime()-((xe=Ge.current)==null?void 0:xe.getTime()),Xx=Math.abs(Jt)/vi;if(Math.abs(Jt)>=nC||Xx>.11){le(Yr.current),(ve=d.onDismiss)==null||ve.call(d,d),_n(),Te(!0);return}(Zt=Ye.current)==null||Zt.style.setProperty("--swipe-amount","0px"),K(!1)},onPointerMove:X=>{var xe;if(!Xr.current||!Rn)return;let ve=X.clientY-Xr.current.y,Zt=X.clientX-Xr.current.x,Jt=(zf==="top"?Math.min:Math.max)(0,ve),vi=X.pointerType==="touch"?10:2;Math.abs(Jt)>vi?(xe=Ye.current)==null||xe.style.setProperty("--swipe-amount",`${ve}px`):Math.abs(Zt)>vi&&(Xr.current=null)}},Kx&&!d.jsx?F.createElement("button",{"aria-label":U,"data-disabled":Fl,"data-close-button":!0,onClick:Fl||!Rn?()=>{}:()=>{var X;_n(),(X=d.onDismiss)==null||X.call(d,d)},className:Q(P==null?void 0:P.closeButton,(o=d==null?void 0:d.classNames)==null?void 0:o.closeButton)},F.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"},F.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),F.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))):null,d.jsx||F.isValidElement(d.title)?d.jsx||d.title:F.createElement(F.Fragment,null,ct||d.icon||d.promise?F.createElement("div",{"data-icon":"",className:Q(P==null?void 0:P.icon,(s=d==null?void 0:d.classNames)==null?void 0:s.icon)},d.promise||d.type==="loading"&&!d.icon?d.icon||Yx():null,d.type!=="loading"?d.icon||(I==null?void 0:I[ct])||DN(ct):null):null,F.createElement("div",{"data-content":"",className:Q(P==null?void 0:P.content,(i=d==null?void 0:d.classNames)==null?void 0:i.content)},F.createElement("div",{"data-title":"",className:Q(P==null?void 0:P.title,(l=d==null?void 0:d.classNames)==null?void 0:l.title)},d.title),d.description?F.createElement("div",{"data-description":"",className:Q(L,Gr,P==null?void 0:P.description,(c=d==null?void 0:d.classNames)==null?void 0:c.description)},d.description):null),F.isValidElement(d.cancel)?d.cancel:d.cancel&&zi(d.cancel)?F.createElement("button",{"data-button":!0,"data-cancel":!0,style:d.cancelButtonStyle||_,onClick:X=>{var xe,ve;zi(d.cancel)&&Rn&&((ve=(xe=d.cancel).onClick)==null||ve.call(xe,X),_n())},className:Q(P==null?void 0:P.cancelButton,(u=d==null?void 0:d.classNames)==null?void 0:u.cancelButton)},d.cancel.label):null,F.isValidElement(d.action)?d.action:d.action&&zi(d.action)?F.createElement("button",{"data-button":!0,"data-action":!0,style:d.actionButtonStyle||O,onClick:X=>{var xe,ve;zi(d.action)&&(X.defaultPrevented||((ve=(xe=d.action).onClick)==null||ve.call(xe,X),_n()))},className:Q(P==null?void 0:P.actionButton,(p=d==null?void 0:d.classNames)==null?void 0:p.actionButton)},d.action.label):null))};function vh(){if(typeof window=="undefined"||typeof document=="undefined")return"ltr";let e=document.documentElement.getAttribute("dir");return e==="auto"||!e?window.getComputedStyle(document.documentElement).direction:e}var iC=e=>{let{invert:t,position:n="bottom-right",hotkey:r=["altKey","KeyT"],expand:o,closeButton:s,className:i,offset:l,theme:c="light",richColors:u,duration:p,style:f,visibleToasts:d=XN,toastOptions:y,dir:b=vh(),gap:v=tC,loadingIcon:w,icons:m,containerAriaLabel:g="Notifications",pauseWhenPageIsHidden:x,cn:S=oC}=e,[C,T]=F.useState([]),k=F.useMemo(()=>Array.from(new Set([n].concat(C.filter($=>$.position).map($=>$.position)))),[C,n]),[R,_]=F.useState([]),[O,B]=F.useState(!1),[L,V]=F.useState(!1),[Y,M]=F.useState(c!=="system"?c:typeof window!="undefined"&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),z=F.useRef(null),q=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),P=F.useRef(null),I=F.useRef(!1),U=F.useCallback($=>{var Q;(Q=C.find(te=>te.id===$.id))!=null&&Q.delete||yt.dismiss($.id),T(te=>te.filter(({id:pe})=>pe!==$.id))},[C]);return F.useEffect(()=>yt.subscribe($=>{if($.dismiss){T(Q=>Q.map(te=>te.id===$.id?E(N({},te),{delete:!0}):te));return}setTimeout(()=>{E0.flushSync(()=>{T(Q=>{let te=Q.findIndex(pe=>pe.id===$.id);return te!==-1?[...Q.slice(0,te),N(N({},Q[te]),$),...Q.slice(te+1)]:[$,...Q]})})})}),[]),F.useEffect(()=>{if(c!=="system"){M(c);return}c==="system"&&(window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?M("dark"):M("light")),typeof window!="undefined"&&window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",({matches:$})=>{M($?"dark":"light")})},[c]),F.useEffect(()=>{C.length<=1&&B(!1)},[C]),F.useEffect(()=>{let $=Q=>{var te,pe;r.every(Ce=>Q[Ce]||Q.code===Ce)&&(B(!0),(te=z.current)==null||te.focus()),Q.code==="Escape"&&(document.activeElement===z.current||(pe=z.current)!=null&&pe.contains(document.activeElement))&&B(!1)};return document.addEventListener("keydown",$),()=>document.removeEventListener("keydown",$)},[r]),F.useEffect(()=>{if(z.current)return()=>{P.current&&(P.current.focus({preventScroll:!0}),P.current=null,I.current=!1)}},[z.current]),C.length?F.createElement("section",{"aria-label":`${g} ${q}`,tabIndex:-1},k.map(($,Q)=>{var te;let[pe,Ce]=$.split("-");return F.createElement("ol",{key:$,dir:b==="auto"?vh():b,tabIndex:-1,ref:z,className:i,"data-sonner-toaster":!0,"data-theme":Y,"data-y-position":pe,"data-x-position":Ce,style:N({"--front-toast-height":`${((te=R[0])==null?void 0:te.height)||0}px`,"--offset":typeof l=="number"?`${l}px`:l||ZN,"--width":`${eC}px`,"--gap":`${v}px`},f),onBlur:ie=>{I.current&&!ie.currentTarget.contains(ie.relatedTarget)&&(I.current=!1,P.current&&(P.current.focus({preventScroll:!0}),P.current=null))},onFocus:ie=>{ie.target instanceof HTMLElement&&ie.target.dataset.dismissible==="false"||I.current||(I.current=!0,P.current=ie.relatedTarget)},onMouseEnter:()=>B(!0),onMouseMove:()=>B(!0),onMouseLeave:()=>{L||B(!1)},onPointerDown:ie=>{ie.target instanceof HTMLElement&&ie.target.dataset.dismissible==="false"||V(!0)},onPointerUp:()=>V(!1)},C.filter(ie=>!ie.position&&Q===0||ie.position===$).map((ie,$e)=>{var K,ue;return F.createElement(sC,{key:ie.id,icons:m,index:$e,toast:ie,defaultRichColors:u,duration:(K=y==null?void 0:y.duration)!=null?K:p,className:y==null?void 0:y.className,descriptionClassName:y==null?void 0:y.descriptionClassName,invert:t,visibleToasts:d,closeButton:(ue=y==null?void 0:y.closeButton)!=null?ue:s,interacting:L,position:$,style:y==null?void 0:y.style,unstyled:y==null?void 0:y.unstyled,classNames:y==null?void 0:y.classNames,cancelButtonStyle:y==null?void 0:y.cancelButtonStyle,actionButtonStyle:y==null?void 0:y.actionButtonStyle,removeToast:U,toasts:C.filter(Te=>Te.position==ie.position),heights:R.filter(Te=>Te.position==ie.position),setHeights:_,expandByDefault:o,gap:v,loadingIcon:w,expanded:O,pauseWhenPageIsHidden:x,cn:S})}))})):null};const aC=t=>{var e=D(t,[]);const{theme:n="system"}=IN();return a.jsx(iC,N({theme:n,className:"toaster group",toastOptions:{classNames:{toast:"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg",description:"group-[.toast]:text-muted-foreground",actionButton:"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground",cancelButton:"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground"}}},e))};var lC=Nm.useId||(()=>{}),cC=0;function No(e){const[t,n]=h.useState(lC());return nt(()=>{n(r=>r!=null?r:String(cC++))},[e]),t?`radix-${t}`:""}const uC=["top","right","bottom","left"],lr=Math.min,wt=Math.max,Fa=Math.round,Ui=Math.floor,cr=e=>({x:e,y:e}),dC={left:"right",right:"left",bottom:"top",top:"bottom"},fC={start:"end",end:"start"};function Tu(e,t,n){return wt(e,lr(t,n))}function En(e,t){return typeof e=="function"?e(t):e}function kn(e){return e.split("-")[0]}function ts(e){return e.split("-")[1]}function lf(e){return e==="x"?"y":"x"}function cf(e){return e==="y"?"height":"width"}function ur(e){return["top","bottom"].includes(kn(e))?"y":"x"}function uf(e){return lf(ur(e))}function pC(e,t,n){n===void 0&&(n=!1);const r=ts(e),o=uf(e),s=cf(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=$a(i)),[i,$a(i)]}function hC(e){const t=$a(e);return[Ru(e),t,Ru(t)]}function Ru(e){return e.replace(/start|end/g,t=>fC[t])}function mC(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function gC(e,t,n,r){const o=ts(e);let s=mC(kn(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(Ru)))),s}function $a(e){return e.replace(/left|right|bottom|top/g,t=>dC[t])}function vC(e){return N({top:0,right:0,bottom:0,left:0},e)}function gv(e){return typeof e!="number"?vC(e):{top:e,right:e,bottom:e,left:e}}function za(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function yh(e,t,n){let{reference:r,floating:o}=e;const s=ur(t),i=uf(t),l=cf(i),c=kn(t),u=s==="y",p=r.x+r.width/2-o.width/2,f=r.y+r.height/2-o.height/2,d=r[l]/2-o[l]/2;let y;switch(c){case"top":y={x:p,y:r.y-o.height};break;case"bottom":y={x:p,y:r.y+r.height};break;case"right":y={x:r.x+r.width,y:f};break;case"left":y={x:r.x-o.width,y:f};break;default:y={x:r.x,y:r.y}}switch(ts(t)){case"start":y[i]-=d*(n&&u?-1:1);break;case"end":y[i]+=d*(n&&u?-1:1);break}return y}const yC=(e,t,n)=>se(Ve,null,function*(){const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,l=s.filter(Boolean),c=yield i.isRTL==null?void 0:i.isRTL(t);let u=yield i.getElementRects({reference:e,floating:t,strategy:o}),{x:p,y:f}=yh(u,r,c),d=r,y={},b=0;for(let v=0;v<l.length;v++){const{name:w,fn:m}=l[v],{x:g,y:x,data:S,reset:C}=yield m({x:p,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:y,rects:u,platform:i,elements:{reference:e,floating:t}});p=g!=null?g:p,f=x!=null?x:f,y=E(N({},y),{[w]:N(N({},y[w]),S)}),C&&b<=50&&(b++,typeof C=="object"&&(C.placement&&(d=C.placement),C.rects&&(u=C.rects===!0?yield i.getElementRects({reference:e,floating:t,strategy:o}):C.rects),{x:p,y:f}=yh(u,d,c)),v=-1)}return{x:p,y:f,placement:d,strategy:o,middlewareData:y}});function qs(e,t){return se(this,null,function*(){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:l,strategy:c}=e,{boundary:u="clippingAncestors",rootBoundary:p="viewport",elementContext:f="floating",altBoundary:d=!1,padding:y=0}=En(t,e),b=gv(y),w=l[d?f==="floating"?"reference":"floating":f],m=za(yield s.getClippingRect({element:(n=yield s.isElement==null?void 0:s.isElement(w))==null||n?w:w.contextElement||(yield s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:u,rootBoundary:p,strategy:c})),g=f==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,x=yield s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating),S=(yield s.isElement==null?void 0:s.isElement(x))?(yield s.getScale==null?void 0:s.getScale(x))||{x:1,y:1}:{x:1,y:1},C=za(s.convertOffsetParentRelativeRectToViewportRelativeRect?yield s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:x,strategy:c}):g);return{top:(m.top-C.top+b.top)/S.y,bottom:(C.bottom-m.bottom+b.bottom)/S.y,left:(m.left-C.left+b.left)/S.x,right:(C.right-m.right+b.right)/S.x}})}const xC=e=>({name:"arrow",options:e,fn(n){return se(this,null,function*(){const{x:r,y:o,placement:s,rects:i,platform:l,elements:c,middlewareData:u}=n,{element:p,padding:f=0}=En(e,n)||{};if(p==null)return{};const d=gv(f),y={x:r,y:o},b=uf(s),v=cf(b),w=yield l.getDimensions(p),m=b==="y",g=m?"top":"left",x=m?"bottom":"right",S=m?"clientHeight":"clientWidth",C=i.reference[v]+i.reference[b]-y[b]-i.floating[v],T=y[b]-i.reference[b],k=yield l.getOffsetParent==null?void 0:l.getOffsetParent(p);let R=k?k[S]:0;(!R||!(yield l.isElement==null?void 0:l.isElement(k)))&&(R=c.floating[S]||i.floating[v]);const _=C/2-T/2,O=R/2-w[v]/2-1,B=lr(d[g],O),L=lr(d[x],O),V=B,Y=R-w[v]-L,M=R/2-w[v]/2+_,z=Tu(V,M,Y),q=!u.arrow&&ts(s)!=null&&M!==z&&i.reference[v]/2-(M<V?B:L)-w[v]/2<0,P=q?M<V?M-V:M-Y:0;return{[b]:y[b]+P,data:N({[b]:z,centerOffset:M-z-P},q&&{alignmentOffset:P}),reset:q}})}}),wC=function(e){return e===void 0&&(e={}),{name:"flip",options:e,fn(n){return se(this,null,function*(){var r,o;const{placement:s,middlewareData:i,rects:l,initialPlacement:c,platform:u,elements:p}=n,M=En(e,n),{mainAxis:f=!0,crossAxis:d=!0,fallbackPlacements:y,fallbackStrategy:b="bestFit",fallbackAxisSideDirection:v="none",flipAlignment:w=!0}=M,m=D(M,["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"]);if((r=i.arrow)!=null&&r.alignmentOffset)return{};const g=kn(s),x=ur(c),S=kn(c)===c,C=yield u.isRTL==null?void 0:u.isRTL(p.floating),T=y||(S||!w?[$a(c)]:hC(c)),k=v!=="none";!y&&k&&T.push(...gC(c,w,v,C));const R=[c,...T],_=yield qs(n,m),O=[];let B=((o=i.flip)==null?void 0:o.overflows)||[];if(f&&O.push(_[g]),d){const z=pC(s,l,C);O.push(_[z[0]],_[z[1]])}if(B=[...B,{placement:s,overflows:O}],!O.every(z=>z<=0)){var L,V;const z=(((L=i.flip)==null?void 0:L.index)||0)+1,q=R[z];if(q)return{data:{index:z,overflows:B},reset:{placement:q}};let P=(V=B.filter(I=>I.overflows[0]<=0).sort((I,U)=>I.overflows[1]-U.overflows[1])[0])==null?void 0:V.placement;if(!P)switch(b){case"bestFit":{var Y;const I=(Y=B.filter(U=>{if(k){const $=ur(U.placement);return $===x||$==="y"}return!0}).map(U=>[U.placement,U.overflows.filter($=>$>0).reduce(($,Q)=>$+Q,0)]).sort((U,$)=>U[1]-$[1])[0])==null?void 0:Y[0];I&&(P=I);break}case"initialPlacement":P=c;break}if(s!==P)return{reset:{placement:P}}}return{}})}}};function xh(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function wh(e){return uC.some(t=>e[t]>=0)}const bC=function(e){return e===void 0&&(e={}),{name:"hide",options:e,fn(n){return se(this,null,function*(){const{rects:r}=n,i=En(e,n),{strategy:o="referenceHidden"}=i,s=D(i,["strategy"]);switch(o){case"referenceHidden":{const l=yield qs(n,E(N({},s),{elementContext:"reference"})),c=xh(l,r.reference);return{data:{referenceHiddenOffsets:c,referenceHidden:wh(c)}}}case"escaped":{const l=yield qs(n,E(N({},s),{altBoundary:!0})),c=xh(l,r.floating);return{data:{escapedOffsets:c,escaped:wh(c)}}}default:return{}}})}}};function SC(e,t){return se(this,null,function*(){const{placement:n,platform:r,elements:o}=e,s=yield r.isRTL==null?void 0:r.isRTL(o.floating),i=kn(n),l=ts(n),c=ur(n)==="y",u=["left","top"].includes(i)?-1:1,p=s&&c?-1:1,f=En(t,e);let{mainAxis:d,crossAxis:y,alignmentAxis:b}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&typeof b=="number"&&(y=l==="end"?b*-1:b),c?{x:y*p,y:d*u}:{x:d*u,y:y*p}})}const NC=function(e){return e===void 0&&(e=0),{name:"offset",options:e,fn(n){return se(this,null,function*(){var r,o;const{x:s,y:i,placement:l,middlewareData:c}=n,u=yield SC(n,e);return l===((r=c.offset)==null?void 0:r.placement)&&(o=c.arrow)!=null&&o.alignmentOffset?{}:{x:s+u.x,y:i+u.y,data:E(N({},u),{placement:l})}})}}},CC=function(e){return e===void 0&&(e={}),{name:"shift",options:e,fn(n){return se(this,null,function*(){const{x:r,y:o,placement:s}=n,m=En(e,n),{mainAxis:i=!0,crossAxis:l=!1,limiter:c={fn:g=>{let{x,y:S}=g;return{x,y:S}}}}=m,u=D(m,["mainAxis","crossAxis","limiter"]),p={x:r,y:o},f=yield qs(n,u),d=ur(kn(s)),y=lf(d);let b=p[y],v=p[d];if(i){const g=y==="y"?"top":"left",x=y==="y"?"bottom":"right",S=b+f[g],C=b-f[x];b=Tu(S,b,C)}if(l){const g=d==="y"?"top":"left",x=d==="y"?"bottom":"right",S=v+f[g],C=v-f[x];v=Tu(S,v,C)}const w=c.fn(E(N({},n),{[y]:b,[d]:v}));return E(N({},w),{data:{x:w.x-r,y:w.y-o,enabled:{[y]:i,[d]:l}}})})}}},jC=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:l=0,mainAxis:c=!0,crossAxis:u=!0}=En(e,t),p={x:n,y:r},f=ur(o),d=lf(f);let y=p[d],b=p[f];const v=En(l,t),w=typeof v=="number"?{mainAxis:v,crossAxis:0}:N({mainAxis:0,crossAxis:0},v);if(c){const x=d==="y"?"height":"width",S=s.reference[d]-s.floating[x]+w.mainAxis,C=s.reference[d]+s.reference[x]-w.mainAxis;y<S?y=S:y>C&&(y=C)}if(u){var m,g;const x=d==="y"?"width":"height",S=["top","left"].includes(kn(o)),C=s.reference[f]-s.floating[x]+(S&&((m=i.offset)==null?void 0:m[f])||0)+(S?0:w.crossAxis),T=s.reference[f]+s.reference[x]+(S?0:((g=i.offset)==null?void 0:g[f])||0)-(S?w.crossAxis:0);b<C?b=C:b>T&&(b=T)}return{[d]:y,[f]:b}}}},EC=function(e){return e===void 0&&(e={}),{name:"size",options:e,fn(n){return se(this,null,function*(){var r,o;const{placement:s,rects:i,platform:l,elements:c}=n,B=En(e,n),{apply:u=()=>{}}=B,p=D(B,["apply"]),f=yield qs(n,p),d=kn(s),y=ts(s),b=ur(s)==="y",{width:v,height:w}=i.floating;let m,g;d==="top"||d==="bottom"?(m=d,g=y===((yield l.isRTL==null?void 0:l.isRTL(c.floating))?"start":"end")?"left":"right"):(g=d,m=y==="end"?"top":"bottom");const x=w-f.top-f.bottom,S=v-f.left-f.right,C=lr(w-f[m],x),T=lr(v-f[g],S),k=!n.middlewareData.shift;let R=C,_=T;if((r=n.middlewareData.shift)!=null&&r.enabled.x&&(_=S),(o=n.middlewareData.shift)!=null&&o.enabled.y&&(R=x),k&&!y){const L=wt(f.left,0),V=wt(f.right,0),Y=wt(f.top,0),M=wt(f.bottom,0);b?_=v-2*(L!==0||V!==0?L+V:wt(f.left,f.right)):R=w-2*(Y!==0||M!==0?Y+M:wt(f.top,f.bottom))}yield u(E(N({},n),{availableWidth:_,availableHeight:R}));const O=yield l.getDimensions(c.floating);return v!==O.width||w!==O.height?{reset:{rects:!0}}:{}})}}};function hl(){return typeof window!="undefined"}function ns(e){return vv(e)?(e.nodeName||"").toLowerCase():"#document"}function Ct(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function fn(e){var t;return(t=(vv(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function vv(e){return hl()?e instanceof Node||e instanceof Ct(e).Node:!1}function Gt(e){return hl()?e instanceof Element||e instanceof Ct(e).Element:!1}function dn(e){return hl()?e instanceof HTMLElement||e instanceof Ct(e).HTMLElement:!1}function bh(e){return!hl()||typeof ShadowRoot=="undefined"?!1:e instanceof ShadowRoot||e instanceof Ct(e).ShadowRoot}function di(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Yt(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function kC(e){return["table","td","th"].includes(ns(e))}function ml(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(n){return!1}})}function df(e){const t=ff(),n=Gt(e)?Yt(e):e;return n.transform!=="none"||n.perspective!=="none"||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function PC(e){let t=dr(e);for(;dn(t)&&!qo(t);){if(df(t))return t;if(ml(t))return null;t=dr(t)}return null}function ff(){return typeof CSS=="undefined"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function qo(e){return["html","body","#document"].includes(ns(e))}function Yt(e){return Ct(e).getComputedStyle(e)}function gl(e){return Gt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function dr(e){if(ns(e)==="html")return e;const t=e.assignedSlot||e.parentNode||bh(e)&&e.host||fn(e);return bh(t)?t.host:t}function yv(e){const t=dr(e);return qo(t)?e.ownerDocument?e.ownerDocument.body:e.body:dn(t)&&di(t)?t:yv(t)}function Gs(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=yv(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=Ct(o);if(s){const l=_u(i);return t.concat(i,i.visualViewport||[],di(o)?o:[],l&&n?Gs(l):[])}return t.concat(o,Gs(o,[],n))}function _u(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function xv(e){const t=Yt(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=dn(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,l=Fa(n)!==s||Fa(r)!==i;return l&&(n=s,r=i),{width:n,height:r,$:l}}function pf(e){return Gt(e)?e:e.contextElement}function Co(e){const t=pf(e);if(!dn(t))return cr(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=xv(t);let i=(s?Fa(n.width):n.width)/r,l=(s?Fa(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const TC=cr(0);function wv(e){const t=Ct(e);return!ff()||!t.visualViewport?TC:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function RC(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==Ct(e)?!1:t}function Ur(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=pf(e);let i=cr(1);t&&(r?Gt(r)&&(i=Co(r)):i=Co(e));const l=RC(s,n,r)?wv(s):cr(0);let c=(o.left+l.x)/i.x,u=(o.top+l.y)/i.y,p=o.width/i.x,f=o.height/i.y;if(s){const d=Ct(s),y=r&&Gt(r)?Ct(r):r;let b=d,v=_u(b);for(;v&&r&&y!==b;){const w=Co(v),m=v.getBoundingClientRect(),g=Yt(v),x=m.left+(v.clientLeft+parseFloat(g.paddingLeft))*w.x,S=m.top+(v.clientTop+parseFloat(g.paddingTop))*w.y;c*=w.x,u*=w.y,p*=w.x,f*=w.y,c+=x,u+=S,b=Ct(v),v=_u(b)}}return za({width:p,height:f,x:c,y:u})}function _C(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=fn(r),l=t?ml(t.floating):!1;if(r===i||l&&s)return n;let c={scrollLeft:0,scrollTop:0},u=cr(1);const p=cr(0),f=dn(r);if((f||!f&&!s)&&((ns(r)!=="body"||di(i))&&(c=gl(r)),dn(r))){const d=Ur(r);u=Co(r),p.x=d.x+r.clientLeft,p.y=d.y+r.clientTop}return{width:n.width*u.x,height:n.height*u.y,x:n.x*u.x-c.scrollLeft*u.x+p.x,y:n.y*u.y-c.scrollTop*u.y+p.y}}function OC(e){return Array.from(e.getClientRects())}function Ou(e,t){const n=gl(e).scrollLeft;return t?t.left+n:Ur(fn(e)).left+n}function AC(e){const t=fn(e),n=gl(e),r=e.ownerDocument.body,o=wt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=wt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Ou(e);const l=-n.scrollTop;return Yt(r).direction==="rtl"&&(i+=wt(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:l}}function LC(e,t){const n=Ct(e),r=fn(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,l=0,c=0;if(o){s=o.width,i=o.height;const u=ff();(!u||u&&t==="fixed")&&(l=o.offsetLeft,c=o.offsetTop)}return{width:s,height:i,x:l,y:c}}function MC(e,t){const n=Ur(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=dn(e)?Co(e):cr(1),i=e.clientWidth*s.x,l=e.clientHeight*s.y,c=o*s.x,u=r*s.y;return{width:i,height:l,x:c,y:u}}function Sh(e,t,n){let r;if(t==="viewport")r=LC(e,n);else if(t==="document")r=AC(fn(e));else if(Gt(t))r=MC(t,n);else{const o=wv(e);r=E(N({},t),{x:t.x-o.x,y:t.y-o.y})}return za(r)}function bv(e,t){const n=dr(e);return n===t||!Gt(n)||qo(n)?!1:Yt(n).position==="fixed"||bv(n,t)}function IC(e,t){const n=t.get(e);if(n)return n;let r=Gs(e,[],!1).filter(l=>Gt(l)&&ns(l)!=="body"),o=null;const s=Yt(e).position==="fixed";let i=s?dr(e):e;for(;Gt(i)&&!qo(i);){const l=Yt(i),c=df(i);!c&&l.position==="fixed"&&(o=null),(s?!c&&!o:!c&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||di(i)&&!c&&bv(e,i))?r=r.filter(p=>p!==i):o=l,i=dr(i)}return t.set(e,r),r}function DC(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?ml(t)?[]:IC(t,this._c):[].concat(n),r],l=i[0],c=i.reduce((u,p)=>{const f=Sh(t,p,o);return u.top=wt(f.top,u.top),u.right=lr(f.right,u.right),u.bottom=lr(f.bottom,u.bottom),u.left=wt(f.left,u.left),u},Sh(t,l,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}}function FC(e){const{width:t,height:n}=xv(e);return{width:t,height:n}}function $C(e,t,n){const r=dn(t),o=fn(t),s=n==="fixed",i=Ur(e,!0,s,t);let l={scrollLeft:0,scrollTop:0};const c=cr(0);if(r||!r&&!s)if((ns(t)!=="body"||di(o))&&(l=gl(t)),r){const y=Ur(t,!0,s,t);c.x=y.x+t.clientLeft,c.y=y.y+t.clientTop}else o&&(c.x=Ou(o));let u=0,p=0;if(o&&!r&&!s){const y=o.getBoundingClientRect();p=y.top+l.scrollTop,u=y.left+l.scrollLeft-Ou(o,y)}const f=i.left+l.scrollLeft-c.x-u,d=i.top+l.scrollTop-c.y-p;return{x:f,y:d,width:i.width,height:i.height}}function gc(e){return Yt(e).position==="static"}function Nh(e,t){if(!dn(e)||Yt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return fn(e)===n&&(n=n.ownerDocument.body),n}function Sv(e,t){const n=Ct(e);if(ml(e))return n;if(!dn(e)){let o=dr(e);for(;o&&!qo(o);){if(Gt(o)&&!gc(o))return o;o=dr(o)}return n}let r=Nh(e,t);for(;r&&kC(r)&&gc(r);)r=Nh(r,t);return r&&qo(r)&&gc(r)&&!df(r)?n:r||PC(e)||n}const zC=function(e){return se(this,null,function*(){const t=this.getOffsetParent||Sv,n=this.getDimensions,r=yield n(e.floating);return{reference:$C(e.reference,yield t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}})};function UC(e){return Yt(e).direction==="rtl"}const BC={convertOffsetParentRelativeRectToViewportRelativeRect:_C,getDocumentElement:fn,getClippingRect:DC,getOffsetParent:Sv,getElementRects:zC,getClientRects:OC,getDimensions:FC,getScale:Co,isElement:Gt,isRTL:UC};function WC(e,t){let n=null,r;const o=fn(e);function s(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function i(l,c){l===void 0&&(l=!1),c===void 0&&(c=1),s();const{left:u,top:p,width:f,height:d}=e.getBoundingClientRect();if(l||t(),!f||!d)return;const y=Ui(p),b=Ui(o.clientWidth-(u+f)),v=Ui(o.clientHeight-(p+d)),w=Ui(u),g={rootMargin:-y+"px "+-b+"px "+-v+"px "+-w+"px",threshold:wt(0,lr(1,c))||1};let x=!0;function S(C){const T=C[0].intersectionRatio;if(T!==c){if(!x)return i();T?i(!1,T):r=setTimeout(()=>{i(!1,1e-7)},1e3)}x=!1}try{n=new IntersectionObserver(S,E(N({},g),{root:o.ownerDocument}))}catch(C){n=new IntersectionObserver(S,g)}n.observe(e)}return i(!0),s}function HC(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:c=!1}=r,u=pf(e),p=o||s?[...u?Gs(u):[],...Gs(t)]:[];p.forEach(m=>{o&&m.addEventListener("scroll",n,{passive:!0}),s&&m.addEventListener("resize",n)});const f=u&&l?WC(u,n):null;let d=-1,y=null;i&&(y=new ResizeObserver(m=>{let[g]=m;g&&g.target===u&&y&&(y.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var x;(x=y)==null||x.observe(t)})),n()}),u&&!c&&y.observe(u),y.observe(t));let b,v=c?Ur(e):null;c&&w();function w(){const m=Ur(e);v&&(m.x!==v.x||m.y!==v.y||m.width!==v.width||m.height!==v.height)&&n(),v=m,b=requestAnimationFrame(w)}return n(),()=>{var m;p.forEach(g=>{o&&g.removeEventListener("scroll",n),s&&g.removeEventListener("resize",n)}),f==null||f(),(m=y)==null||m.disconnect(),y=null,c&&cancelAnimationFrame(b)}}const VC=NC,QC=CC,KC=wC,qC=EC,GC=bC,Ch=xC,YC=jC,XC=(e,t,n)=>{const r=new Map,o=N({platform:BC},n),s=E(N({},o.platform),{_c:r});return yC(e,t,E(N({},o),{platform:s}))};var ca=typeof document!="undefined"?h.useLayoutEffect:h.useEffect;function Ua(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Ua(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!Ua(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Nv(e){return typeof window=="undefined"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function jh(e,t){const n=Nv(e);return Math.round(t*n)/n}function vc(e){const t=h.useRef(e);return ca(()=>{t.current=e}),t}function ZC(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:l=!0,whileElementsMounted:c,open:u}=e,[p,f]=h.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[d,y]=h.useState(r);Ua(d,r)||y(r);const[b,v]=h.useState(null),[w,m]=h.useState(null),g=h.useCallback(P=>{P!==T.current&&(T.current=P,v(P))},[]),x=h.useCallback(P=>{P!==k.current&&(k.current=P,m(P))},[]),S=s||b,C=i||w,T=h.useRef(null),k=h.useRef(null),R=h.useRef(p),_=c!=null,O=vc(c),B=vc(o),L=vc(u),V=h.useCallback(()=>{if(!T.current||!k.current)return;const P={placement:t,strategy:n,middleware:d};B.current&&(P.platform=B.current),XC(T.current,k.current,P).then(I=>{const U=E(N({},I),{isPositioned:L.current!==!1});Y.current&&!Ua(R.current,U)&&(R.current=U,Kr.flushSync(()=>{f(U)}))})},[d,t,n,B,L]);ca(()=>{u===!1&&R.current.isPositioned&&(R.current.isPositioned=!1,f(P=>E(N({},P),{isPositioned:!1})))},[u]);const Y=h.useRef(!1);ca(()=>(Y.current=!0,()=>{Y.current=!1}),[]),ca(()=>{if(S&&(T.current=S),C&&(k.current=C),S&&C){if(O.current)return O.current(S,C,V);V()}},[S,C,V,O,_]);const M=h.useMemo(()=>({reference:T,floating:k,setReference:g,setFloating:x}),[g,x]),z=h.useMemo(()=>({reference:S,floating:C}),[S,C]),q=h.useMemo(()=>{const P={position:n,left:0,top:0};if(!z.floating)return P;const I=jh(z.floating,p.x),U=jh(z.floating,p.y);return l?N(E(N({},P),{transform:"translate("+I+"px, "+U+"px)"}),Nv(z.floating)>=1.5&&{willChange:"transform"}):{position:n,left:I,top:U}},[n,l,z.floating,p.x,p.y]);return h.useMemo(()=>E(N({},p),{update:V,refs:M,elements:z,floatingStyles:q}),[p,V,M,z,q])}const JC=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Ch({element:r.current,padding:o}).fn(n):{}:r?Ch({element:r,padding:o}).fn(n):{}}}},ej=(e,t)=>E(N({},VC(e)),{options:[e,t]}),tj=(e,t)=>E(N({},QC(e)),{options:[e,t]}),nj=(e,t)=>E(N({},YC(e)),{options:[e,t]}),rj=(e,t)=>E(N({},KC(e)),{options:[e,t]}),oj=(e,t)=>E(N({},qC(e)),{options:[e,t]}),sj=(e,t)=>E(N({},GC(e)),{options:[e,t]}),ij=(e,t)=>E(N({},JC(e)),{options:[e,t]});var aj="Arrow",Cv=h.forwardRef((e,t)=>{const i=e,{children:n,width:r=10,height:o=5}=i,s=D(i,["children","width","height"]);return a.jsx(ce.svg,E(N({},s),{ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:a.jsx("polygon",{points:"0,0 30,0 15,10"})}))});Cv.displayName=aj;var lj=Cv;function cj(e,t=[]){let n=[];function r(s,i){const l=h.createContext(i),c=n.length;n=[...n,i];function u(f){const m=f,{scope:d,children:y}=m,b=D(m,["scope","children"]),v=(d==null?void 0:d[e][c])||l,w=h.useMemo(()=>b,Object.values(b));return a.jsx(v.Provider,{value:w,children:y})}function p(f,d){const y=(d==null?void 0:d[e][c])||l,b=h.useContext(y);if(b)return b;if(i!==void 0)return i;throw new Error(`\`${f}\` must be used within \`${s}\``)}return u.displayName=s+"Provider",[u,p]}const o=()=>{const s=n.map(i=>h.createContext(i));return function(l){const c=(l==null?void 0:l[e])||s;return h.useMemo(()=>({[`__scope${e}`]:E(N({},l),{[e]:c})}),[l,c])}};return o.scopeName=e,[r,uj(o,...t)]}function uj(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((l,{useScope:c,scopeName:u})=>{const f=c(s)[`__scope${u}`];return N(N({},l),f)},{});return h.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function dj(e){const[t,n]=h.useState(void 0);return nt(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,l;if("borderBoxSize"in s){const c=s.borderBoxSize,u=Array.isArray(c)?c[0]:c;i=u.inlineSize,l=u.blockSize}else i=e.offsetWidth,l=e.offsetHeight;n({width:i,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var hf="Popper",[jv,vl]=cj(hf),[fj,Ev]=jv(hf),kv=e=>{const{__scopePopper:t,children:n}=e,[r,o]=h.useState(null);return a.jsx(fj,{scope:t,anchor:r,onAnchorChange:o,children:n})};kv.displayName=hf;var Pv="PopperAnchor",Tv=h.forwardRef((e,t)=>{const c=e,{__scopePopper:n,virtualRef:r}=c,o=D(c,["__scopePopper","virtualRef"]),s=Ev(Pv,n),i=h.useRef(null),l=Ne(t,i);return h.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:a.jsx(ce.div,E(N({},o),{ref:l}))});Tv.displayName=Pv;var mf="PopperContent",[pj,hj]=jv(mf),Rv=h.forwardRef((e,t)=>{var K,ue,Te,ge,le,de,qe,Ge;const $e=e,{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:l=0,avoidCollisions:c=!0,collisionBoundary:u=[],collisionPadding:p=0,sticky:f="partial",hideWhenDetached:d=!1,updatePositionStrategy:y="optimized",onPlaced:b}=$e,v=D($e,["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"]),w=Ev(mf,n),[m,g]=h.useState(null),x=Ne(t,Ye=>g(Ye)),[S,C]=h.useState(null),T=dj(S),k=(K=T==null?void 0:T.width)!=null?K:0,R=(ue=T==null?void 0:T.height)!=null?ue:0,_=r+(s!=="center"?"-"+s:""),O=typeof p=="number"?p:N({top:0,right:0,bottom:0,left:0},p),B=Array.isArray(u)?u:[u],L=B.length>0,V={padding:O,boundary:B.filter(gj),altBoundary:L},{refs:Y,floatingStyles:M,placement:z,isPositioned:q,middlewareData:P}=ZC({strategy:"fixed",placement:_,whileElementsMounted:(...Ye)=>HC(...Ye,{animationFrame:y==="always"}),elements:{reference:w.anchor},middleware:[ej({mainAxis:o+R,alignmentAxis:i}),c&&tj(N({mainAxis:!0,crossAxis:!1,limiter:f==="partial"?nj():void 0},V)),c&&rj(N({},V)),oj(E(N({},V),{apply:({elements:Ye,rects:Tn,availableWidth:xr,availableHeight:ct})=>{const{width:Rn,height:Il}=Tn.reference,Gr=Ye.floating.style;Gr.setProperty("--radix-popper-available-width",`${xr}px`),Gr.setProperty("--radix-popper-available-height",`${ct}px`),Gr.setProperty("--radix-popper-anchor-width",`${Rn}px`),Gr.setProperty("--radix-popper-anchor-height",`${Il}px`)}})),S&&ij({element:S,padding:l}),vj({arrowWidth:k,arrowHeight:R}),d&&sj(N({strategy:"referenceHidden"},V))]}),[I,U]=Av(z),$=Et(b);nt(()=>{q&&($==null||$())},[q,$]);const Q=(Te=P.arrow)==null?void 0:Te.x,te=(ge=P.arrow)==null?void 0:ge.y,pe=((le=P.arrow)==null?void 0:le.centerOffset)!==0,[Ce,ie]=h.useState();return nt(()=>{m&&ie(window.getComputedStyle(m).zIndex)},[m]),a.jsx("div",{ref:Y.setFloating,"data-radix-popper-content-wrapper":"",style:N(E(N({},M),{transform:q?M.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ce,"--radix-popper-transform-origin":[(de=P.transformOrigin)==null?void 0:de.x,(qe=P.transformOrigin)==null?void 0:qe.y].join(" ")}),((Ge=P.hide)==null?void 0:Ge.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:e.dir,children:a.jsx(pj,{scope:n,placedSide:I,onArrowChange:C,arrowX:Q,arrowY:te,shouldHideArrow:pe,children:a.jsx(ce.div,E(N({"data-side":I,"data-align":U},v),{ref:x,style:E(N({},v.style),{animation:q?void 0:"none"})}))})})});Rv.displayName=mf;var _v="PopperArrow",mj={top:"bottom",right:"left",bottom:"top",left:"right"},Ov=h.forwardRef(function(t,n){const l=t,{__scopePopper:r}=l,o=D(l,["__scopePopper"]),s=hj(_v,r),i=mj[s.placedSide];return a.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:a.jsx(lj,E(N({},o),{ref:n,style:E(N({},o.style),{display:"block"})}))})});Ov.displayName=_v;function gj(e){return e!==null}var vj=e=>({name:"transformOrigin",options:e,fn(t){var w,m,g,x,S;const{placement:n,rects:r,middlewareData:o}=t,i=((w=o.arrow)==null?void 0:w.centerOffset)!==0,l=i?0:e.arrowWidth,c=i?0:e.arrowHeight,[u,p]=Av(n),f={start:"0%",center:"50%",end:"100%"}[p],d=((g=(m=o.arrow)==null?void 0:m.x)!=null?g:0)+l/2,y=((S=(x=o.arrow)==null?void 0:x.y)!=null?S:0)+c/2;let b="",v="";return u==="bottom"?(b=i?f:`${d}px`,v=`${-c}px`):u==="top"?(b=i?f:`${d}px`,v=`${r.floating.height+c}px`):u==="right"?(b=`${-c}px`,v=i?f:`${y}px`):u==="left"&&(b=`${r.floating.width+c}px`,v=i?f:`${y}px`),{data:{x:b,y:v}}}});function Av(e){const[t,n="center"]=e.split("-");return[t,n]}var yj=kv,Lv=Tv,Mv=Rv,Iv=Ov,[yl,c4]=ul("Tooltip",[vl]),gf=vl(),Dv="TooltipProvider",xj=700,Eh="tooltip.open",[wj,Fv]=yl(Dv),$v=e=>{const{__scopeTooltip:t,delayDuration:n=xj,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,[i,l]=h.useState(!0),c=h.useRef(!1),u=h.useRef(0);return h.useEffect(()=>{const p=u.current;return()=>window.clearTimeout(p)},[]),a.jsx(wj,{scope:t,isOpenDelayed:i,delayDuration:n,onOpen:h.useCallback(()=>{window.clearTimeout(u.current),l(!1)},[]),onClose:h.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>l(!0),r)},[r]),isPointerInTransitRef:c,onPointerInTransitChange:h.useCallback(p=>{c.current=p},[]),disableHoverableContent:o,children:s})};$v.displayName=Dv;var zv="Tooltip",[u4,xl]=yl(zv),Au="TooltipTrigger",bj=h.forwardRef((e,t)=>{const d=e,{__scopeTooltip:n}=d,r=D(d,["__scopeTooltip"]),o=xl(Au,n),s=Fv(Au,n),i=gf(n),l=h.useRef(null),c=Ne(t,l,o.onTriggerChange),u=h.useRef(!1),p=h.useRef(!1),f=h.useCallback(()=>u.current=!1,[]);return h.useEffect(()=>()=>document.removeEventListener("pointerup",f),[f]),a.jsx(Lv,E(N({asChild:!0},i),{children:a.jsx(ce.button,E(N({"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute},r),{ref:c,onPointerMove:J(e.onPointerMove,y=>{y.pointerType!=="touch"&&!p.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),p.current=!0)}),onPointerLeave:J(e.onPointerLeave,()=>{o.onTriggerLeave(),p.current=!1}),onPointerDown:J(e.onPointerDown,()=>{u.current=!0,document.addEventListener("pointerup",f,{once:!0})}),onFocus:J(e.onFocus,()=>{u.current||o.onOpen()}),onBlur:J(e.onBlur,o.onClose),onClick:J(e.onClick,o.onClose)}))}))});bj.displayName=Au;var Sj="TooltipPortal",[d4,Nj]=yl(Sj,{forceMount:void 0}),Go="TooltipContent",Uv=h.forwardRef((e,t)=>{const n=Nj(Go,e.__scopeTooltip),l=e,{forceMount:r=n.forceMount,side:o="top"}=l,s=D(l,["forceMount","side"]),i=xl(Go,e.__scopeTooltip);return a.jsx(Jo,{present:r||i.open,children:i.disableHoverableContent?a.jsx(Bv,E(N({side:o},s),{ref:t})):a.jsx(Cj,E(N({side:o},s),{ref:t}))})}),Cj=h.forwardRef((e,t)=>{const n=xl(Go,e.__scopeTooltip),r=Fv(Go,e.__scopeTooltip),o=h.useRef(null),s=Ne(t,o),[i,l]=h.useState(null),{trigger:c,onClose:u}=n,p=o.current,{onPointerInTransitChange:f}=r,d=h.useCallback(()=>{l(null),f(!1)},[f]),y=h.useCallback((b,v)=>{const w=b.currentTarget,m={x:b.clientX,y:b.clientY},g=Pj(m,w.getBoundingClientRect()),x=Tj(m,g),S=Rj(v.getBoundingClientRect()),C=Oj([...x,...S]);l(C),f(!0)},[f]);return h.useEffect(()=>()=>d(),[d]),h.useEffect(()=>{if(c&&p){const b=w=>y(w,p),v=w=>y(w,c);return c.addEventListener("pointerleave",b),p.addEventListener("pointerleave",v),()=>{c.removeEventListener("pointerleave",b),p.removeEventListener("pointerleave",v)}}},[c,p,y,d]),h.useEffect(()=>{if(i){const b=v=>{const w=v.target,m={x:v.clientX,y:v.clientY},g=(c==null?void 0:c.contains(w))||(p==null?void 0:p.contains(w)),x=!_j(m,i);g?d():x&&(d(),u())};return document.addEventListener("pointermove",b),()=>document.removeEventListener("pointermove",b)}},[c,p,i,u,d]),a.jsx(Bv,E(N({},e),{ref:s}))}),[jj,Ej]=yl(zv,{isInside:!1}),Bv=h.forwardRef((e,t)=>{const f=e,{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i}=f,l=D(f,["__scopeTooltip","children","aria-label","onEscapeKeyDown","onPointerDownOutside"]),c=xl(Go,n),u=gf(n),{onClose:p}=c;return h.useEffect(()=>(document.addEventListener(Eh,p),()=>document.removeEventListener(Eh,p)),[p]),h.useEffect(()=>{if(c.trigger){const d=y=>{const b=y.target;b!=null&&b.contains(c.trigger)&&p()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[c.trigger,p]),a.jsx(ci,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:d=>d.preventDefault(),onDismiss:p,children:a.jsxs(Mv,E(N(N({"data-state":c.stateAttribute},u),l),{ref:t,style:E(N({},l.style),{"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"}),children:[a.jsx(T0,{children:r}),a.jsx(jj,{scope:n,isInside:!0,children:a.jsx(oS,{id:c.contentId,role:"tooltip",children:o||r})})]}))})});Uv.displayName=Go;var Wv="TooltipArrow",kj=h.forwardRef((e,t)=>{const i=e,{__scopeTooltip:n}=i,r=D(i,["__scopeTooltip"]),o=gf(n);return Ej(Wv,n).isInside?null:a.jsx(Iv,E(N(N({},o),r),{ref:t}))});kj.displayName=Wv;function Pj(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function Tj(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function Rj(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function _j(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const l=t[s].x,c=t[s].y,u=t[i].x,p=t[i].y;c>r!=p>r&&n<(u-l)*(r-c)/(p-c)+l&&(o=!o)}return o}function Oj(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),Aj(t)}function Aj(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var Lj=$v,Hv=Uv;const Mj=Lj,Ij=h.forwardRef((o,r)=>{var s=o,{className:e,sideOffset:t=4}=s,n=D(s,["className","sideOffset"]);return a.jsx(Hv,N({ref:r,sideOffset:t,className:Le("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e)},n))});Ij.displayName=Hv.displayName;var fi=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},Br=typeof window=="undefined"||"Deno"in globalThis;function _t(){}function Dj(e,t){return typeof e=="function"?e(t):e}function Lu(e){return typeof e=="number"&&e>=0&&e!==1/0}function Vv(e,t){return Math.max(e+(t||0)-Date.now(),0)}function jo(e,t){return typeof e=="function"?e(t):e}function Vt(e,t){return typeof e=="function"?e(t):e}function kh(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:l}=e;if(i){if(r){if(t.queryHash!==vf(i,t.options))return!1}else if(!Xs(t.queryKey,i))return!1}if(n!=="all"){const c=t.isActive();if(n==="active"&&!c||n==="inactive"&&c)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||s&&!s(t))}function Ph(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(Ys(t.options.mutationKey)!==Ys(s))return!1}else if(!Xs(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function vf(e,t){return((t==null?void 0:t.queryKeyHashFn)||Ys)(e)}function Ys(e){return JSON.stringify(e,(t,n)=>Iu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Xs(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Xs(e[n],t[n])):!1}function Qv(e,t){if(e===t)return e;const n=Th(e)&&Th(t);if(n||Iu(e)&&Iu(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,l=n?[]:{};let c=0;for(let u=0;u<i;u++){const p=n?u:s[u];(!n&&r.includes(p)||n)&&e[p]===void 0&&t[p]===void 0?(l[p]=void 0,c++):(l[p]=Qv(e[p],t[p]),l[p]===e[p]&&e[p]!==void 0&&c++)}return o===i&&c===o?e:l}return t}function Mu(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function Th(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Iu(e){if(!Rh(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!Rh(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function Rh(e){return Object.prototype.toString.call(e)==="[object Object]"}function Fj(e){return new Promise(t=>{setTimeout(t,e)})}function Du(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?Qv(e,t):t}function $j(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function zj(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var yf=Symbol();function Kv(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===yf?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var kr,Un,ko,om,Uj=(om=class extends fi{constructor(){super();G(this,kr);G(this,Un);G(this,ko);H(this,ko,t=>{if(!Br&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){j(this,Un)||this.setEventListener(j(this,ko))}onUnsubscribe(){var t;this.hasListeners()||((t=j(this,Un))==null||t.call(this),H(this,Un,void 0))}setEventListener(t){var n;H(this,ko,t),(n=j(this,Un))==null||n.call(this),H(this,Un,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){j(this,kr)!==t&&(H(this,kr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof j(this,kr)=="boolean"?j(this,kr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},kr=new WeakMap,Un=new WeakMap,ko=new WeakMap,om),xf=new Uj,Po,Bn,To,sm,Bj=(sm=class extends fi{constructor(){super();G(this,Po,!0);G(this,Bn);G(this,To);H(this,To,t=>{if(!Br&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){j(this,Bn)||this.setEventListener(j(this,To))}onUnsubscribe(){var t;this.hasListeners()||((t=j(this,Bn))==null||t.call(this),H(this,Bn,void 0))}setEventListener(t){var n;H(this,To,t),(n=j(this,Bn))==null||n.call(this),H(this,Bn,t(this.setOnline.bind(this)))}setOnline(t){j(this,Po)!==t&&(H(this,Po,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return j(this,Po)}},Po=new WeakMap,Bn=new WeakMap,To=new WeakMap,sm),Ba=new Bj;function Fu(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function Wj(e){return Math.min(1e3*Kf(2,e),3e4)}function qv(e){return(e!=null?e:"online")==="online"?Ba.isOnline():!0}var Gv=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function yc(e){return e instanceof Gv}function Yv(e){let t=!1,n=0,r=!1,o;const s=Fu(),i=v=>{var w;r||(d(new Gv(v)),(w=e.abort)==null||w.call(e))},l=()=>{t=!0},c=()=>{t=!1},u=()=>xf.isFocused()&&(e.networkMode==="always"||Ba.isOnline())&&e.canRun(),p=()=>qv(e.networkMode)&&e.canRun(),f=v=>{var w;r||(r=!0,(w=e.onSuccess)==null||w.call(e,v),o==null||o(),s.resolve(v))},d=v=>{var w;r||(r=!0,(w=e.onError)==null||w.call(e,v),o==null||o(),s.reject(v))},y=()=>new Promise(v=>{var w;o=m=>{(r||u())&&v(m)},(w=e.onPause)==null||w.call(e)}).then(()=>{var v;o=void 0,r||(v=e.onContinue)==null||v.call(e)}),b=()=>{if(r)return;let v;const w=n===0?e.initialPromise:void 0;try{v=w!=null?w:e.fn()}catch(m){v=Promise.reject(m)}Promise.resolve(v).then(f).catch(m=>{var T,k,R;if(r)return;const g=(T=e.retry)!=null?T:Br?0:3,x=(k=e.retryDelay)!=null?k:Wj,S=typeof x=="function"?x(n,m):x,C=g===!0||typeof g=="number"&&n<g||typeof g=="function"&&g(n,m);if(t||!C){d(m);return}n++,(R=e.onFail)==null||R.call(e,n,m),Fj(S).then(()=>u()?void 0:y()).then(()=>{t?d(m):b()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:l,continueRetry:c,canStart:p,start:()=>(p()?b():y().then(b),s)}}function Hj(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=l=>setTimeout(l,0);const s=l=>{t?e.push(l):o(()=>{n(l)})},i=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(c=>{n(c)})})})};return{batch:l=>{let c;t++;try{c=l()}finally{t--,t||i()}return c},batchCalls:l=>(...c)=>{s(()=>{l(...c)})},schedule:s,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var He=Hj(),Pr,im,Xv=(im=class{constructor(){G(this,Pr)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Lu(this.gcTime)&&H(this,Pr,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e!=null?e:Br?1/0:5*60*1e3)}clearGcTimeout(){j(this,Pr)&&(clearTimeout(j(this,Pr)),H(this,Pr,void 0))}},Pr=new WeakMap,im),Ro,_o,Rt,Je,ei,Tr,Bt,mn,am,Vj=(am=class extends Xv{constructor(t){var n;super();G(this,Bt);G(this,Ro);G(this,_o);G(this,Rt);G(this,Je);G(this,ei);G(this,Tr);H(this,Tr,!1),H(this,ei,t.defaultOptions),this.setOptions(t.options),this.observers=[],H(this,Rt,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,H(this,Ro,Qj(this.options)),this.state=(n=t.state)!=null?n:j(this,Ro),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=j(this,Je))==null?void 0:t.promise}setOptions(t){this.options=N(N({},j(this,ei)),t),this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&j(this,Rt).remove(this)}setData(t,n){const r=Du(this.state.data,t,this.options);return re(this,Bt,mn).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){re(this,Bt,mn).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=j(this,Je))==null?void 0:r.promise;return(o=j(this,Je))==null||o.cancel(t),n?n.then(_t).catch(_t):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(j(this,Ro))}isActive(){return this.observers.some(t=>Vt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===yf||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Vv(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=j(this,Je))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=j(this,Je))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),j(this,Rt).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(j(this,Je)&&(j(this,Tr)?j(this,Je).cancel({revert:!0}):j(this,Je).cancelRetry()),this.scheduleGc()),j(this,Rt).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||re(this,Bt,mn).call(this,{type:"invalidate"})}fetch(t,n){var c,u,p;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(j(this,Je))return j(this,Je).continueRetry(),j(this,Je).promise}if(t&&this.setOptions(t),!this.options.queryFn){const f=this.observers.find(d=>d.options.queryFn);f&&this.setOptions(f.options)}const r=new AbortController,o=f=>{Object.defineProperty(f,"signal",{enumerable:!0,get:()=>(H(this,Tr,!0),r.signal)})},s=()=>{const f=Kv(this.options,n),d={queryKey:this.queryKey,meta:this.meta};return o(d),H(this,Tr,!1),this.options.persister?this.options.persister(f,d,this):f(d)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};o(i),(c=this.options.behavior)==null||c.onFetch(i,this),H(this,_o,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((u=i.fetchOptions)==null?void 0:u.meta))&&re(this,Bt,mn).call(this,{type:"fetch",meta:(p=i.fetchOptions)==null?void 0:p.meta});const l=f=>{var d,y,b,v;yc(f)&&f.silent||re(this,Bt,mn).call(this,{type:"error",error:f}),yc(f)||((y=(d=j(this,Rt).config).onError)==null||y.call(d,f,this),(v=(b=j(this,Rt).config).onSettled)==null||v.call(b,this.state.data,f,this)),this.scheduleGc()};return H(this,Je,Yv({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:f=>{var d,y,b,v;if(f===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(f)}catch(w){l(w);return}(y=(d=j(this,Rt).config).onSuccess)==null||y.call(d,f,this),(v=(b=j(this,Rt).config).onSettled)==null||v.call(b,f,this.state.error,this),this.scheduleGc()},onError:l,onFail:(f,d)=>{re(this,Bt,mn).call(this,{type:"failed",failureCount:f,error:d})},onPause:()=>{re(this,Bt,mn).call(this,{type:"pause"})},onContinue:()=>{re(this,Bt,mn).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),j(this,Je).start()}},Ro=new WeakMap,_o=new WeakMap,Rt=new WeakMap,Je=new WeakMap,ei=new WeakMap,Tr=new WeakMap,Bt=new WeakSet,mn=function(t){const n=r=>{var o,s;switch(t.type){case"failed":return E(N({},r),{fetchFailureCount:t.failureCount,fetchFailureReason:t.error});case"pause":return E(N({},r),{fetchStatus:"paused"});case"continue":return E(N({},r),{fetchStatus:"fetching"});case"fetch":return E(N(N({},r),Zv(r.data,this.options)),{fetchMeta:(o=t.meta)!=null?o:null});case"success":return N(E(N({},r),{data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:(s=t.dataUpdatedAt)!=null?s:Date.now(),error:null,isInvalidated:!1,status:"success"}),!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null});case"error":const i=t.error;return yc(i)&&i.revert&&j(this,_o)?E(N({},j(this,_o)),{fetchStatus:"idle"}):E(N({},r),{error:i,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:i,fetchStatus:"idle",status:"error"});case"invalidate":return E(N({},r),{isInvalidated:!0});case"setState":return N(N({},r),t.state)}};this.state=n(this.state),He.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),j(this,Rt).notify({query:this,type:"updated",action:t})})},am);function Zv(e,t){return N({fetchFailureCount:0,fetchFailureReason:null,fetchStatus:qv(t.networkMode)?"fetching":"paused"},e===void 0&&{error:null,status:"pending"})}function Qj(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r!=null?r:Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var nn,lm,Kj=(lm=class extends fi{constructor(t={}){super();G(this,nn);this.config=t,H(this,nn,new Map)}build(t,n,r){var l;const o=n.queryKey,s=(l=n.queryHash)!=null?l:vf(o,n);let i=this.get(s);return i||(i=new Vj({cache:this,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){j(this,nn).has(t.queryHash)||(j(this,nn).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=j(this,nn).get(t.queryHash);n&&(t.destroy(),n===t&&j(this,nn).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){He.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return j(this,nn).get(t)}getAll(){return[...j(this,nn).values()]}find(t){const n=N({exact:!0},t);return this.getAll().find(r=>kh(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>kh(t,r)):n}notify(t){He.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){He.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){He.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},nn=new WeakMap,lm),rn,rt,Rr,on,Mn,cm,qj=(cm=class extends Xv{constructor(t){super();G(this,on);G(this,rn);G(this,rt);G(this,Rr);this.mutationId=t.mutationId,H(this,rt,t.mutationCache),H(this,rn,[]),this.state=t.state||Gj(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){j(this,rn).includes(t)||(j(this,rn).push(t),this.clearGcTimeout(),j(this,rt).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){H(this,rn,j(this,rn).filter(n=>n!==t)),this.scheduleGc(),j(this,rt).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){j(this,rn).length||(this.state.status==="pending"?this.scheduleGc():j(this,rt).remove(this))}continue(){var t,n;return(n=(t=j(this,Rr))==null?void 0:t.continue())!=null?n:this.execute(this.state.variables)}execute(t){return se(this,null,function*(){var o,s,i,l,c,u,p,f,d,y,b,v,w,m,g,x,S,C,T,k,R;H(this,Rr,Yv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(_,O)=>{re(this,on,Mn).call(this,{type:"failed",failureCount:_,error:O})},onPause:()=>{re(this,on,Mn).call(this,{type:"pause"})},onContinue:()=>{re(this,on,Mn).call(this,{type:"continue"})},retry:(o=this.options.retry)!=null?o:0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>j(this,rt).canRun(this)}));const n=this.state.status==="pending",r=!j(this,Rr).canStart();try{if(!n){re(this,on,Mn).call(this,{type:"pending",variables:t,isPaused:r}),yield(i=(s=j(this,rt).config).onMutate)==null?void 0:i.call(s,t,this);const O=yield(c=(l=this.options).onMutate)==null?void 0:c.call(l,t);O!==this.state.context&&re(this,on,Mn).call(this,{type:"pending",context:O,variables:t,isPaused:r})}const _=yield j(this,Rr).start();return yield(p=(u=j(this,rt).config).onSuccess)==null?void 0:p.call(u,_,t,this.state.context,this),yield(d=(f=this.options).onSuccess)==null?void 0:d.call(f,_,t,this.state.context),yield(b=(y=j(this,rt).config).onSettled)==null?void 0:b.call(y,_,null,this.state.variables,this.state.context,this),yield(w=(v=this.options).onSettled)==null?void 0:w.call(v,_,null,t,this.state.context),re(this,on,Mn).call(this,{type:"success",data:_}),_}catch(_){try{throw yield(g=(m=j(this,rt).config).onError)==null?void 0:g.call(m,_,t,this.state.context,this),yield(S=(x=this.options).onError)==null?void 0:S.call(x,_,t,this.state.context),yield(T=(C=j(this,rt).config).onSettled)==null?void 0:T.call(C,void 0,_,this.state.variables,this.state.context,this),yield(R=(k=this.options).onSettled)==null?void 0:R.call(k,void 0,_,t,this.state.context),_}finally{re(this,on,Mn).call(this,{type:"error",error:_})}}finally{j(this,rt).runNext(this)}})}},rn=new WeakMap,rt=new WeakMap,Rr=new WeakMap,on=new WeakSet,Mn=function(t){const n=r=>{switch(t.type){case"failed":return E(N({},r),{failureCount:t.failureCount,failureReason:t.error});case"pause":return E(N({},r),{isPaused:!0});case"continue":return E(N({},r),{isPaused:!1});case"pending":return E(N({},r),{context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()});case"success":return E(N({},r),{data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1});case"error":return E(N({},r),{data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"})}};this.state=n(this.state),He.batch(()=>{j(this,rn).forEach(r=>{r.onMutationUpdate(t)}),j(this,rt).notify({mutation:this,type:"updated",action:t})})},cm);function Gj(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var vt,ti,um,Yj=(um=class extends fi{constructor(t={}){super();G(this,vt);G(this,ti);this.config=t,H(this,vt,new Map),H(this,ti,Date.now())}build(t,n,r){const o=new qj({mutationCache:this,mutationId:++xi(this,ti)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){var o;const n=Bi(t),r=(o=j(this,vt).get(n))!=null?o:[];r.push(t),j(this,vt).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=Bi(t);if(j(this,vt).has(n)){const o=(r=j(this,vt).get(n))==null?void 0:r.filter(s=>s!==t);o&&(o.length===0?j(this,vt).delete(n):j(this,vt).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=j(this,vt).get(Bi(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r,o;const n=(r=j(this,vt).get(Bi(t)))==null?void 0:r.find(s=>s!==t&&s.state.isPaused);return(o=n==null?void 0:n.continue())!=null?o:Promise.resolve()}clear(){He.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...j(this,vt).values()].flat()}find(t){const n=N({exact:!0},t);return this.getAll().find(r=>Ph(n,r))}findAll(t={}){return this.getAll().filter(n=>Ph(t,n))}notify(t){He.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return He.batch(()=>Promise.all(t.map(n=>n.continue().catch(_t))))}},vt=new WeakMap,ti=new WeakMap,um);function Bi(e){var t,n;return(n=(t=e.options.scope)==null?void 0:t.id)!=null?n:String(e.mutationId)}function _h(e){return{onFetch:(t,n)=>{var p,f,d,y,b;const r=t.options,o=(d=(f=(p=t.fetchOptions)==null?void 0:p.meta)==null?void 0:f.fetchMore)==null?void 0:d.direction,s=((y=t.state.data)==null?void 0:y.pages)||[],i=((b=t.state.data)==null?void 0:b.pageParams)||[];let l={pages:[],pageParams:[]},c=0;const u=()=>se(this,null,function*(){var x;let v=!1;const w=S=>{Object.defineProperty(S,"signal",{enumerable:!0,get:()=>(t.signal.aborted?v=!0:t.signal.addEventListener("abort",()=>{v=!0}),t.signal)})},m=Kv(t.options,t.fetchOptions),g=(S,C,T)=>se(this,null,function*(){if(v)return Promise.reject();if(C==null&&S.pages.length)return Promise.resolve(S);const k={queryKey:t.queryKey,pageParam:C,direction:T?"backward":"forward",meta:t.options.meta};w(k);const R=yield m(k),{maxPages:_}=t.options,O=T?zj:$j;return{pages:O(S.pages,R,_),pageParams:O(S.pageParams,C,_)}});if(o&&s.length){const S=o==="backward",C=S?Xj:Oh,T={pages:s,pageParams:i},k=C(r,T);l=yield g(T,k,S)}else{const S=e!=null?e:s.length;do{const C=c===0?(x=i[0])!=null?x:r.initialPageParam:Oh(r,l);if(c>0&&C==null)break;l=yield g(l,C),c++}while(c<S)}return l});t.options.persister?t.fetchFn=()=>{var v,w;return(w=(v=t.options).persister)==null?void 0:w.call(v,u,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=u}}}function Oh(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function Xj(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var Re,Wn,Hn,Oo,Ao,Vn,Lo,Mo,dm,Zj=(dm=class{constructor(e={}){G(this,Re);G(this,Wn);G(this,Hn);G(this,Oo);G(this,Ao);G(this,Vn);G(this,Lo);G(this,Mo);H(this,Re,e.queryCache||new Kj),H(this,Wn,e.mutationCache||new Yj),H(this,Hn,e.defaultOptions||{}),H(this,Oo,new Map),H(this,Ao,new Map),H(this,Vn,0)}mount(){xi(this,Vn)._++,j(this,Vn)===1&&(H(this,Lo,xf.subscribe(e=>se(this,null,function*(){e&&(yield this.resumePausedMutations(),j(this,Re).onFocus())}))),H(this,Mo,Ba.subscribe(e=>se(this,null,function*(){e&&(yield this.resumePausedMutations(),j(this,Re).onOnline())}))))}unmount(){var e,t;xi(this,Vn)._--,j(this,Vn)===0&&((e=j(this,Lo))==null||e.call(this),H(this,Lo,void 0),(t=j(this,Mo))==null||t.call(this),H(this,Mo,void 0))}isFetching(e){return j(this,Re).findAll(E(N({},e),{fetchStatus:"fetching"})).length}isMutating(e){return j(this,Wn).findAll(E(N({},e),{status:"pending"})).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=j(this,Re).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=j(this,Re).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(jo(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return j(this,Re).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=j(this,Re).get(r.queryHash),s=o==null?void 0:o.state.data,i=Dj(t,s);if(i!==void 0)return j(this,Re).build(this,r).setData(i,E(N({},n),{manual:!0}))}setQueriesData(e,t,n){return He.batch(()=>j(this,Re).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=j(this,Re).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=j(this,Re);He.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=j(this,Re),r=N({type:"active"},e);return He.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n=N({revert:!0},t),r=He.batch(()=>j(this,Re).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(_t).catch(_t)}invalidateQueries(e={},t={}){return He.batch(()=>{var r,o;if(j(this,Re).findAll(e).forEach(s=>{s.invalidate()}),e.refetchType==="none")return Promise.resolve();const n=E(N({},e),{type:(o=(r=e.refetchType)!=null?r:e.type)!=null?o:"active"});return this.refetchQueries(n,t)})}refetchQueries(e={},t){var o;const n=E(N({},t),{cancelRefetch:(o=t==null?void 0:t.cancelRefetch)!=null?o:!0}),r=He.batch(()=>j(this,Re).findAll(e).filter(s=>!s.isDisabled()).map(s=>{let i=s.fetch(void 0,n);return n.throwOnError||(i=i.catch(_t)),s.state.fetchStatus==="paused"?Promise.resolve():i}));return Promise.all(r).then(_t)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=j(this,Re).build(this,t);return n.isStaleByTime(jo(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(_t).catch(_t)}fetchInfiniteQuery(e){return e.behavior=_h(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(_t).catch(_t)}ensureInfiniteQueryData(e){return e.behavior=_h(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return Ba.isOnline()?j(this,Wn).resumePausedMutations():Promise.resolve()}getQueryCache(){return j(this,Re)}getMutationCache(){return j(this,Wn)}getDefaultOptions(){return j(this,Hn)}setDefaultOptions(e){H(this,Hn,e)}setQueryDefaults(e,t){j(this,Oo).set(Ys(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...j(this,Oo).values()];let n={};return t.forEach(r=>{Xs(e,r.queryKey)&&(n=N(N({},n),r.defaultOptions))}),n}setMutationDefaults(e,t){j(this,Ao).set(Ys(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...j(this,Ao).values()];let n={};return t.forEach(r=>{Xs(e,r.mutationKey)&&(n=N(N({},n),r.defaultOptions))}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t=E(N(N(N({},j(this,Hn).queries),this.getQueryDefaults(e.queryKey)),e),{_defaulted:!0});return t.queryHash||(t.queryHash=vf(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===yf&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:E(N(N(N({},j(this,Hn).mutations),(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey)),e),{_defaulted:!0})}clear(){j(this,Re).clear(),j(this,Wn).clear()}},Re=new WeakMap,Wn=new WeakMap,Hn=new WeakMap,Oo=new WeakMap,Ao=new WeakMap,Vn=new WeakMap,Lo=new WeakMap,Mo=new WeakMap,dm),dt,oe,ni,ot,_r,Io,Qn,sn,ri,Do,Fo,Or,Ar,Kn,$o,fe,xs,$u,zu,Uu,Bu,Wu,Hu,Vu,Jv,fm,Jj=(fm=class extends fi{constructor(t,n){super();G(this,fe);G(this,dt);G(this,oe);G(this,ni);G(this,ot);G(this,_r);G(this,Io);G(this,Qn);G(this,sn);G(this,ri);G(this,Do);G(this,Fo);G(this,Or);G(this,Ar);G(this,Kn);G(this,$o,new Set);this.options=n,H(this,dt,t),H(this,sn,null),H(this,Qn,Fu()),this.options.experimental_prefetchInRender||j(this,Qn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(j(this,oe).addObserver(this),Ah(j(this,oe),this.options)?re(this,fe,xs).call(this):this.updateResult(),re(this,fe,Bu).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Qu(j(this,oe),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Qu(j(this,oe),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,re(this,fe,Wu).call(this),re(this,fe,Hu).call(this),j(this,oe).removeObserver(this)}setOptions(t,n){const r=this.options,o=j(this,oe);if(this.options=j(this,dt).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof Vt(this.options.enabled,j(this,oe))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");re(this,fe,Vu).call(this),j(this,oe).setOptions(this.options),r._defaulted&&!Mu(this.options,r)&&j(this,dt).getQueryCache().notify({type:"observerOptionsUpdated",query:j(this,oe),observer:this});const s=this.hasListeners();s&&Lh(j(this,oe),o,this.options,r)&&re(this,fe,xs).call(this),this.updateResult(n),s&&(j(this,oe)!==o||Vt(this.options.enabled,j(this,oe))!==Vt(r.enabled,j(this,oe))||jo(this.options.staleTime,j(this,oe))!==jo(r.staleTime,j(this,oe)))&&re(this,fe,$u).call(this);const i=re(this,fe,zu).call(this);s&&(j(this,oe)!==o||Vt(this.options.enabled,j(this,oe))!==Vt(r.enabled,j(this,oe))||i!==j(this,Kn))&&re(this,fe,Uu).call(this,i)}getOptimisticResult(t){const n=j(this,dt).getQueryCache().build(j(this,dt),t),r=this.createResult(n,t);return t2(this,r)&&(H(this,ot,r),H(this,Io,this.options),H(this,_r,j(this,oe).state)),r}getCurrentResult(){return j(this,ot)}trackResult(t,n){const r={};return Object.keys(t).forEach(o=>{Object.defineProperty(r,o,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(o),n==null||n(o),t[o])})}),r}trackProp(t){j(this,$o).add(t)}getCurrentQuery(){return j(this,oe)}refetch(n={}){var t=D(n,[]);return this.fetch(N({},t))}fetchOptimistic(t){const n=j(this,dt).defaultQueryOptions(t),r=j(this,dt).getQueryCache().build(j(this,dt),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){var n;return re(this,fe,xs).call(this,E(N({},t),{cancelRefetch:(n=t.cancelRefetch)!=null?n:!0})).then(()=>(this.updateResult(),j(this,ot)))}createResult(t,n){var R;const r=j(this,oe),o=this.options,s=j(this,ot),i=j(this,_r),l=j(this,Io),u=t!==r?t.state:j(this,ni),{state:p}=t;let f=N({},p),d=!1,y;if(n._optimisticResults){const _=this.hasListeners(),O=!_&&Ah(t,n),B=_&&Lh(t,r,n,o);(O||B)&&(f=N(N({},f),Zv(p.data,t.options))),n._optimisticResults==="isRestoring"&&(f.fetchStatus="idle")}let{error:b,errorUpdatedAt:v,status:w}=f;if(n.select&&f.data!==void 0)if(s&&f.data===(i==null?void 0:i.data)&&n.select===j(this,ri))y=j(this,Do);else try{H(this,ri,n.select),y=n.select(f.data),y=Du(s==null?void 0:s.data,y,n),H(this,Do,y),H(this,sn,null)}catch(_){H(this,sn,_)}else y=f.data;if(n.placeholderData!==void 0&&y===void 0&&w==="pending"){let _;if(s!=null&&s.isPlaceholderData&&n.placeholderData===(l==null?void 0:l.placeholderData))_=s.data;else if(_=typeof n.placeholderData=="function"?n.placeholderData((R=j(this,Fo))==null?void 0:R.state.data,j(this,Fo)):n.placeholderData,n.select&&_!==void 0)try{_=n.select(_),H(this,sn,null)}catch(O){H(this,sn,O)}_!==void 0&&(w="success",y=Du(s==null?void 0:s.data,_,n),d=!0)}j(this,sn)&&(b=j(this,sn),y=j(this,Do),v=Date.now(),w="error");const m=f.fetchStatus==="fetching",g=w==="pending",x=w==="error",S=g&&m,C=y!==void 0,k={status:w,fetchStatus:f.fetchStatus,isPending:g,isSuccess:w==="success",isError:x,isInitialLoading:S,isLoading:S,data:y,dataUpdatedAt:f.dataUpdatedAt,error:b,errorUpdatedAt:v,failureCount:f.fetchFailureCount,failureReason:f.fetchFailureReason,errorUpdateCount:f.errorUpdateCount,isFetched:f.dataUpdateCount>0||f.errorUpdateCount>0,isFetchedAfterMount:f.dataUpdateCount>u.dataUpdateCount||f.errorUpdateCount>u.errorUpdateCount,isFetching:m,isRefetching:m&&!g,isLoadingError:x&&!C,isPaused:f.fetchStatus==="paused",isPlaceholderData:d,isRefetchError:x&&C,isStale:wf(t,n),refetch:this.refetch,promise:j(this,Qn)};if(this.options.experimental_prefetchInRender){const _=L=>{k.status==="error"?L.reject(k.error):k.data!==void 0&&L.resolve(k.data)},O=()=>{const L=H(this,Qn,k.promise=Fu());_(L)},B=j(this,Qn);switch(B.status){case"pending":t.queryHash===r.queryHash&&_(B);break;case"fulfilled":(k.status==="error"||k.data!==B.value)&&O();break;case"rejected":(k.status!=="error"||k.error!==B.reason)&&O();break}}return k}updateResult(t){const n=j(this,ot),r=this.createResult(j(this,oe),this.options);if(H(this,_r,j(this,oe).state),H(this,Io,this.options),j(this,_r).data!==void 0&&H(this,Fo,j(this,oe)),Mu(r,n))return;H(this,ot,r);const o={},s=()=>{if(!n)return!0;const{notifyOnChangeProps:i}=this.options,l=typeof i=="function"?i():i;if(l==="all"||!l&&!j(this,$o).size)return!0;const c=new Set(l!=null?l:j(this,$o));return this.options.throwOnError&&c.add("error"),Object.keys(j(this,ot)).some(u=>{const p=u;return j(this,ot)[p]!==n[p]&&c.has(p)})};(t==null?void 0:t.listeners)!==!1&&s()&&(o.listeners=!0),re(this,fe,Jv).call(this,N(N({},o),t))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&re(this,fe,Bu).call(this)}},dt=new WeakMap,oe=new WeakMap,ni=new WeakMap,ot=new WeakMap,_r=new WeakMap,Io=new WeakMap,Qn=new WeakMap,sn=new WeakMap,ri=new WeakMap,Do=new WeakMap,Fo=new WeakMap,Or=new WeakMap,Ar=new WeakMap,Kn=new WeakMap,$o=new WeakMap,fe=new WeakSet,xs=function(t){re(this,fe,Vu).call(this);let n=j(this,oe).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(_t)),n},$u=function(){re(this,fe,Wu).call(this);const t=jo(this.options.staleTime,j(this,oe));if(Br||j(this,ot).isStale||!Lu(t))return;const r=Vv(j(this,ot).dataUpdatedAt,t)+1;H(this,Or,setTimeout(()=>{j(this,ot).isStale||this.updateResult()},r))},zu=function(){var t;return(t=typeof this.options.refetchInterval=="function"?this.options.refetchInterval(j(this,oe)):this.options.refetchInterval)!=null?t:!1},Uu=function(t){re(this,fe,Hu).call(this),H(this,Kn,t),!(Br||Vt(this.options.enabled,j(this,oe))===!1||!Lu(j(this,Kn))||j(this,Kn)===0)&&H(this,Ar,setInterval(()=>{(this.options.refetchIntervalInBackground||xf.isFocused())&&re(this,fe,xs).call(this)},j(this,Kn)))},Bu=function(){re(this,fe,$u).call(this),re(this,fe,Uu).call(this,re(this,fe,zu).call(this))},Wu=function(){j(this,Or)&&(clearTimeout(j(this,Or)),H(this,Or,void 0))},Hu=function(){j(this,Ar)&&(clearInterval(j(this,Ar)),H(this,Ar,void 0))},Vu=function(){const t=j(this,dt).getQueryCache().build(j(this,dt),this.options);if(t===j(this,oe))return;const n=j(this,oe);H(this,oe,t),H(this,ni,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},Jv=function(t){He.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(j(this,ot))}),j(this,dt).getQueryCache().notify({query:j(this,oe),type:"observerResultsUpdated"})})},fm);function e2(e,t){return Vt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function Ah(e,t){return e2(e,t)||e.state.data!==void 0&&Qu(e,t,t.refetchOnMount)}function Qu(e,t,n){if(Vt(t.enabled,e)!==!1){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&wf(e,t)}return!1}function Lh(e,t,n,r){return(e!==t||Vt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&wf(e,n)}function wf(e,t){return Vt(t.enabled,e)!==!1&&e.isStaleByTime(jo(t.staleTime,e))}function t2(e,t){return!Mu(e.getCurrentResult(),t)}var ey=h.createContext(void 0),n2=e=>{const t=h.useContext(ey);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},r2=({client:e,children:t})=>(h.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),a.jsx(ey.Provider,{value:e,children:t})),ty=h.createContext(!1),o2=()=>h.useContext(ty);ty.Provider;function s2(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var i2=h.createContext(s2()),a2=()=>h.useContext(i2);function l2(e,t){return typeof e=="function"?e(...t):!!e}function c2(){}var u2=(e,t)=>{(e.suspense||e.throwOnError)&&(t.isReset()||(e.retryOnMount=!1))},d2=e=>{h.useEffect(()=>{e.clearReset()},[e])},f2=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&l2(n,[e.error,r]),p2=e=>{e.suspense&&(e.staleTime===void 0&&(e.staleTime=1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},h2=(e,t)=>e.isLoading&&e.isFetching&&!t,m2=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,Mh=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function g2(e,t,n){var p,f,d,y,b;const r=n2(),o=o2(),s=a2(),i=r.defaultQueryOptions(e);(f=(p=r.getDefaultOptions().queries)==null?void 0:p._experimental_beforeQuery)==null||f.call(p,i),i._optimisticResults=o?"isRestoring":"optimistic",p2(i),u2(i,s),d2(s);const l=!r.getQueryCache().get(i.queryHash),[c]=h.useState(()=>new t(r,i)),u=c.getOptimisticResult(i);if(h.useSyncExternalStore(h.useCallback(v=>{const w=o?()=>{}:c.subscribe(He.batchCalls(v));return c.updateResult(),w},[c,o]),()=>c.getCurrentResult(),()=>c.getCurrentResult()),h.useEffect(()=>{c.setOptions(i,{listeners:!1})},[i,c]),m2(i,u))throw Mh(i,c,s);if(f2({result:u,errorResetBoundary:s,throwOnError:i.throwOnError,query:r.getQueryCache().get(i.queryHash)}))throw u.error;if((y=(d=r.getDefaultOptions().queries)==null?void 0:d._experimental_afterQuery)==null||y.call(d,i,u),i.experimental_prefetchInRender&&!Br&&h2(u,o)){const v=l?Mh(i,c,s):(b=r.getQueryCache().get(i.queryHash))==null?void 0:b.promise;v==null||v.catch(c2).finally(()=>{c.updateResult()})}return i.notifyOnChangeProps?u:c.trackResult(u)}function pi(e,t){return g2(e,Jj)}/**
 * @remix-run/router v1.20.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Zs(){return Zs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Zs.apply(this,arguments)}var Yn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(Yn||(Yn={}));const Ih="popstate";function v2(e){e===void 0&&(e={});function t(r,o){let{pathname:s,search:i,hash:l}=r.location;return Ku("",{pathname:s,search:i,hash:l},o.state&&o.state.usr||null,o.state&&o.state.key||"default")}function n(r,o){return typeof o=="string"?o:Wa(o)}return x2(t,n,null,e)}function Ie(e,t){if(e===!1||e===null||typeof e=="undefined")throw new Error(t)}function ny(e,t){if(!e){typeof console!="undefined"&&console.warn(t);try{throw new Error(t)}catch(n){}}}function y2(){return Math.random().toString(36).substr(2,8)}function Dh(e,t){return{usr:e.state,key:e.key,idx:t}}function Ku(e,t,n,r){return n===void 0&&(n=null),Zs({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?rs(t):t,{state:n,key:t&&t.key||r||y2()})}function Wa(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function rs(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function x2(e,t,n,r){r===void 0&&(r={});let{window:o=document.defaultView,v5Compat:s=!1}=r,i=o.history,l=Yn.Pop,c=null,u=p();u==null&&(u=0,i.replaceState(Zs({},i.state,{idx:u}),""));function p(){return(i.state||{idx:null}).idx}function f(){l=Yn.Pop;let w=p(),m=w==null?null:w-u;u=w,c&&c({action:l,location:v.location,delta:m})}function d(w,m){l=Yn.Push;let g=Ku(v.location,w,m);u=p()+1;let x=Dh(g,u),S=v.createHref(g);try{i.pushState(x,"",S)}catch(C){if(C instanceof DOMException&&C.name==="DataCloneError")throw C;o.location.assign(S)}s&&c&&c({action:l,location:v.location,delta:1})}function y(w,m){l=Yn.Replace;let g=Ku(v.location,w,m);u=p();let x=Dh(g,u),S=v.createHref(g);i.replaceState(x,"",S),s&&c&&c({action:l,location:v.location,delta:0})}function b(w){let m=o.location.origin!=="null"?o.location.origin:o.location.href,g=typeof w=="string"?w:Wa(w);return g=g.replace(/ $/,"%20"),Ie(m,"No window.location.(origin|href) available to create URL for href: "+g),new URL(g,m)}let v={get action(){return l},get location(){return e(o,i)},listen(w){if(c)throw new Error("A history only accepts one active listener");return o.addEventListener(Ih,f),c=w,()=>{o.removeEventListener(Ih,f),c=null}},createHref(w){return t(o,w)},createURL:b,encodeLocation(w){let m=b(w);return{pathname:m.pathname,search:m.search,hash:m.hash}},push:d,replace:y,go(w){return i.go(w)}};return v}var Fh;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(Fh||(Fh={}));function w2(e,t,n){return n===void 0&&(n="/"),b2(e,t,n,!1)}function b2(e,t,n,r){let o=typeof t=="string"?rs(t):t,s=bf(o.pathname||"/",n);if(s==null)return null;let i=ry(e);S2(i);let l=null;for(let c=0;l==null&&c<i.length;++c){let u=A2(s);l=_2(i[c],u,r)}return l}function ry(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let o=(s,i,l)=>{let c={relativePath:l===void 0?s.path||"":l,caseSensitive:s.caseSensitive===!0,childrenIndex:i,route:s};c.relativePath.startsWith("/")&&(Ie(c.relativePath.startsWith(r),'Absolute route path "'+c.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),c.relativePath=c.relativePath.slice(r.length));let u=sr([r,c.relativePath]),p=n.concat(c);s.children&&s.children.length>0&&(Ie(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),ry(s.children,t,p,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:T2(u,s.index),routesMeta:p})};return e.forEach((s,i)=>{var l;if(s.path===""||!((l=s.path)!=null&&l.includes("?")))o(s,i);else for(let c of oy(s.path))o(s,i,c)}),t}function oy(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,o=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return o?[s,""]:[s];let i=oy(r.join("/")),l=[];return l.push(...i.map(c=>c===""?s:[s,c].join("/"))),o&&l.push(...i),l.map(c=>e.startsWith("/")&&c===""?"/":c)}function S2(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:R2(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const N2=/^:[\w-]+$/,C2=3,j2=2,E2=1,k2=10,P2=-2,$h=e=>e==="*";function T2(e,t){let n=e.split("/"),r=n.length;return n.some($h)&&(r+=P2),t&&(r+=j2),n.filter(o=>!$h(o)).reduce((o,s)=>o+(N2.test(s)?C2:s===""?E2:k2),r)}function R2(e,t){return e.length===t.length&&e.slice(0,-1).every((r,o)=>r===t[o])?e[e.length-1]-t[t.length-1]:0}function _2(e,t,n){let{routesMeta:r}=e,o={},s="/",i=[];for(let l=0;l<r.length;++l){let c=r[l],u=l===r.length-1,p=s==="/"?t:t.slice(s.length)||"/",f=zh({path:c.relativePath,caseSensitive:c.caseSensitive,end:u},p),d=c.route;if(!f&&u&&n&&!r[r.length-1].route.index&&(f=zh({path:c.relativePath,caseSensitive:c.caseSensitive,end:!1},p)),!f)return null;Object.assign(o,f.params),i.push({params:o,pathname:sr([s,f.pathname]),pathnameBase:D2(sr([s,f.pathnameBase])),route:d}),f.pathnameBase!=="/"&&(s=sr([s,f.pathnameBase]))}return i}function zh(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=O2(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let s=o[0],i=s.replace(/(.)\/+$/,"$1"),l=o.slice(1);return{params:r.reduce((u,p,f)=>{let{paramName:d,isOptional:y}=p;if(d==="*"){let v=l[f]||"";i=s.slice(0,s.length-v.length).replace(/(.)\/+$/,"$1")}const b=l[f];return y&&!b?u[d]=void 0:u[d]=(b||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:i,pattern:e}}function O2(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),ny(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(i,l,c)=>(r.push({paramName:l,isOptional:c!=null}),c?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),o+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":e!==""&&e!=="/"&&(o+="(?:(?=\\/|$))"),[new RegExp(o,t?void 0:"i"),r]}function A2(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return ny(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function bf(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function L2(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:o=""}=typeof e=="string"?rs(e):e;return{pathname:n?n.startsWith("/")?n:M2(n,t):t,search:F2(r),hash:$2(o)}}function M2(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(o=>{o===".."?n.length>1&&n.pop():o!=="."&&n.push(o)}),n.length>1?n.join("/"):"/"}function xc(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function I2(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function sy(e,t){let n=I2(e);return t?n.map((r,o)=>o===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function iy(e,t,n,r){r===void 0&&(r=!1);let o;typeof e=="string"?o=rs(e):(o=Zs({},e),Ie(!o.pathname||!o.pathname.includes("?"),xc("?","pathname","search",o)),Ie(!o.pathname||!o.pathname.includes("#"),xc("#","pathname","hash",o)),Ie(!o.search||!o.search.includes("#"),xc("#","search","hash",o)));let s=e===""||o.pathname==="",i=s?"/":o.pathname,l;if(i==null)l=n;else{let f=t.length-1;if(!r&&i.startsWith("..")){let d=i.split("/");for(;d[0]==="..";)d.shift(),f-=1;o.pathname=d.join("/")}l=f>=0?t[f]:"/"}let c=L2(o,l),u=i&&i!=="/"&&i.endsWith("/"),p=(s||i===".")&&n.endsWith("/");return!c.pathname.endsWith("/")&&(u||p)&&(c.pathname+="/"),c}const sr=e=>e.join("/").replace(/\/\/+/g,"/"),D2=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),F2=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,$2=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function z2(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const ay=["post","put","patch","delete"];new Set(ay);const U2=["get",...ay];new Set(U2);/**
 * React Router v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Js(){return Js=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Js.apply(this,arguments)}const Sf=h.createContext(null),B2=h.createContext(null),qr=h.createContext(null),wl=h.createContext(null),mr=h.createContext({outlet:null,matches:[],isDataRoute:!1}),ly=h.createContext(null);function W2(e,t){let{relative:n}=t===void 0?{}:t;hi()||Ie(!1);let{basename:r,navigator:o}=h.useContext(qr),{hash:s,pathname:i,search:l}=uy(e,{relative:n}),c=i;return r!=="/"&&(c=i==="/"?r:sr([r,i])),o.createHref({pathname:c,search:l,hash:s})}function hi(){return h.useContext(wl)!=null}function gr(){return hi()||Ie(!1),h.useContext(wl).location}function cy(e){h.useContext(qr).static||h.useLayoutEffect(e)}function Nf(){let{isDataRoute:e}=h.useContext(mr);return e?rE():H2()}function H2(){hi()||Ie(!1);let e=h.useContext(Sf),{basename:t,future:n,navigator:r}=h.useContext(qr),{matches:o}=h.useContext(mr),{pathname:s}=gr(),i=JSON.stringify(sy(o,n.v7_relativeSplatPath)),l=h.useRef(!1);return cy(()=>{l.current=!0}),h.useCallback(function(u,p){if(p===void 0&&(p={}),!l.current)return;if(typeof u=="number"){r.go(u);return}let f=iy(u,JSON.parse(i),s,p.relative==="path");e==null&&t!=="/"&&(f.pathname=f.pathname==="/"?t:sr([t,f.pathname])),(p.replace?r.replace:r.push)(f,p.state,p)},[t,r,i,s,e])}function V2(){let{matches:e}=h.useContext(mr),t=e[e.length-1];return t?t.params:{}}function uy(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=h.useContext(qr),{matches:o}=h.useContext(mr),{pathname:s}=gr(),i=JSON.stringify(sy(o,r.v7_relativeSplatPath));return h.useMemo(()=>iy(e,JSON.parse(i),s,n==="path"),[e,i,s,n])}function Q2(e,t){return K2(e,t)}function K2(e,t,n,r){hi()||Ie(!1);let{navigator:o}=h.useContext(qr),{matches:s}=h.useContext(mr),i=s[s.length-1],l=i?i.params:{};i&&i.pathname;let c=i?i.pathnameBase:"/";i&&i.route;let u=gr(),p;if(t){var f;let w=typeof t=="string"?rs(t):t;c==="/"||(f=w.pathname)!=null&&f.startsWith(c)||Ie(!1),p=w}else p=u;let d=p.pathname||"/",y=d;if(c!=="/"){let w=c.replace(/^\//,"").split("/");y="/"+d.replace(/^\//,"").split("/").slice(w.length).join("/")}let b=w2(e,{pathname:y}),v=Z2(b&&b.map(w=>Object.assign({},w,{params:Object.assign({},l,w.params),pathname:sr([c,o.encodeLocation?o.encodeLocation(w.pathname).pathname:w.pathname]),pathnameBase:w.pathnameBase==="/"?c:sr([c,o.encodeLocation?o.encodeLocation(w.pathnameBase).pathname:w.pathnameBase])})),s,n,r);return t&&v?h.createElement(wl.Provider,{value:{location:Js({pathname:"/",search:"",hash:"",state:null,key:"default"},p),navigationType:Yn.Pop}},v):v}function q2(){let e=nE(),t=z2(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,o={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return h.createElement(h.Fragment,null,h.createElement("h2",null,"Unexpected Application Error!"),h.createElement("h3",{style:{fontStyle:"italic"}},t),n?h.createElement("pre",{style:o},n):null,null)}const G2=h.createElement(q2,null);class Y2 extends h.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?h.createElement(mr.Provider,{value:this.props.routeContext},h.createElement(ly.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function X2(e){let{routeContext:t,match:n,children:r}=e,o=h.useContext(Sf);return o&&o.static&&o.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=n.route.id),h.createElement(mr.Provider,{value:t},r)}function Z2(e,t,n,r){var o;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let i=e,l=(o=n)==null?void 0:o.errors;if(l!=null){let p=i.findIndex(f=>f.route.id&&(l==null?void 0:l[f.route.id])!==void 0);p>=0||Ie(!1),i=i.slice(0,Math.min(i.length,p+1))}let c=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let p=0;p<i.length;p++){let f=i[p];if((f.route.HydrateFallback||f.route.hydrateFallbackElement)&&(u=p),f.route.id){let{loaderData:d,errors:y}=n,b=f.route.loader&&d[f.route.id]===void 0&&(!y||y[f.route.id]===void 0);if(f.route.lazy||b){c=!0,u>=0?i=i.slice(0,u+1):i=[i[0]];break}}}return i.reduceRight((p,f,d)=>{let y,b=!1,v=null,w=null;n&&(y=l&&f.route.id?l[f.route.id]:void 0,v=f.route.errorElement||G2,c&&(u<0&&d===0?(b=!0,w=null):u===d&&(b=!0,w=f.route.hydrateFallbackElement||null)));let m=t.concat(i.slice(0,d+1)),g=()=>{let x;return y?x=v:b?x=w:f.route.Component?x=h.createElement(f.route.Component,null):f.route.element?x=f.route.element:x=p,h.createElement(X2,{match:f,routeContext:{outlet:p,matches:m,isDataRoute:n!=null},children:x})};return n&&(f.route.ErrorBoundary||f.route.errorElement||d===0)?h.createElement(Y2,{location:n.location,revalidation:n.revalidation,component:v,error:y,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()},null)}var dy=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(dy||{}),Ha=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Ha||{});function J2(e){let t=h.useContext(Sf);return t||Ie(!1),t}function eE(e){let t=h.useContext(B2);return t||Ie(!1),t}function tE(e){let t=h.useContext(mr);return t||Ie(!1),t}function fy(e){let t=tE(),n=t.matches[t.matches.length-1];return n.route.id||Ie(!1),n.route.id}function nE(){var e;let t=h.useContext(ly),n=eE(Ha.UseRouteError),r=fy(Ha.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function rE(){let{router:e}=J2(dy.UseNavigateStable),t=fy(Ha.UseNavigateStable),n=h.useRef(!1);return cy(()=>{n.current=!0}),h.useCallback(function(o,s){s===void 0&&(s={}),n.current&&(typeof o=="number"?e.navigate(o):e.navigate(o,Js({fromRouteId:t},s)))},[e,t])}function gn(e){Ie(!1)}function oE(e){let{basename:t="/",children:n=null,location:r,navigationType:o=Yn.Pop,navigator:s,static:i=!1,future:l}=e;hi()&&Ie(!1);let c=t.replace(/^\/*/,"/"),u=h.useMemo(()=>({basename:c,navigator:s,static:i,future:Js({v7_relativeSplatPath:!1},l)}),[c,l,s,i]);typeof r=="string"&&(r=rs(r));let{pathname:p="/",search:f="",hash:d="",state:y=null,key:b="default"}=r,v=h.useMemo(()=>{let w=bf(p,c);return w==null?null:{location:{pathname:w,search:f,hash:d,state:y,key:b},navigationType:o}},[c,p,f,d,y,b,o]);return v==null?null:h.createElement(qr.Provider,{value:u},h.createElement(wl.Provider,{children:n,value:v}))}function sE(e){let{children:t,location:n}=e;return Q2(qu(t),n)}new Promise(()=>{});function qu(e,t){t===void 0&&(t=[]);let n=[];return h.Children.forEach(e,(r,o)=>{if(!h.isValidElement(r))return;let s=[...t,o];if(r.type===h.Fragment){n.push.apply(n,qu(r.props.children,s));return}r.type!==gn&&Ie(!1),!r.props.index||!r.props.children||Ie(!1);let i={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(i.children=qu(r.props.children,s)),n.push(i)}),n}/**
 * React Router DOM v6.27.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Gu(){return Gu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Gu.apply(this,arguments)}function iE(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function aE(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function lE(e,t){return e.button===0&&(!t||t==="_self")&&!aE(e)}function Yu(e){return e===void 0&&(e=""),new URLSearchParams(typeof e=="string"||Array.isArray(e)||e instanceof URLSearchParams?e:Object.keys(e).reduce((t,n)=>{let r=e[n];return t.concat(Array.isArray(r)?r.map(o=>[n,o]):[[n,r]])},[]))}function cE(e,t){let n=Yu(e);return t&&t.forEach((r,o)=>{n.has(o)||t.getAll(o).forEach(s=>{n.append(o,s)})}),n}const uE=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],dE="6";try{window.__reactRouterVersion=dE}catch(e){}const fE="startTransition",Uh=Nm[fE];function pE(e){let{basename:t,children:n,future:r,window:o}=e,s=h.useRef();s.current==null&&(s.current=v2({window:o,v5Compat:!0}));let i=s.current,[l,c]=h.useState({action:i.action,location:i.location}),{v7_startTransition:u}=r||{},p=h.useCallback(f=>{u&&Uh?Uh(()=>c(f)):c(f)},[c,u]);return h.useLayoutEffect(()=>i.listen(p),[i,p]),h.createElement(oE,{basename:t,children:n,location:l.location,navigationType:l.action,navigator:i,future:r})}const hE=typeof window!="undefined"&&typeof window.document!="undefined"&&typeof window.document.createElement!="undefined",mE=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,Ae=h.forwardRef(function(t,n){let{onClick:r,relative:o,reloadDocument:s,replace:i,state:l,target:c,to:u,preventScrollReset:p,viewTransition:f}=t,d=iE(t,uE),{basename:y}=h.useContext(qr),b,v=!1;if(typeof u=="string"&&mE.test(u)&&(b=u,hE))try{let x=new URL(window.location.href),S=u.startsWith("//")?new URL(x.protocol+u):new URL(u),C=bf(S.pathname,y);S.origin===x.origin&&C!=null?u=C+S.search+S.hash:v=!0}catch(x){}let w=W2(u,{relative:o}),m=gE(u,{replace:i,state:l,target:c,preventScrollReset:p,relative:o,viewTransition:f});function g(x){r&&r(x),x.defaultPrevented||m(x)}return h.createElement("a",Gu({},d,{href:b||w,onClick:v||s?r:g,ref:n,target:c}))});var Bh;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Bh||(Bh={}));var Wh;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(Wh||(Wh={}));function gE(e,t){let{target:n,replace:r,state:o,preventScrollReset:s,relative:i,viewTransition:l}=t===void 0?{}:t,c=Nf(),u=gr(),p=uy(e,{relative:i});return h.useCallback(f=>{if(lE(f,n)){f.preventDefault();let d=r!==void 0?r:Wa(u)===Wa(p);c(e,{replace:d,state:o,preventScrollReset:s,relative:i,viewTransition:l})}},[u,c,p,r,o,n,e,s,i,l])}function vE(e){let t=h.useRef(Yu(e)),n=h.useRef(!1),r=gr(),o=h.useMemo(()=>cE(r.search,n.current?null:t.current),[r.search]),s=Nf(),i=h.useCallback((l,c)=>{const u=Yu(typeof l=="function"?l(o):l);n.current=!0,s("?"+u,c)},[s,o]);return[o,i]}const py="/build/assets/logo-DJ4UPdSy.png",yE=()=>{const[e,t]=h.useState(!1),[n,r]=h.useState(""),[o,s]=h.useState(!1),[i,l]=h.useState(!1),c=Nf(),u=gr();h.useEffect(()=>{const y=()=>{s(window.scrollY>20)};return window.addEventListener("scroll",y),()=>window.removeEventListener("scroll",y)},[]);const p=y=>{y.preventDefault(),n.trim()&&(c(`/search?q=${encodeURIComponent(n.trim())}`),r(""),l(!1))},f=[{to:"/",label:"Home"},{to:"/about",label:"About"},{to:"/products",label:"Products"},{to:"/catalogs",label:"Catalogs"},{to:"/contact",label:"Contact"}],d=y=>u.pathname===y;return a.jsx("header",{className:`sticky top-0 z-50 transition-all duration-300 ${o?"bg-white/95 backdrop-blur-md shadow-xl border-b border-gray-200/50":"bg-white/90 backdrop-blur-sm shadow-lg border-b border-gray-200"}`,children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"flex items-center justify-between h-20",children:[a.jsx(Ae,{to:"/",className:"flex items-center space-x-3 group",children:a.jsx("img",{src:py,alt:"RELIFE Logo",className:"h-12 w-auto object-contain group-hover:scale-105 transition-transform duration-300"})}),a.jsx("nav",{className:"hidden lg:flex items-center space-x-2",children:f.map(y=>a.jsxs(Ae,{to:y.to,className:`relative font-medium transition-all duration-300 group px-4 py-2 rounded-xl ${d(y.to)?"text-blue-600 bg-blue-50 shadow-sm":"text-gray-700 hover:text-blue-600 hover:bg-blue-50/50"}`,style:{color:d(y.to)?"#3b4d66":void 0},children:[y.label,a.jsx("span",{className:`absolute -bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 transition-all duration-300 rounded-full ${d(y.to)?"w-8":"w-0 group-hover:w-8"}`,style:{background:"linear-gradient(135deg, #27ae60 0%, #3b4d66 100%)"}})]},y.to))}),a.jsx("div",{className:"hidden md:flex items-center space-x-4",children:a.jsx("form",{onSubmit:p,className:"relative",children:a.jsxs("div",{className:`relative transition-all duration-300 ${i?"scale-105":""}`,children:[a.jsx("input",{type:"text",placeholder:"Search products...",value:n,onChange:y=>r(y.target.value),onFocus:()=>l(!0),onBlur:()=>l(!1),className:`w-80 pl-12 pr-4 py-3 border rounded-2xl transition-all duration-300 text-sm bg-white/80 backdrop-blur-sm ${i?"border-blue-400 ring-4 ring-blue-100 shadow-lg":"border-gray-300 hover:border-blue-300 shadow-sm"}`,"aria-label":"Search products"}),a.jsx(Da,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 transition-colors duration-300",style:{color:i?"#3b4d66":"#6b7280"}})]})})}),a.jsx("button",{className:"lg:hidden p-3 rounded-xl bg-white/80 backdrop-blur-sm border border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-all duration-300 shadow-sm hover:shadow-md",onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?a.jsx(sf,{className:"w-6 h-6",style:{color:"#3b4d66"}}):a.jsx(US,{className:"w-6 h-6",style:{color:"#3b4d66"}})})]}),e&&a.jsxs("div",{className:"lg:hidden py-6 border-t border-blue-100 bg-blue-50/30",children:[a.jsx("nav",{className:"flex flex-col space-y-4",children:f.map(y=>a.jsx(Ae,{to:y.to,className:"text-gray-700 hover:text-blue-900 transition-colors duration-200 font-semibold text-sm uppercase tracking-wide px-2 py-1",onClick:()=>t(!1),children:y.label},y.to))}),a.jsx("form",{onSubmit:p,className:"mt-6",children:a.jsxs("div",{className:"relative",children:[a.jsx("input",{type:"text",placeholder:"Search medical products...",value:n,onChange:y=>r(y.target.value),className:"w-full pl-11 pr-4 py-3 border border-blue-200 rounded-xl focus:ring-2 focus:ring-blue-900 focus:border-transparent bg-white text-sm","aria-label":"Search products"}),a.jsx(Da,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-blue-600 w-5 h-5"})]})})]})]})})},xE="/build/assets/logoWhite-D--vAvjf.png",wE=()=>{const e=()=>{window.scrollTo({top:0,behavior:"smooth"})};return a.jsxs("footer",{className:"bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900 text-white relative overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-20",children:[a.jsx("div",{className:"absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-1000"}),a.jsx("div",{className:"absolute bottom-1/4 left-1/3 w-1 h-1 bg-white rounded-full animate-pulse delay-2000"}),a.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-3000"})]}),a.jsxs("div",{className:"container mx-auto px-4 py-20 relative z-10",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-12 max-w-6xl mx-auto",children:[a.jsxs("div",{className:"space-y-6 md:col-span-1",children:[a.jsx("div",{className:"flex items-center space-x-3",children:a.jsx("img",{src:xE,alt:"RELIFE Logo",className:"h-14 w-auto object-contain"})}),a.jsx("p",{className:"text-gray-300 text-sm leading-relaxed",children:"Pioneering the future of prosthetic technology with innovative solutions designed for enhanced mobility, comfort, and quality of life."}),a.jsxs("div",{className:"flex space-x-3",children:[a.jsx("a",{href:"#",className:"group",children:a.jsx("div",{className:"w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110",children:a.jsx(MS,{className:"w-5 h-5"})})}),a.jsx("a",{href:"#",className:"group",children:a.jsx("div",{className:"w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110",children:a.jsx(KS,{className:"w-5 h-5"})})}),a.jsx("a",{href:"#",className:"group",children:a.jsx("div",{className:"w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110",children:a.jsx(FS,{className:"w-5 h-5"})})}),a.jsx("a",{href:"#",className:"group",children:a.jsx("div",{className:"w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110",children:a.jsx($S,{className:"w-5 h-5"})})})]})]}),a.jsxs("div",{className:"text-center md:text-left",children:[a.jsx("h3",{className:"text-lg font-bold mb-6 text-blue-200",children:"Navigation"}),a.jsxs("ul",{className:"space-y-3",children:[a.jsx("li",{children:a.jsx(Ae,{to:"/",className:"text-gray-300 hover:text-white transition-colors text-sm font-medium",children:"Home"})}),a.jsx("li",{children:a.jsx(Ae,{to:"/about",className:"text-gray-300 hover:text-white transition-colors text-sm font-medium",children:"About Us"})}),a.jsx("li",{children:a.jsx(Ae,{to:"/products",className:"text-gray-300 hover:text-white transition-colors text-sm font-medium",children:"Products"})}),a.jsx("li",{children:a.jsx(Ae,{to:"/catalogs",className:"text-gray-300 hover:text-white transition-colors text-sm font-medium",children:"Catalogs"})})]})]}),a.jsxs("div",{className:"text-center md:text-left md:col-span-1 lg:col-span-1",children:[a.jsx("h3",{className:"text-lg font-bold mb-6 text-blue-200",children:"Contact Information"}),a.jsxs("div",{className:"space-y-4 text-gray-300 text-sm max-w-xs mx-auto md:max-w-none md:mx-0",children:[a.jsxs("div",{className:"flex items-start space-x-3 justify-center md:justify-start",children:[a.jsx(sv,{className:"w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0"}),a.jsxs("div",{className:"min-w-0 flex-1",children:[a.jsx("p",{className:"font-medium",children:"RELIFE INC"}),a.jsx("p",{children:"EDGEROOK DR"}),a.jsx("p",{children:"TORONTO ON M9V 5E8 CANADA"})]})]}),a.jsxs("div",{className:"flex items-center space-x-3 justify-center md:justify-start",children:[a.jsx(Eu,{className:"w-5 h-5 text-blue-400 flex-shrink-0"}),a.jsxs("div",{className:"min-w-0 flex-1",children:[a.jsx("a",{href:"tel:+16476466640",className:"font-medium hover:text-blue-300 transition-colors",children:"+****************"}),a.jsx("p",{className:"text-xs text-blue-300",children:"24/7 Technical Support"})]})]}),a.jsxs("div",{className:"flex items-center space-x-3 justify-center md:justify-start",children:[a.jsx(la,{className:"w-5 h-5 text-blue-400 flex-shrink-0"}),a.jsxs("div",{className:"min-w-0 flex-1",children:[a.jsx("a",{href:"mailto:<EMAIL>",className:"font-medium hover:text-blue-300 transition-colors",children:"<EMAIL>"}),a.jsx("p",{className:"text-xs text-blue-300",children:"Business Inquiries"})]})]})]})]})]}),a.jsx("div",{className:"border-t border-gray-700/50 mt-16 pt-8",children:a.jsxs("div",{className:"flex flex-col items-center justify-center space-y-6",children:[a.jsx("div",{className:"text-center text-gray-400 text-sm",children:a.jsxs("p",{className:"flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-2",children:[a.jsxs("span",{children:["© ",new Date().getFullYear()," RELIFE Inc. All rights reserved."]}),a.jsx("span",{className:"hidden md:inline",children:"•"}),a.jsxs("span",{className:"flex items-center space-x-1",children:[a.jsx("span",{children:"Made with"}),a.jsx(Sr,{className:"w-4 h-4 text-red-400 fill-current"}),a.jsx("span",{children:"for better lives"})]})]})}),a.jsx("div",{className:"text-center text-gray-500 text-xs",children:a.jsxs("p",{className:"flex items-center justify-center space-x-1",children:[a.jsx("span",{children:"Developed by"}),a.jsx("a",{href:"https://synapta.art/",target:"_blank",rel:"noopener noreferrer",className:"text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium underline decoration-blue-400/30 hover:decoration-blue-300/50 underline-offset-2",children:"Synapta.art"})]})})]})})]}),a.jsx("button",{onClick:e,className:"fixed bottom-8 right-8 w-14 h-14 bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 z-50 group","aria-label":"Scroll to top",children:a.jsx(ES,{className:"w-6 h-6 mx-auto group-hover:-translate-y-1 transition-transform duration-300"})})]})},bE=()=>{const{pathname:e}=gr();return h.useEffect(()=>{window.scrollTo(0,0),document.documentElement.scrollTop=0,document.body.scrollTop=0},[e]),null},SE=nv("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function NE(r){var o=r,{className:e,variant:t}=o,n=D(o,["className","variant"]);return a.jsx("div",N({className:Le(SE({variant:t}),e)},n))}const Cf=({product:e})=>{const[t,n]=h.useState(!1),[r,o]=h.useState(0),s=e.images&&e.images.length>0?e.images:[e.image],i=s[r],l=u=>{u.preventDefault(),u.stopPropagation(),o(p=>(p+1)%s.length)},c=u=>{u.preventDefault(),u.stopPropagation(),o(p=>(p-1+s.length)%s.length)};return a.jsx("div",{className:"group relative h-full",children:a.jsx(Ae,{to:`/product/${e.id}`,className:"block h-full",children:a.jsxs("div",{className:"medical-card group-hover:border-blue-200/70 relative overflow-hidden h-full flex flex-col",children:[a.jsxs("div",{className:"relative aspect-w-1 aspect-h-1 bg-gradient-to-br from-blue-50 to-gray-50 overflow-hidden rounded-t-2xl",children:[a.jsx("img",{src:i,alt:`${e.title} - Image ${r+1}`,className:`w-full h-64 object-cover transition-all duration-700 ${t?"opacity-100 scale-100":"opacity-0 scale-110"} group-hover:scale-110`,onLoad:()=>n(!0)}),s.length>1&&a.jsxs(a.Fragment,{children:[a.jsx("button",{onClick:c,className:"absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white hover:scale-110 z-10",children:a.jsx(PS,{className:"w-4 h-4 text-gray-700"})}),a.jsx("button",{onClick:l,className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-white/80 backdrop-blur-sm rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white hover:scale-110 z-10",children:a.jsx(TS,{className:"w-4 h-4 text-gray-700"})}),a.jsx("div",{className:"absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-all duration-300",children:s.map((u,p)=>a.jsx("button",{onClick:f=>{f.preventDefault(),f.stopPropagation(),o(p)},className:`w-2 h-2 rounded-full transition-all duration-300 ${p===r?"bg-white scale-125":"bg-white/60 hover:bg-white/80"}`},p))})]}),s.length>1&&a.jsxs("div",{className:"absolute top-4 left-4 bg-black/50 backdrop-blur-sm text-white text-xs px-2 py-1 rounded-full",children:[r+1,"/",s.length]}),a.jsxs("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300",children:[a.jsx("div",{className:"absolute top-4 right-4",children:a.jsx("button",{className:"p-2 rounded-full bg-white/80 backdrop-blur-sm text-gray-600 hover:bg-white hover:text-blue-600 transition-all duration-300",children:a.jsx(LS,{className:"w-4 h-4"})})}),a.jsx("div",{className:"absolute bottom-4 left-4 right-4",children:a.jsx("button",{className:"w-full py-2 px-4 bg-white/90 backdrop-blur-sm text-blue-600 font-semibold rounded-xl opacity-0 group-hover:opacity-100 transform translate-y-4 group-hover:translate-y-0 transition-all duration-300 hover:bg-white",children:"Quick View"})})]})]}),a.jsxs("div",{className:"p-6 flex-1 flex flex-col",children:[a.jsx("div",{className:"mb-4",children:a.jsx(NE,{variant:"secondary",className:"bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 font-semibold text-xs px-3 py-1.5 rounded-full",children:e.category})}),a.jsx("h3",{className:"text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors leading-tight line-clamp-2",children:e.title}),a.jsx("p",{className:"text-gray-600 text-sm line-clamp-3 leading-relaxed mb-4 flex-1",children:e.description}),a.jsxs("div",{className:"mt-auto",children:[a.jsx("div",{className:"mb-6",children:a.jsx("span",{className:"text-xl font-bold bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",children:e.price})}),a.jsx("div",{className:"w-full h-1 bg-gray-200 rounded-full overflow-hidden",children:a.jsx("div",{className:"h-full bg-gradient-to-r from-blue-600 to-blue-700 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left"})})]})]})]})})})},hy="/api/v1";function St(e,t){return se(this,null,function*(){const n=`${hy}${e}`,{method:r="GET",body:o}=t||{};try{const s=yield fetch(n,N({method:r,headers:{Accept:"application/json","Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},credentials:"include"},o&&{body:JSON.stringify(o)}));if(!s.ok){let i=`API request failed: ${s.status} ${s.statusText}`;try{const l=yield s.json();console.error(`API Error ${s.status}:`,l),s.status===422&&l.errors?i=`Validation Error: ${Object.values(l.errors).flat().join(", ")}`:l.message&&(i=l.message)}catch(l){const c=yield s.text();console.error(`API Error ${s.status}:`,c),(s.status===419||c.includes("CSRF"))&&(i="CSRF token mismatch. Please refresh the page and try again.")}throw new Error(i)}return s.json()}catch(s){throw console.error("API Request Error:",s),s instanceof TypeError&&s.message.includes("fetch")?new Error("Cannot connect to backend server. Please ensure the backend is running."):s}})}const my={getProducts:e=>se(Ve,null,function*(){const t=new URLSearchParams;e!=null&&e.page&&t.append("page",e.page.toString()),e!=null&&e.per_page&&t.append("per_page",e.per_page.toString()),e!=null&&e.sort&&t.append("sort",e.sort),e!=null&&e.filter&&Object.entries(e.filter).forEach(([o,s])=>{t.append(`filter[${o}]`,s)});const n=t.toString(),r=`/products${n?`?${n}`:""}`;return St(r)}),getFeaturedProducts:()=>se(Ve,null,function*(){return St("/products/featured")}),getProduct:e=>se(Ve,null,function*(){return St(`/products/${e}`)}),searchProducts:(e,t)=>se(Ve,null,function*(){const n=new URLSearchParams;return n.append("q",e),t!=null&&t.page&&n.append("page",t.page.toString()),t!=null&&t.per_page&&n.append("per_page",t.per_page.toString()),St(`/products/search?${n.toString()}`)})},CE={getCategories:()=>se(Ve,null,function*(){return St("/categories")}),getCategory:e=>se(Ve,null,function*(){return St(`/categories/${e}`)}),getCategoryProducts:(e,t)=>se(Ve,null,function*(){const n=new URLSearchParams;return t!=null&&t.page&&n.append("page",t.page.toString()),t!=null&&t.per_page&&n.append("per_page",t.per_page.toString()),St(`/categories/${e}/products?${n.toString()}`)})},jE=e=>{const t=e.images&&e.images.length>0,n=["/product1.jpg","/product2.jpg","/product3.jpg","/product4.jpg"],r=n[0];return{id:e.id.toString(),title:e.name,description:e.description,price:e.price?`$${e.price}`:"Contact for Pricing",category:e.category.name,image:t?e.images[0].url:r,images:t?e.images.map(o=>o.url):n,specs:e.specifications||{}}},gy=e=>e.map(jE),vy={getCatalogs:e=>se(Ve,null,function*(){const t=new URLSearchParams;e!=null&&e.category&&t.append("category",e.category),e!=null&&e.search&&t.append("search",e.search);const n=t.toString(),r=`/catalogs${n?`?${n}`:""}`;return St(r)}),getCatalog:e=>se(Ve,null,function*(){return St(`/catalogs/${e}`)}),getCategories:()=>se(Ve,null,function*(){return St("/catalogs/categories")}),getDownloadUrl:e=>`${hy}/catalogs/${e}/download`},EE={getConfig:()=>se(Ve,null,function*(){return St("/config",{method:"GET"})}),getRecaptchaConfig:()=>se(Ve,null,function*(){return St("/config/recaptcha",{method:"GET"})})},kE={submitContactForm:e=>se(Ve,null,function*(){return St("/contact",{method:"POST",body:e})})},PE=e=>pi({queryKey:["products",e],queryFn:()=>my.getProducts(e),staleTime:5*60*1e3,gcTime:10*60*1e3}),TE=()=>pi({queryKey:["products","featured"],queryFn:()=>my.getFeaturedProducts(),staleTime:5*60*1e3,gcTime:10*60*1e3}),RE=()=>pi({queryKey:["categories"],queryFn:()=>CE.getCategories(),staleTime:10*60*1e3,gcTime:30*60*1e3}),yy=e=>{const t=PE(e);return E(N({},t),{data:t.data?E(N({},t.data),{data:gy(t.data.data)}):void 0})},_E=()=>{const e=TE();return E(N({},e),{data:e.data?gy(e.data.data):void 0})},OE=e=>pi({queryKey:["catalogs",e],queryFn:()=>vy.getCatalogs(e),staleTime:10*60*1e3,gcTime:30*60*1e3}),AE=()=>pi({queryKey:["catalog-categories"],queryFn:()=>vy.getCategories(),staleTime:30*60*1e3,gcTime:60*60*1e3}),hn=({className:e="",variant:t="rectangular"})=>{const n="animate-pulse bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%]",r={text:"h-4 rounded",rectangular:"rounded-lg",circular:"rounded-full"};return a.jsx("div",{className:`${n} ${r[t]} ${e}`})},jf=({count:e=3})=>a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:Array.from({length:e}).map((t,n)=>a.jsxs("div",{className:"modern-card p-0 overflow-hidden",children:[a.jsx(hn,{className:"h-64 rounded-t-2xl rounded-b-none"}),a.jsxs("div",{className:"p-6 space-y-4",children:[a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx(hn,{className:"h-6 w-20 rounded-full"}),a.jsx("div",{className:"flex space-x-1",children:Array.from({length:5}).map((r,o)=>a.jsx(hn,{variant:"circular",className:"w-3 h-3"},o))})]}),a.jsx(hn,{className:"h-6 w-3/4"}),a.jsxs("div",{className:"space-y-2",children:[a.jsx(hn,{className:"h-4 w-full"}),a.jsx(hn,{className:"h-4 w-2/3"})]}),a.jsxs("div",{className:"flex items-center justify-between",children:[a.jsx(hn,{className:"h-6 w-24"}),a.jsx(hn,{className:"h-4 w-20"})]}),a.jsx(hn,{className:"h-1 w-full rounded-full"})]})]},n))}),LE=()=>{const[e,t]=h.useState(!1),{data:n,isLoading:r,error:o}=_E();h.useEffect(()=>{t(!0),window.scrollTo(0,0)},[]);const s=[{icon:Er,title:"Approved",description:"All our products meet the highest safety and quality standards"},{icon:Sr,title:"Patient-Centered",description:"Designed with comfort and user experience as our top priority"},{icon:QS,title:"Advanced Technology",description:"Cutting-edge materials and engineering for superior performance"},{icon:ju,title:"24/7 Support",description:"Round-the-clock technical support and maintenance services"}];return a.jsxs("div",{className:"min-h-screen",children:[a.jsxs("section",{className:"relative min-h-screen flex items-center justify-center overflow-hidden",children:[a.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"}),a.jsxs("div",{className:"absolute inset-0 opacity-30",children:[a.jsx("div",{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-blue-300 rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-1000"}),a.jsx("div",{className:"absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse delay-2000"}),a.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-1 h-1 bg-blue-600 rounded-full animate-pulse delay-3000"})]}),a.jsx("div",{className:"container mx-auto px-4 relative z-10",children:a.jsxs("div",{className:`max-w-6xl mx-auto text-center transition-all duration-1000 ${e?"opacity-100 translate-y-0":"opacity-0 translate-y-10"}`,children:[a.jsxs("h1",{className:"text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight",children:["Advanced Prosthetic Solutions for"," ",a.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",children:"Better Living"})]}),a.jsx("p",{className:"text-xl md:text-2xl text-gray-600 mb-12 leading-relaxed max-w-4xl mx-auto",children:"Discover cutting-edge prosthetic technology designed to restore mobility, independence, and confidence. Our premium solutions combine innovation with compassionate care."}),a.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center mb-16",children:[a.jsxs(Ae,{to:"/products",className:"medical-button group flex items-center justify-center space-x-2",children:[a.jsx("span",{children:"Explore Products"}),a.jsx(ch,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"})]}),a.jsx(Ae,{to:"/contact",className:"group flex items-center justify-center space-x-3 border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300",children:a.jsx("span",{children:"Contact Us"})})]})]})}),a.jsx("div",{className:"absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse"}),a.jsx("div",{className:"absolute bottom-20 right-10 w-32 h-32 bg-blue-300 rounded-full opacity-20 animate-pulse delay-1000"})]}),a.jsxs("section",{className:"medical-section bg-white relative overflow-hidden",children:[a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"text-center mb-20",children:[a.jsxs("h2",{className:"text-4xl md:text-5xl font-bold mb-6",style:{color:"#27ae60"},children:["Why Choose"," ",a.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",children:"RELIFE?"})]}),a.jsx("p",{className:"text-xl max-w-3xl mx-auto leading-relaxed",style:{color:"#6b7280"},children:"We're committed to providing the highest quality prosthetic solutions with advanced technology and personalized care."})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:s.map((i,l)=>a.jsx("div",{className:"group",children:a.jsxs("div",{className:"modern-card p-8 text-center h-full",children:[a.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300",children:a.jsx(i.icon,{className:"w-8 h-8",style:{color:"#3b4d66"}})}),a.jsx("h3",{className:"text-xl font-bold mb-4",style:{color:"#27ae60"},children:i.title}),a.jsx("p",{className:"leading-relaxed",style:{color:"#6b7280"},children:i.description})]})},l))})]}),a.jsx("div",{className:"absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-20 transform translate-x-32 -translate-y-32"})]}),a.jsx("section",{className:"medical-section bg-gradient-to-br from-gray-50 to-blue-50/30",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"text-center mb-20",children:[a.jsx("span",{className:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6",children:"Our Products"}),a.jsxs("h2",{className:"text-4xl md:text-5xl font-bold mb-6",style:{color:"#27ae60"},children:["Featured"," ",a.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",children:"Products"})]}),a.jsx("p",{className:"text-xl max-w-3xl mx-auto leading-relaxed",style:{color:"#6b7280"},children:"Explore our most popular prosthetic solutions designed for comfort, durability, and natural movement."})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 mb-16",children:r?a.jsx(jf,{count:3}):o?a.jsxs("div",{className:"col-span-3 text-center py-8",children:[a.jsx("p",{className:"text-red-600 mb-4",children:"Failed to load featured products"}),a.jsx("button",{onClick:()=>window.location.reload(),className:"text-blue-600 hover:underline",children:"Try again"})]}):n&&n.length>0?n.slice(0,3).map((i,l)=>a.jsx("div",{className:"fade-in",style:{animationDelay:`${l*.2}s`},children:a.jsx(Cf,{product:i})},i.id)):a.jsx("div",{className:"col-span-3 text-center py-8",children:a.jsx("p",{className:"text-gray-600",children:"No featured products available"})})}),a.jsx("div",{className:"text-center",children:a.jsxs(Ae,{to:"/products",className:"medical-button group inline-flex items-center space-x-2",children:[a.jsx("span",{children:"View All Products"}),a.jsx(ch,{className:"w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"})]})})]})}),a.jsxs("section",{className:"medical-section bg-gradient-to-br from-gray-800 via-gray-900 to-blue-800 text-white relative overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-20",children:[a.jsx("div",{className:"absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-1000"}),a.jsx("div",{className:"absolute bottom-1/4 left-1/3 w-1 h-1 bg-white rounded-full animate-pulse delay-2000"}),a.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-3000"})]}),a.jsxs("div",{className:"container mx-auto px-4 text-center relative z-10",children:[a.jsxs("h2",{className:"text-4xl md:text-5xl font-bold mb-6",children:["Ready to"," ",a.jsx("span",{className:"text-blue-300",children:"Transform Your Life?"})]}),a.jsx("p",{className:"text-xl mb-12 max-w-3xl mx-auto leading-relaxed opacity-90",children:"Contact our team today to learn more about our prosthetic solutions and find the perfect fit for your needs. Your journey to independence starts here."}),a.jsxs("div",{className:"flex flex-col sm:flex-row gap-6 justify-center",children:[a.jsx(Ae,{to:"/contact",className:"bg-white text-gray-800 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1",children:"Contact Us Today"}),a.jsx(Ae,{to:"/products",className:"border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-gray-800 transition-all duration-300",children:"Browse Products"})]})]}),a.jsx("div",{className:"absolute top-0 left-0 w-96 h-96 bg-white opacity-5 rounded-full transform -translate-x-48 -translate-y-48"}),a.jsx("div",{className:"absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full transform translate-x-48 translate-y-48"})]})]})},ME=()=>(h.useEffect(()=>{window.scrollTo(0,0)},[]),a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50/30",children:[a.jsxs("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-gray-50 py-24 overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-10",children:[a.jsx("div",{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-blue-600 rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-1000"}),a.jsx("div",{className:"absolute bottom-1/4 left-1/3 w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-2000"}),a.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-3000"}),a.jsx("div",{className:"absolute top-1/2 left-1/2 w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-4000"})]}),a.jsx("div",{className:"container mx-auto px-4 relative z-10",children:a.jsxs("div",{className:"max-w-5xl mx-auto text-center",children:[a.jsxs("div",{className:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6 animate-fade-in",children:[a.jsx(Sr,{className:"w-4 h-4 inline mr-2"}),"About Our Company"]}),a.jsxs("div",{className:"flex flex-col items-center mb-8 animate-slide-up",children:[a.jsx("h1",{className:"text-5xl md:text-7xl font-bold mb-6",children:"About"}),a.jsx("div",{className:"flex items-center justify-center",children:a.jsx("img",{src:py,alt:"RELIFE Logo",className:"h-16 md:h-20 w-auto object-contain"})})]}),a.jsx("p",{className:"text-xl md:text-2xl text-gray-700 leading-relaxed max-w-4xl mx-auto animate-slide-up delay-200",children:"we've been at the forefront of prosthetic innovation, helping individuals regain mobility and confidence through advanced technology and compassionate care."}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mt-16 animate-fade-in delay-500",children:[a.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-blue-100",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4",children:a.jsx(YS,{className:"w-6 h-6 text-white"})}),a.jsx("h3",{className:"font-bold text-gray-900 mb-2",children:"Innovation Leader"}),a.jsx("p",{className:"text-gray-600 text-sm",children:"Pioneering advanced prosthetic technologies"})]}),a.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-blue-100",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4",children:a.jsx(Sr,{className:"w-6 h-6 text-white"})}),a.jsx("h3",{className:"font-bold text-gray-900 mb-2",children:"Patient-Centered"}),a.jsx("p",{className:"text-gray-600 text-sm",children:"Focused on improving quality of life"})]}),a.jsxs("div",{className:"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-blue-100",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center mx-auto mb-4",children:a.jsx(uh,{className:"w-6 h-6 text-white"})}),a.jsx("h3",{className:"font-bold text-gray-900 mb-2",children:"Excellence Driven"}),a.jsx("p",{className:"text-gray-600 text-sm",children:"Committed to the highest standards"})]})]})]})})]}),a.jsx("section",{className:"py-20",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[a.jsxs("div",{className:"animate-fade-in",children:[a.jsxs("div",{className:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6",children:[a.jsx(Sr,{className:"w-4 h-4 inline mr-2"}),"Our Purpose"]}),a.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Our Mission"}),a.jsx("p",{className:"text-lg text-gray-700 mb-6 leading-relaxed",children:"We believe that everyone deserves to live life to the fullest, regardless of physical challenges. Our mission is to provide cutting-edge prosthetic solutions that restore not just mobility, but dignity, independence, and hope."}),a.jsx("p",{className:"text-lg text-gray-700 leading-relaxed",children:"Through continuous innovation, personalized care, and unwavering commitment to quality, we're helping to redefine what's possible in the field of prosthetics."})]}),a.jsx("div",{className:"relative animate-fade-in delay-300",children:a.jsxs("div",{className:"p-8 lg:p-10 text-center",children:[a.jsx("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(VS,{className:"w-10 h-10 text-white"})}),a.jsx("h3",{className:"text-3xl font-bold text-gray-900 mb-6",children:"Our Vision"}),a.jsx("p",{className:"text-lg text-gray-700 leading-relaxed mb-8",children:"To be the global leader in prosthetic innovation, making advanced mobility solutions accessible to everyone who needs them."}),a.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[a.jsxs("div",{className:"flex items-center space-x-3 p-4 bg-gray-50 rounded-xl",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0",children:a.jsx(dh,{className:"w-4 h-4 text-blue-600"})}),a.jsx("span",{className:"text-gray-700 font-medium",children:"Innovation Excellence"})]}),a.jsxs("div",{className:"flex items-center space-x-3 p-4 bg-gray-50 rounded-xl",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0",children:a.jsx(ph,{className:"w-4 h-4 text-blue-600"})}),a.jsx("span",{className:"text-gray-700 font-medium",children:"Global Accessibility"})]}),a.jsxs("div",{className:"flex items-center space-x-3 p-4 bg-gray-50 rounded-xl",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0",children:a.jsx(Sr,{className:"w-4 h-4 text-blue-600"})}),a.jsx("span",{className:"text-gray-700 font-medium",children:"Life Transformation"})]})]})]})})]})})}),a.jsx("section",{className:"py-20 bg-gradient-to-br from-gray-50 to-blue-50/30",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"text-center mb-20 animate-fade-in",children:[a.jsxs("div",{className:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6",children:[a.jsx(Er,{className:"w-4 h-4 inline mr-2"}),"Our Foundation"]}),a.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Our Core Values"}),a.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"These principles guide everything we do, from product development to customer service, ensuring we deliver excellence at every step."})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[a.jsxs("div",{className:"medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-100",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(dh,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Innovation"}),a.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Continuously pushing the boundaries of what's possible in prosthetic technology."})]}),a.jsxs("div",{className:"medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-200",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(Sr,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Compassion"}),a.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Understanding the unique challenges and providing personalized care."})]}),a.jsxs("div",{className:"medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-300",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(uh,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Excellence"}),a.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Maintaining the highest standards in product quality and service delivery."})]}),a.jsxs("div",{className:"medical-card bg-white/90 backdrop-blur-sm p-8 text-center group animate-fade-in delay-400",children:[a.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(DS,{className:"w-8 h-8 text-white"})}),a.jsx("h3",{className:"text-xl font-bold text-gray-900 mb-4",children:"Integrity"}),a.jsx("p",{className:"text-gray-600 leading-relaxed",children:"Building trust through honest communication and ethical business practices."})]})]})]})}),a.jsx("section",{className:"py-20 bg-white",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"text-center mb-20 animate-fade-in",children:[a.jsxs("div",{className:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6",children:[a.jsx(ph,{className:"w-4 h-4 inline mr-2"}),"Our Team"]}),a.jsx("h2",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Our Expertise"}),a.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed",children:"Our multidisciplinary team brings together decades of experience in prosthetics, engineering, and patient care to deliver exceptional results."})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"medical-card bg-gradient-to-br from-blue-50 to-white p-8 text-center animate-fade-in delay-100",children:[a.jsx("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(qS,{className:"w-10 h-10 text-white"})}),a.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Certified Prosthetists"}),a.jsx("p",{className:"text-gray-600 leading-relaxed mb-6",children:"Board-certified professionals with extensive experience in prosthetic fitting and care."})]}),a.jsxs("div",{className:"medical-card bg-gradient-to-br from-blue-50 to-white p-8 text-center animate-fade-in delay-200",children:[a.jsx("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(GS,{className:"w-10 h-10 text-white"})}),a.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Engineering Team"}),a.jsx("p",{className:"text-gray-600 leading-relaxed mb-6",children:"Innovative engineers developing next-generation prosthetic technologies."})]}),a.jsxs("div",{className:"medical-card bg-gradient-to-br from-blue-50 to-white p-8 text-center animate-fade-in delay-300",children:[a.jsx("div",{className:"w-20 h-20 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg",children:a.jsx(BS,{className:"w-10 h-10 text-white"})}),a.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Support Specialists"}),a.jsx("p",{className:"text-gray-600 leading-relaxed mb-6",children:"Dedicated support team providing ongoing care and assistance throughout your journey."})]})]})]})})]}));var xy={exports:{}},IE="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",DE=IE,FE=DE;function wy(){}function by(){}by.resetWarningCache=wy;var $E=function(){function e(r,o,s,i,l,c){if(c!==FE){var u=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:by,resetWarningCache:wy};return n.PropTypes=n,n};xy.exports=$E();var zE=xy.exports;const ut=Ka(zE);var UE=["sitekey","onChange","theme","type","tabindex","onExpired","onErrored","size","stoken","grecaptcha","badge","hl","isolated"];function Xu(){return Xu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xu.apply(this,arguments)}function BE(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function Wi(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function WE(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Zu(e,t)}function Zu(e,t){return Zu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(r,o){return r.__proto__=o,r},Zu(e,t)}var bl=function(e){WE(t,e);function t(){var r;return r=e.call(this)||this,r.handleExpired=r.handleExpired.bind(Wi(r)),r.handleErrored=r.handleErrored.bind(Wi(r)),r.handleChange=r.handleChange.bind(Wi(r)),r.handleRecaptchaRef=r.handleRecaptchaRef.bind(Wi(r)),r}var n=t.prototype;return n.getCaptchaFunction=function(o){return this.props.grecaptcha?this.props.grecaptcha.enterprise?this.props.grecaptcha.enterprise[o]:this.props.grecaptcha[o]:null},n.getValue=function(){var o=this.getCaptchaFunction("getResponse");return o&&this._widgetId!==void 0?o(this._widgetId):null},n.getWidgetId=function(){return this.props.grecaptcha&&this._widgetId!==void 0?this._widgetId:null},n.execute=function(){var o=this.getCaptchaFunction("execute");if(o&&this._widgetId!==void 0)return o(this._widgetId);this._executeRequested=!0},n.executeAsync=function(){var o=this;return new Promise(function(s,i){o.executionResolve=s,o.executionReject=i,o.execute()})},n.reset=function(){var o=this.getCaptchaFunction("reset");o&&this._widgetId!==void 0&&o(this._widgetId)},n.forceReset=function(){var o=this.getCaptchaFunction("reset");o&&o()},n.handleExpired=function(){this.props.onExpired?this.props.onExpired():this.handleChange(null)},n.handleErrored=function(){this.props.onErrored&&this.props.onErrored(),this.executionReject&&(this.executionReject(),delete this.executionResolve,delete this.executionReject)},n.handleChange=function(o){this.props.onChange&&this.props.onChange(o),this.executionResolve&&(this.executionResolve(o),delete this.executionReject,delete this.executionResolve)},n.explicitRender=function(){var o=this.getCaptchaFunction("render");if(o&&this._widgetId===void 0){var s=document.createElement("div");this._widgetId=o(s,{sitekey:this.props.sitekey,callback:this.handleChange,theme:this.props.theme,type:this.props.type,tabindex:this.props.tabindex,"expired-callback":this.handleExpired,"error-callback":this.handleErrored,size:this.props.size,stoken:this.props.stoken,hl:this.props.hl,badge:this.props.badge,isolated:this.props.isolated}),this.captcha.appendChild(s)}this._executeRequested&&this.props.grecaptcha&&this._widgetId!==void 0&&(this._executeRequested=!1,this.execute())},n.componentDidMount=function(){this.explicitRender()},n.componentDidUpdate=function(){this.explicitRender()},n.handleRecaptchaRef=function(o){this.captcha=o},n.render=function(){var o=this.props;o.sitekey,o.onChange,o.theme,o.type,o.tabindex,o.onExpired,o.onErrored,o.size,o.stoken,o.grecaptcha,o.badge,o.hl,o.isolated;var s=BE(o,UE);return h.createElement("div",Xu({},s,{ref:this.handleRecaptchaRef}))},t}(h.Component);bl.displayName="ReCAPTCHA";bl.propTypes={sitekey:ut.string.isRequired,onChange:ut.func,grecaptcha:ut.object,theme:ut.oneOf(["dark","light"]),type:ut.oneOf(["image","audio"]),tabindex:ut.number,onExpired:ut.func,onErrored:ut.func,size:ut.oneOf(["compact","normal","invisible"]),stoken:ut.string,hl:ut.string,badge:ut.oneOf(["bottomright","bottomleft","inline"]),isolated:ut.bool};bl.defaultProps={onChange:function(){},theme:"light",type:"image",tabindex:0,size:"normal",badge:"bottomright"};var Sy={exports:{}},me={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Be=typeof Symbol=="function"&&Symbol.for,Ef=Be?Symbol.for("react.element"):60103,kf=Be?Symbol.for("react.portal"):60106,Sl=Be?Symbol.for("react.fragment"):60107,Nl=Be?Symbol.for("react.strict_mode"):60108,Cl=Be?Symbol.for("react.profiler"):60114,jl=Be?Symbol.for("react.provider"):60109,El=Be?Symbol.for("react.context"):60110,Pf=Be?Symbol.for("react.async_mode"):60111,kl=Be?Symbol.for("react.concurrent_mode"):60111,Pl=Be?Symbol.for("react.forward_ref"):60112,Tl=Be?Symbol.for("react.suspense"):60113,HE=Be?Symbol.for("react.suspense_list"):60120,Rl=Be?Symbol.for("react.memo"):60115,_l=Be?Symbol.for("react.lazy"):60116,VE=Be?Symbol.for("react.block"):60121,QE=Be?Symbol.for("react.fundamental"):60117,KE=Be?Symbol.for("react.responder"):60118,qE=Be?Symbol.for("react.scope"):60119;function Tt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Ef:switch(e=e.type,e){case Pf:case kl:case Sl:case Cl:case Nl:case Tl:return e;default:switch(e=e&&e.$$typeof,e){case El:case Pl:case _l:case Rl:case jl:return e;default:return t}}case kf:return t}}}function Ny(e){return Tt(e)===kl}me.AsyncMode=Pf;me.ConcurrentMode=kl;me.ContextConsumer=El;me.ContextProvider=jl;me.Element=Ef;me.ForwardRef=Pl;me.Fragment=Sl;me.Lazy=_l;me.Memo=Rl;me.Portal=kf;me.Profiler=Cl;me.StrictMode=Nl;me.Suspense=Tl;me.isAsyncMode=function(e){return Ny(e)||Tt(e)===Pf};me.isConcurrentMode=Ny;me.isContextConsumer=function(e){return Tt(e)===El};me.isContextProvider=function(e){return Tt(e)===jl};me.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ef};me.isForwardRef=function(e){return Tt(e)===Pl};me.isFragment=function(e){return Tt(e)===Sl};me.isLazy=function(e){return Tt(e)===_l};me.isMemo=function(e){return Tt(e)===Rl};me.isPortal=function(e){return Tt(e)===kf};me.isProfiler=function(e){return Tt(e)===Cl};me.isStrictMode=function(e){return Tt(e)===Nl};me.isSuspense=function(e){return Tt(e)===Tl};me.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Sl||e===kl||e===Cl||e===Nl||e===Tl||e===HE||typeof e=="object"&&e!==null&&(e.$$typeof===_l||e.$$typeof===Rl||e.$$typeof===jl||e.$$typeof===El||e.$$typeof===Pl||e.$$typeof===QE||e.$$typeof===KE||e.$$typeof===qE||e.$$typeof===VE)};me.typeOf=Tt;Sy.exports=me;var GE=Sy.exports,Tf=GE,YE={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},XE={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},ZE={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Cy={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Rf={};Rf[Tf.ForwardRef]=ZE;Rf[Tf.Memo]=Cy;function Hh(e){return Tf.isMemo(e)?Cy:Rf[e.$$typeof]||YE}var JE=Object.defineProperty,ek=Object.getOwnPropertyNames,Vh=Object.getOwnPropertySymbols,tk=Object.getOwnPropertyDescriptor,nk=Object.getPrototypeOf,Qh=Object.prototype;function jy(e,t,n){if(typeof t!="string"){if(Qh){var r=nk(t);r&&r!==Qh&&jy(e,r,n)}var o=ek(t);Vh&&(o=o.concat(Vh(t)));for(var s=Hh(e),i=Hh(t),l=0;l<o.length;++l){var c=o[l];if(!XE[c]&&!(n&&n[c])&&!(i&&i[c])&&!(s&&s[c])){var u=tk(t,c);try{JE(e,c,u)}catch(p){}}}}return e}var rk=jy;const ok=Ka(rk);function Ju(){return Ju=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Ju.apply(this,arguments)}function sk(e,t){if(e==null)return{};var n={},r=Object.keys(e),o,s;for(s=0;s<r.length;s++)o=r[s],!(t.indexOf(o)>=0)&&(n[o]=e[o]);return n}function ik(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}var $t={},ak=0;function lk(e,t){return t=t||{},function(r){var o=r.displayName||r.name||"Component",s=function(l){ik(c,l);function c(p,f){var d;return d=l.call(this,p,f)||this,d.state={},d.__scriptURL="",d}var u=c.prototype;return u.asyncScriptLoaderGetScriptLoaderID=function(){return this.__scriptLoaderID||(this.__scriptLoaderID="async-script-loader-"+ak++),this.__scriptLoaderID},u.setupScriptURL=function(){return this.__scriptURL=typeof e=="function"?e():e,this.__scriptURL},u.asyncScriptLoaderHandleLoad=function(f){var d=this;this.setState(f,function(){return d.props.asyncScriptOnLoad&&d.props.asyncScriptOnLoad(d.state)})},u.asyncScriptLoaderTriggerOnScriptLoaded=function(){var f=$t[this.__scriptURL];if(!f||!f.loaded)throw new Error("Script is not loaded.");for(var d in f.observers)f.observers[d](f);delete window[t.callbackName]},u.componentDidMount=function(){var f=this,d=this.setupScriptURL(),y=this.asyncScriptLoaderGetScriptLoaderID(),b=t,v=b.globalName,w=b.callbackName,m=b.scriptId;if(v&&typeof window[v]!="undefined"&&($t[d]={loaded:!0,observers:{}}),$t[d]){var g=$t[d];if(g&&(g.loaded||g.errored)){this.asyncScriptLoaderHandleLoad(g);return}g.observers[y]=function(k){return f.asyncScriptLoaderHandleLoad(k)};return}var x={};x[y]=function(k){return f.asyncScriptLoaderHandleLoad(k)},$t[d]={loaded:!1,observers:x};var S=document.createElement("script");S.src=d,S.async=!0;for(var C in t.attributes)S.setAttribute(C,t.attributes[C]);m&&(S.id=m);var T=function(R){if($t[d]){var _=$t[d],O=_.observers;for(var B in O)R(O[B])&&delete O[B]}};w&&typeof window!="undefined"&&(window[w]=function(){return f.asyncScriptLoaderTriggerOnScriptLoaded()}),S.onload=function(){var k=$t[d];k&&(k.loaded=!0,T(function(R){return w?!1:(R(k),!0)}))},S.onerror=function(){var k=$t[d];k&&(k.errored=!0,T(function(R){return R(k),!0}))},document.body.appendChild(S)},u.componentWillUnmount=function(){var f=this.__scriptURL;if(t.removeOnUnmount===!0)for(var d=document.getElementsByTagName("script"),y=0;y<d.length;y+=1)d[y].src.indexOf(f)>-1&&d[y].parentNode&&d[y].parentNode.removeChild(d[y]);var b=$t[f];b&&(delete b.observers[this.asyncScriptLoaderGetScriptLoaderID()],t.removeOnUnmount===!0&&delete $t[f])},u.render=function(){var f=t.globalName,d=this.props;d.asyncScriptOnLoad;var y=d.forwardedRef,b=sk(d,["asyncScriptOnLoad","forwardedRef"]);return f&&typeof window!="undefined"&&(b[f]=typeof window[f]!="undefined"?window[f]:void 0),b.ref=y,h.createElement(r,b)},c}(h.Component),i=h.forwardRef(function(l,c){return h.createElement(s,Ju({},l,{forwardedRef:c}))});return i.displayName="AsyncScriptLoader("+o+")",i.propTypes={asyncScriptOnLoad:ut.func},ok(i,r)}}var ed="onloadcallback",ck="grecaptcha";function td(){return typeof window!="undefined"&&window.recaptchaOptions||{}}function uk(){var e=td(),t=e.useRecaptchaNet?"recaptcha.net":"www.google.com";return e.enterprise?"https://"+t+"/recaptcha/enterprise.js?onload="+ed+"&render=explicit":"https://"+t+"/recaptcha/api.js?onload="+ed+"&render=explicit"}const dk=lk(uk,{callbackName:ed,globalName:ck,attributes:td().nonce?{nonce:td().nonce}:{}})(bl),Ey=h.forwardRef(({siteKey:e,onVerify:t,onExpired:n,onError:r,onLoad:o,theme:s="light",size:i="normal",className:l="",containerClassName:c=""},u)=>{const p=h.useRef(null);h.useImperativeHandle(u,()=>({reset:()=>{var v;(v=p.current)==null||v.reset()},getResponse:()=>{var v;return((v=p.current)==null?void 0:v.getValue())||""}}));const f=v=>{v?t(v):n==null||n()},d=()=>{n==null||n()},y=()=>{r==null||r()},b=()=>{o==null||o()};return a.jsx("div",{className:`flex justify-center ${c}`,children:a.jsx("div",{className:`recaptcha-container ${l}`,style:{transform:"scale(1)",transformOrigin:"0 0",borderRadius:"8px",overflow:"hidden",boxShadow:"0 2px 8px rgba(0, 0, 0, 0.1)"},children:a.jsx(dk,{ref:p,sitekey:e,onChange:f,onExpired:d,onErrored:y,onLoad:b,theme:s,size:i})})})});Ey.displayName="ReCaptcha";var wc="focusScope.autoFocusOnMount",bc="focusScope.autoFocusOnUnmount",Kh={bubbles:!1,cancelable:!0},fk="FocusScope",_f=h.forwardRef((e,t)=>{const v=e,{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s}=v,i=D(v,["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"]),[l,c]=h.useState(null),u=Et(o),p=Et(s),f=h.useRef(null),d=Ne(t,w=>c(w)),y=h.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;h.useEffect(()=>{if(r){let w=function(S){if(y.paused||!l)return;const C=S.target;l.contains(C)?f.current=C:In(f.current,{select:!0})},m=function(S){if(y.paused||!l)return;const C=S.relatedTarget;C!==null&&(l.contains(C)||In(f.current,{select:!0}))},g=function(S){if(document.activeElement===document.body)for(const T of S)T.removedNodes.length>0&&In(l)};document.addEventListener("focusin",w),document.addEventListener("focusout",m);const x=new MutationObserver(g);return l&&x.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",w),document.removeEventListener("focusout",m),x.disconnect()}}},[r,l,y.paused]),h.useEffect(()=>{if(l){Gh.add(y);const w=document.activeElement;if(!l.contains(w)){const g=new CustomEvent(wc,Kh);l.addEventListener(wc,u),l.dispatchEvent(g),g.defaultPrevented||(pk(yk(ky(l)),{select:!0}),document.activeElement===w&&In(l))}return()=>{l.removeEventListener(wc,u),setTimeout(()=>{const g=new CustomEvent(bc,Kh);l.addEventListener(bc,p),l.dispatchEvent(g),g.defaultPrevented||In(w!=null?w:document.body,{select:!0}),l.removeEventListener(bc,p),Gh.remove(y)},0)}}},[l,u,p,y]);const b=h.useCallback(w=>{if(!n&&!r||y.paused)return;const m=w.key==="Tab"&&!w.altKey&&!w.ctrlKey&&!w.metaKey,g=document.activeElement;if(m&&g){const x=w.currentTarget,[S,C]=hk(x);S&&C?!w.shiftKey&&g===C?(w.preventDefault(),n&&In(S,{select:!0})):w.shiftKey&&g===S&&(w.preventDefault(),n&&In(C,{select:!0})):g===x&&w.preventDefault()}},[n,r,y.paused]);return a.jsx(ce.div,E(N({tabIndex:-1},i),{ref:d,onKeyDown:b}))});_f.displayName=fk;function pk(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(In(r,{select:t}),document.activeElement!==n)return}function hk(e){const t=ky(e),n=qh(t,e),r=qh(t.reverse(),e);return[n,r]}function ky(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function qh(e,t){for(const n of e)if(!mk(n,{upTo:t}))return n}function mk(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function gk(e){return e instanceof HTMLInputElement&&"select"in e}function In(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&gk(e)&&t&&e.select()}}var Gh=vk();function vk(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=Yh(e,t),e.unshift(t)},remove(t){var n;e=Yh(e,t),(n=e[0])==null||n.resume()}}}function Yh(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function yk(e){return e.filter(t=>t.tagName!=="A")}var Sc=0;function Py(){h.useEffect(()=>{var t,n;const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",(t=e[0])!=null?t:Xh()),document.body.insertAdjacentElement("beforeend",(n=e[1])!=null?n:Xh()),Sc++,()=>{Sc===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),Sc--}},[])}function Xh(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var ln=function(){return ln=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},ln.apply(this,arguments)};function Ty(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function xk(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var ua="right-scroll-bar-position",da="width-before-scroll-bar",wk="with-scroll-bars-hidden",bk="--removed-body-scroll-bar-size";function Nc(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function Sk(e,t){var n=h.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var Nk=typeof window!="undefined"?h.useLayoutEffect:h.useEffect,Zh=new WeakMap;function Ck(e,t){var n=Sk(null,function(r){return e.forEach(function(o){return Nc(o,r)})});return Nk(function(){var r=Zh.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(l){s.has(l)||Nc(l,null)}),s.forEach(function(l){o.has(l)||Nc(l,i)})}Zh.set(n,e)},[e]),n}function jk(e){return e}function Ek(e,t){t===void 0&&(t=jk);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(l){return l!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(l){return s(l)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var l=n;n=[],l.forEach(s),i=n}var c=function(){var p=i;i=[],p.forEach(s)},u=function(){return Promise.resolve().then(c)};u(),n={push:function(p){i.push(p),u()},filter:function(p){return i=i.filter(p),n}}}};return o}function kk(e){e===void 0&&(e={});var t=Ek(null);return t.options=ln({async:!0,ssr:!1},e),t}var Ry=function(e){var t=e.sideCar,n=Ty(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return h.createElement(r,ln({},n))};Ry.isSideCarExport=!0;function Pk(e,t){return e.useMedium(t),Ry}var _y=kk(),Cc=function(){},Ol=h.forwardRef(function(e,t){var n=h.useRef(null),r=h.useState({onScrollCapture:Cc,onWheelCapture:Cc,onTouchMoveCapture:Cc}),o=r[0],s=r[1],i=e.forwardProps,l=e.children,c=e.className,u=e.removeScrollBar,p=e.enabled,f=e.shards,d=e.sideCar,y=e.noIsolation,b=e.inert,v=e.allowPinchZoom,w=e.as,m=w===void 0?"div":w,g=e.gapMode,x=Ty(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=d,C=Ck([n,t]),T=ln(ln({},x),o);return h.createElement(h.Fragment,null,p&&h.createElement(S,{sideCar:_y,removeScrollBar:u,shards:f,noIsolation:y,inert:b,setCallbacks:s,allowPinchZoom:!!v,lockRef:n,gapMode:g}),i?h.cloneElement(h.Children.only(l),ln(ln({},T),{ref:C})):h.createElement(m,ln({},T,{className:c,ref:C}),l))});Ol.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ol.classNames={fullWidth:da,zeroRight:ua};var Tk=function(){if(typeof __webpack_nonce__!="undefined")return __webpack_nonce__};function Rk(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=Tk();return t&&e.setAttribute("nonce",t),e}function _k(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function Ok(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var Ak=function(){var e=0,t=null;return{add:function(n){e==0&&(t=Rk())&&(_k(t,n),Ok(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},Lk=function(){var e=Ak();return function(t,n){h.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},Oy=function(){var e=Lk(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},Mk={left:0,top:0,right:0,gap:0},jc=function(e){return parseInt(e||"",10)||0},Ik=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[jc(n),jc(r),jc(o)]},Dk=function(e){if(e===void 0&&(e="margin"),typeof window=="undefined")return Mk;var t=Ik(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Fk=Oy(),Eo="data-scroll-locked",$k=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(wk,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(Eo,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(ua,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(da,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(ua," .").concat(ua,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(da," .").concat(da,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Eo,`] {
    `).concat(bk,": ").concat(l,`px;
  }
`)},Jh=function(){var e=parseInt(document.body.getAttribute(Eo)||"0",10);return isFinite(e)?e:0},zk=function(){h.useEffect(function(){return document.body.setAttribute(Eo,(Jh()+1).toString()),function(){var e=Jh()-1;e<=0?document.body.removeAttribute(Eo):document.body.setAttribute(Eo,e.toString())}},[])},Uk=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;zk();var s=h.useMemo(function(){return Dk(o)},[o]);return h.createElement(Fk,{styles:$k(s,!t,o,n?"":"!important")})},nd=!1;if(typeof window!="undefined")try{var Hi=Object.defineProperty({},"passive",{get:function(){return nd=!0,!0}});window.addEventListener("test",Hi,Hi),window.removeEventListener("test",Hi,Hi)}catch(e){nd=!1}var Jr=nd?{passive:!1}:!1,Bk=function(e){return e.tagName==="TEXTAREA"},Ay=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Bk(e)&&n[t]==="visible")},Wk=function(e){return Ay(e,"overflowY")},Hk=function(e){return Ay(e,"overflowX")},em=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot!="undefined"&&r instanceof ShadowRoot&&(r=r.host);var o=Ly(e,r);if(o){var s=My(e,r),i=s[1],l=s[2];if(i>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Vk=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},Qk=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},Ly=function(e,t){return e==="v"?Wk(t):Hk(t)},My=function(e,t){return e==="v"?Vk(t):Qk(t)},Kk=function(e,t){return e==="h"&&t==="rtl"?-1:1},qk=function(e,t,n,r,o){var s=Kk(e,window.getComputedStyle(t).direction),i=s*r,l=n.target,c=t.contains(l),u=!1,p=i>0,f=0,d=0;do{var y=My(e,l),b=y[0],v=y[1],w=y[2],m=v-w-s*b;(b||m)&&Ly(e,l)&&(f+=m,d+=b),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return(p&&(Math.abs(f)<1||!o)||!p&&(Math.abs(d)<1||!o))&&(u=!0),u},Vi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},tm=function(e){return[e.deltaX,e.deltaY]},nm=function(e){return e&&"current"in e?e.current:e},Gk=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Yk=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Xk=0,eo=[];function Zk(e){var t=h.useRef([]),n=h.useRef([0,0]),r=h.useRef(),o=h.useState(Xk++)[0],s=h.useState(Oy)[0],i=h.useRef(e);h.useEffect(function(){i.current=e},[e]),h.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var v=xk([e.lockRef.current],(e.shards||[]).map(nm),!0).filter(Boolean);return v.forEach(function(w){return w.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),v.forEach(function(w){return w.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=h.useCallback(function(v,w){if("touches"in v&&v.touches.length===2||v.type==="wheel"&&v.ctrlKey)return!i.current.allowPinchZoom;var m=Vi(v),g=n.current,x="deltaX"in v?v.deltaX:g[0]-m[0],S="deltaY"in v?v.deltaY:g[1]-m[1],C,T=v.target,k=Math.abs(x)>Math.abs(S)?"h":"v";if("touches"in v&&k==="h"&&T.type==="range")return!1;var R=em(k,T);if(!R)return!0;if(R?C=k:(C=k==="v"?"h":"v",R=em(k,T)),!R)return!1;if(!r.current&&"changedTouches"in v&&(x||S)&&(r.current=C),!C)return!0;var _=r.current||C;return qk(_,w,v,_==="h"?x:S,!0)},[]),c=h.useCallback(function(v){var w=v;if(!(!eo.length||eo[eo.length-1]!==s)){var m="deltaY"in w?tm(w):Vi(w),g=t.current.filter(function(C){return C.name===w.type&&(C.target===w.target||w.target===C.shadowParent)&&Gk(C.delta,m)})[0];if(g&&g.should){w.cancelable&&w.preventDefault();return}if(!g){var x=(i.current.shards||[]).map(nm).filter(Boolean).filter(function(C){return C.contains(w.target)}),S=x.length>0?l(w,x[0]):!i.current.noIsolation;S&&w.cancelable&&w.preventDefault()}}},[]),u=h.useCallback(function(v,w,m,g){var x={name:v,delta:w,target:m,should:g,shadowParent:Jk(m)};t.current.push(x),setTimeout(function(){t.current=t.current.filter(function(S){return S!==x})},1)},[]),p=h.useCallback(function(v){n.current=Vi(v),r.current=void 0},[]),f=h.useCallback(function(v){u(v.type,tm(v),v.target,l(v,e.lockRef.current))},[]),d=h.useCallback(function(v){u(v.type,Vi(v),v.target,l(v,e.lockRef.current))},[]);h.useEffect(function(){return eo.push(s),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:d}),document.addEventListener("wheel",c,Jr),document.addEventListener("touchmove",c,Jr),document.addEventListener("touchstart",p,Jr),function(){eo=eo.filter(function(v){return v!==s}),document.removeEventListener("wheel",c,Jr),document.removeEventListener("touchmove",c,Jr),document.removeEventListener("touchstart",p,Jr)}},[]);var y=e.removeScrollBar,b=e.inert;return h.createElement(h.Fragment,null,b?h.createElement(s,{styles:Yk(o)}):null,y?h.createElement(Uk,{gapMode:e.gapMode}):null)}function Jk(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const eP=Pk(_y,Zk);var Of=h.forwardRef(function(e,t){return h.createElement(Ol,ln({},e,{ref:t,sideCar:eP}))});Of.classNames=Ol.classNames;var tP=function(e){if(typeof document=="undefined")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},to=new WeakMap,Qi=new WeakMap,Ki={},Ec=0,Iy=function(e){return e&&(e.host||Iy(e.parentNode))},nP=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=Iy(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},rP=function(e,t,n,r){var o=nP(t,Array.isArray(e)?e:[e]);Ki[n]||(Ki[n]=new WeakMap);var s=Ki[n],i=[],l=new Set,c=new Set(o),u=function(f){!f||l.has(f)||(l.add(f),u(f.parentNode))};o.forEach(u);var p=function(f){!f||c.has(f)||Array.prototype.forEach.call(f.children,function(d){if(l.has(d))p(d);else try{var y=d.getAttribute(r),b=y!==null&&y!=="false",v=(to.get(d)||0)+1,w=(s.get(d)||0)+1;to.set(d,v),s.set(d,w),i.push(d),v===1&&b&&Qi.set(d,!0),w===1&&d.setAttribute(n,"true"),b||d.setAttribute(r,"true")}catch(m){console.error("aria-hidden: cannot operate on ",d,m)}})};return p(t),l.clear(),Ec++,function(){i.forEach(function(f){var d=to.get(f)-1,y=s.get(f)-1;to.set(f,d),s.set(f,y),d||(Qi.has(f)||f.removeAttribute(r),Qi.delete(f)),y||f.removeAttribute(n)}),Ec--,Ec||(to=new WeakMap,to=new WeakMap,Qi=new WeakMap,Ki={})}},Dy=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=tP(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),rP(r,o,n,"aria-hidden")):function(){return null}},Af="Dialog",[Fy,f4]=ul(Af),[oP,Xt]=Fy(Af),$y=e=>{const{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:s,modal:i=!0}=e,l=h.useRef(null),c=h.useRef(null),[u=!1,p]=Ia({prop:r,defaultProp:o,onChange:s});return a.jsx(oP,{scope:t,triggerRef:l,contentRef:c,contentId:No(),titleId:No(),descriptionId:No(),open:u,onOpenChange:p,onOpenToggle:h.useCallback(()=>p(f=>!f),[p]),modal:i,children:n})};$y.displayName=Af;var zy="DialogTrigger",sP=h.forwardRef((e,t)=>{const i=e,{__scopeDialog:n}=i,r=D(i,["__scopeDialog"]),o=Xt(zy,n),s=Ne(t,o.triggerRef);return a.jsx(ce.button,E(N({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":If(o.open)},r),{ref:s,onClick:J(e.onClick,o.onOpenToggle)}))});sP.displayName=zy;var Lf="DialogPortal",[iP,Uy]=Fy(Lf,{forceMount:void 0}),By=e=>{const{__scopeDialog:t,forceMount:n,children:r,container:o}=e,s=Xt(Lf,t);return a.jsx(iP,{scope:t,forceMount:n,children:h.Children.map(r,i=>a.jsx(Jo,{present:n||s.open,children:a.jsx(dl,{asChild:!0,container:o,children:i})}))})};By.displayName=Lf;var Va="DialogOverlay",Wy=h.forwardRef((e,t)=>{const n=Uy(Va,e.__scopeDialog),i=e,{forceMount:r=n.forceMount}=i,o=D(i,["forceMount"]),s=Xt(Va,e.__scopeDialog);return s.modal?a.jsx(Jo,{present:r||s.open,children:a.jsx(aP,E(N({},o),{ref:t}))}):null});Wy.displayName=Va;var aP=h.forwardRef((e,t)=>{const s=e,{__scopeDialog:n}=s,r=D(s,["__scopeDialog"]),o=Xt(Va,n);return a.jsx(Of,{as:Ko,allowPinchZoom:!0,shards:[o.contentRef],children:a.jsx(ce.div,E(N({"data-state":If(o.open)},r),{ref:t,style:N({pointerEvents:"auto"},r.style)}))})}),Wr="DialogContent",Hy=h.forwardRef((e,t)=>{const n=Uy(Wr,e.__scopeDialog),i=e,{forceMount:r=n.forceMount}=i,o=D(i,["forceMount"]),s=Xt(Wr,e.__scopeDialog);return a.jsx(Jo,{present:r||s.open,children:s.modal?a.jsx(lP,E(N({},o),{ref:t})):a.jsx(cP,E(N({},o),{ref:t}))})});Hy.displayName=Wr;var lP=h.forwardRef((e,t)=>{const n=Xt(Wr,e.__scopeDialog),r=h.useRef(null),o=Ne(t,n.contentRef,r);return h.useEffect(()=>{const s=r.current;if(s)return Dy(s)},[]),a.jsx(Vy,E(N({},e),{ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:J(e.onCloseAutoFocus,s=>{var i;s.preventDefault(),(i=n.triggerRef.current)==null||i.focus()}),onPointerDownOutside:J(e.onPointerDownOutside,s=>{const i=s.detail.originalEvent,l=i.button===0&&i.ctrlKey===!0;(i.button===2||l)&&s.preventDefault()}),onFocusOutside:J(e.onFocusOutside,s=>s.preventDefault())}))}),cP=h.forwardRef((e,t)=>{const n=Xt(Wr,e.__scopeDialog),r=h.useRef(!1),o=h.useRef(!1);return a.jsx(Vy,E(N({},e),{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{var i,l;(i=e.onCloseAutoFocus)==null||i.call(e,s),s.defaultPrevented||(r.current||(l=n.triggerRef.current)==null||l.focus(),s.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:s=>{var c,u;(c=e.onInteractOutside)==null||c.call(e,s),s.defaultPrevented||(r.current=!0,s.detail.originalEvent.type==="pointerdown"&&(o.current=!0));const i=s.target;((u=n.triggerRef.current)==null?void 0:u.contains(i))&&s.preventDefault(),s.detail.originalEvent.type==="focusin"&&o.current&&s.preventDefault()}}))}),Vy=h.forwardRef((e,t)=>{const p=e,{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:s}=p,i=D(p,["__scopeDialog","trapFocus","onOpenAutoFocus","onCloseAutoFocus"]),l=Xt(Wr,n),c=h.useRef(null),u=Ne(t,c);return Py(),a.jsxs(a.Fragment,{children:[a.jsx(_f,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:s,children:a.jsx(ci,E(N({role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":If(l.open)},i),{ref:u,onDismiss:()=>l.onOpenChange(!1)}))}),a.jsxs(a.Fragment,{children:[a.jsx(uP,{titleId:l.titleId}),a.jsx(fP,{contentRef:c,descriptionId:l.descriptionId})]})]})}),Mf="DialogTitle",Qy=h.forwardRef((e,t)=>{const s=e,{__scopeDialog:n}=s,r=D(s,["__scopeDialog"]),o=Xt(Mf,n);return a.jsx(ce.h2,E(N({id:o.titleId},r),{ref:t}))});Qy.displayName=Mf;var Ky="DialogDescription",qy=h.forwardRef((e,t)=>{const s=e,{__scopeDialog:n}=s,r=D(s,["__scopeDialog"]),o=Xt(Ky,n);return a.jsx(ce.p,E(N({id:o.descriptionId},r),{ref:t}))});qy.displayName=Ky;var Gy="DialogClose",Yy=h.forwardRef((e,t)=>{const s=e,{__scopeDialog:n}=s,r=D(s,["__scopeDialog"]),o=Xt(Gy,n);return a.jsx(ce.button,E(N({type:"button"},r),{ref:t,onClick:J(e.onClick,()=>o.onOpenChange(!1))}))});Yy.displayName=Gy;function If(e){return e?"open":"closed"}var Xy="DialogTitleWarning",[p4,Zy]=zb(Xy,{contentName:Wr,titleName:Mf,docsSlug:"dialog"}),uP=({titleId:e})=>{const t=Zy(Xy),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return h.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},dP="DialogDescriptionWarning",fP=({contentRef:e,descriptionId:t})=>{const r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Zy(dP).contentName}}.`;return h.useEffect(()=>{var s;const o=(s=e.current)==null?void 0:s.getAttribute("aria-describedby");t&&o&&(document.getElementById(t)||console.warn(r))},[r,e,t]),null},pP=$y,hP=By,Jy=Wy,ex=Hy,tx=Qy,nx=qy,mP=Yy;const gP=pP,vP=hP,rx=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(Jy,N({ref:n,className:Le("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e)},t))});rx.displayName=Jy.displayName;const ox=h.forwardRef((o,r)=>{var s=o,{className:e,children:t}=s,n=D(s,["className","children"]);return a.jsxs(vP,{children:[a.jsx(rx,{}),a.jsxs(ex,E(N({ref:r,className:Le("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e)},n),{children:[t,a.jsxs(mP,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[a.jsx(sf,{className:"h-4 w-4"}),a.jsx("span",{className:"sr-only",children:"Close"})]})]}))]})});ox.displayName=ex.displayName;const sx=n=>{var r=n,{className:e}=r,t=D(r,["className"]);return a.jsx("div",N({className:Le("flex flex-col space-y-1.5 text-center sm:text-left",e)},t))};sx.displayName="DialogHeader";const ix=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(tx,N({ref:n,className:Le("text-lg font-semibold leading-none tracking-tight",e)},t))});ix.displayName=tx.displayName;const ax=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(nx,N({ref:n,className:Le("text-sm text-muted-foreground",e)},t))});ax.displayName=nx.displayName;const yP=({isOpen:e,onClose:t,type:n,title:r,message:o,autoClose:s=!1,autoCloseDelay:i=4e3})=>{h.useEffect(()=>{if(e&&s){const p=setTimeout(()=>{t()},i);return()=>clearTimeout(p)}},[e,s,i,t]),h.useEffect(()=>{const p=f=>{f.key==="Escape"&&e&&t()};if(e)return document.addEventListener("keydown",p),()=>document.removeEventListener("keydown",p)},[e,t]);const c=(()=>{switch(n){case"success":return{icon:OS,iconColor:"text-green-600",bgGradient:"from-green-50 to-blue-50",borderColor:"border-green-300",titleColor:"text-green-800",messageColor:"text-green-700"};case"error":return{icon:AS,iconColor:"text-red-600",bgGradient:"from-red-50 to-blue-50",borderColor:"border-red-300",titleColor:"text-red-800",messageColor:"text-red-700"};case"warning":return{icon:_S,iconColor:"text-orange-600",bgGradient:"from-orange-50 to-blue-50",borderColor:"border-orange-300",titleColor:"text-orange-800",messageColor:"text-orange-700"};default:return{icon:Er,iconColor:"text-blue-600",bgGradient:"from-blue-50 to-white",borderColor:"border-blue-300",titleColor:"text-blue-800",messageColor:"text-blue-700"}}})(),u=c.icon;return a.jsx(gP,{open:e,onOpenChange:t,children:a.jsxs(ox,{className:`
          max-w-md mx-auto
          bg-gradient-to-br ${c.bgGradient}
          backdrop-blur-sm
          border-2 ${c.borderColor}
          rounded-2xl
          shadow-2xl
          p-0
          overflow-hidden
          transform transition-all duration-300
          focus:outline-none
          focus:ring-4 focus:ring-blue-100
        `,"aria-describedby":"notification-description",role:"alertdialog","aria-modal":"true",children:[a.jsxs(sx,{className:"relative p-8 pb-4",children:[a.jsx("div",{className:"flex items-center justify-center mb-4",children:a.jsx("div",{className:`
              w-16 h-16 
              rounded-full 
              bg-white/80 
              backdrop-blur-sm 
              border-2 ${c.borderColor}
              flex items-center justify-center
              shadow-lg
              transform transition-all duration-300 hover:scale-105
            `,children:a.jsx(u,{className:`w-8 h-8 ${c.iconColor}`})})}),a.jsx(ix,{className:`
            text-2xl font-bold text-center mb-2
            ${c.titleColor}
            leading-tight
          `,children:r})]}),a.jsxs("div",{className:"px-8 pb-8",children:[a.jsx(ax,{id:"notification-description",className:`
              text-center text-base leading-relaxed
              ${c.messageColor}
              font-medium
            `,children:o}),s&&n==="success"&&a.jsxs("div",{className:"mt-6 flex items-center justify-center space-x-2 text-sm text-green-600",children:[a.jsx("div",{className:"w-2 h-2 bg-green-600 rounded-full animate-pulse"}),a.jsx("span",{children:"This message will close automatically"})]}),!s&&n==="error"&&a.jsx("div",{className:"mt-6 flex items-center justify-center space-x-2 text-sm text-red-600",children:a.jsx("span",{children:"Click outside or press ESC to close"})})]}),a.jsx("div",{className:`
          h-1 w-full 
          bg-gradient-to-r 
          ${n==="success"?"from-green-400 to-blue-500":n==="error"?"from-red-400 to-blue-500":"from-orange-400 to-blue-500"}
        `})]})})};function rm(e,[t,n]){return Math.min(n,Math.max(t,e))}var xP=h.createContext(void 0);function wP(e){const t=h.useContext(xP);return e||t||"ltr"}function bP(e){const t=h.useRef({value:e,previous:e});return h.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var SP=[" ","Enter","ArrowUp","ArrowDown"],NP=[" ","Enter"],mi="Select",[Al,Ll,CP]=R0(mi),[os,h4]=ul(mi,[CP,vl]),Ml=vl(),[jP,vr]=os(mi),[EP,kP]=os(mi),lx=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:i,defaultValue:l,onValueChange:c,dir:u,name:p,autoComplete:f,disabled:d,required:y,form:b}=e,v=Ml(t),[w,m]=h.useState(null),[g,x]=h.useState(null),[S,C]=h.useState(!1),T=wP(u),[k=!1,R]=Ia({prop:r,defaultProp:o,onChange:s}),[_,O]=Ia({prop:i,defaultProp:l,onChange:c}),B=h.useRef(null),L=w?b||!!w.closest("form"):!0,[V,Y]=h.useState(new Set),M=Array.from(V).map(z=>z.props.value).join(";");return a.jsx(yj,E(N({},v),{children:a.jsxs(jP,{required:y,scope:t,trigger:w,onTriggerChange:m,valueNode:g,onValueNodeChange:x,valueNodeHasChildren:S,onValueNodeHasChildrenChange:C,contentId:No(),value:_,onValueChange:O,open:k,onOpenChange:R,dir:T,triggerPointerDownPosRef:B,disabled:d,children:[a.jsx(Al.Provider,{scope:t,children:a.jsx(EP,{scope:e.__scopeSelect,onNativeOptionAdd:h.useCallback(z=>{Y(q=>new Set(q).add(z))},[]),onNativeOptionRemove:h.useCallback(z=>{Y(q=>{const P=new Set(q);return P.delete(z),P})},[]),children:n})}),L?a.jsxs(Ax,{"aria-hidden":!0,required:y,tabIndex:-1,name:p,autoComplete:f,value:_,onChange:z=>O(z.target.value),disabled:d,form:b,children:[_===void 0?a.jsx("option",{value:""}):null,Array.from(V)]},M):null]})}))};lx.displayName=mi;var cx="SelectTrigger",ux=h.forwardRef((e,t)=>{const v=e,{__scopeSelect:n,disabled:r=!1}=v,o=D(v,["__scopeSelect","disabled"]),s=Ml(n),i=vr(cx,n),l=i.disabled||r,c=Ne(t,i.onTriggerChange),u=Ll(n),p=h.useRef("touch"),[f,d,y]=Lx(w=>{const m=u().filter(S=>!S.disabled),g=m.find(S=>S.value===i.value),x=Mx(m,w,g);x!==void 0&&i.onValueChange(x.value)}),b=w=>{l||(i.onOpenChange(!0),y()),w&&(i.triggerPointerDownPosRef.current={x:Math.round(w.pageX),y:Math.round(w.pageY)})};return a.jsx(Lv,E(N({asChild:!0},s),{children:a.jsx(ce.button,E(N({type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Ox(i.value)?"":void 0},o),{ref:c,onClick:J(o.onClick,w=>{w.currentTarget.focus(),p.current!=="mouse"&&b(w)}),onPointerDown:J(o.onPointerDown,w=>{p.current=w.pointerType;const m=w.target;m.hasPointerCapture(w.pointerId)&&m.releasePointerCapture(w.pointerId),w.button===0&&w.ctrlKey===!1&&w.pointerType==="mouse"&&(b(w),w.preventDefault())}),onKeyDown:J(o.onKeyDown,w=>{const m=f.current!=="";!(w.ctrlKey||w.altKey||w.metaKey)&&w.key.length===1&&d(w.key),!(m&&w.key===" ")&&SP.includes(w.key)&&(b(),w.preventDefault())})}))}))});ux.displayName=cx;var dx="SelectValue",fx=h.forwardRef((e,t)=>{const d=e,{__scopeSelect:n,className:r,style:o,children:s,placeholder:i=""}=d,l=D(d,["__scopeSelect","className","style","children","placeholder"]),c=vr(dx,n),{onValueNodeHasChildrenChange:u}=c,p=s!==void 0,f=Ne(t,c.onValueNodeChange);return nt(()=>{u(p)},[u,p]),a.jsx(ce.span,E(N({},l),{ref:f,style:{pointerEvents:"none"},children:Ox(c.value)?a.jsx(a.Fragment,{children:i}):s}))});fx.displayName=dx;var PP="SelectIcon",px=h.forwardRef((e,t)=>{const s=e,{__scopeSelect:n,children:r}=s,o=D(s,["__scopeSelect","children"]);return a.jsx(ce.span,E(N({"aria-hidden":!0},o),{ref:t,children:r||"▼"}))});px.displayName=PP;var TP="SelectPortal",hx=e=>a.jsx(dl,N({asChild:!0},e));hx.displayName=TP;var Hr="SelectContent",mx=h.forwardRef((e,t)=>{const n=vr(Hr,e.__scopeSelect),[r,o]=h.useState();if(nt(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?Kr.createPortal(a.jsx(gx,{scope:e.__scopeSelect,children:a.jsx(Al.Slot,{scope:e.__scopeSelect,children:a.jsx("div",{children:e.children})})}),s):null}return a.jsx(vx,E(N({},e),{ref:t}))});mx.displayName=Hr;var zt=10,[gx,yr]=os(Hr),RP="SelectContentImpl",vx=h.forwardRef((e,t)=>{const $e=e,{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:i,side:l,sideOffset:c,align:u,alignOffset:p,arrowPadding:f,collisionBoundary:d,collisionPadding:y,sticky:b,hideWhenDetached:v,avoidCollisions:w}=$e,m=D($e,["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"]),g=vr(Hr,n),[x,S]=h.useState(null),[C,T]=h.useState(null),k=Ne(t,K=>S(K)),[R,_]=h.useState(null),[O,B]=h.useState(null),L=Ll(n),[V,Y]=h.useState(!1),M=h.useRef(!1);h.useEffect(()=>{if(x)return Dy(x)},[x]),Py();const z=h.useCallback(K=>{const[ue,...Te]=L().map(de=>de.ref.current),[ge]=Te.slice(-1),le=document.activeElement;for(const de of K)if(de===le||(de==null||de.scrollIntoView({block:"nearest"}),de===ue&&C&&(C.scrollTop=0),de===ge&&C&&(C.scrollTop=C.scrollHeight),de==null||de.focus(),document.activeElement!==le))return},[L,C]),q=h.useCallback(()=>z([R,x]),[z,R,x]);h.useEffect(()=>{V&&q()},[V,q]);const{onOpenChange:P,triggerPointerDownPosRef:I}=g;h.useEffect(()=>{if(x){let K={x:0,y:0};const ue=ge=>{var le,de,qe,Ge;K={x:Math.abs(Math.round(ge.pageX)-((de=(le=I.current)==null?void 0:le.x)!=null?de:0)),y:Math.abs(Math.round(ge.pageY)-((Ge=(qe=I.current)==null?void 0:qe.y)!=null?Ge:0))}},Te=ge=>{K.x<=10&&K.y<=10?ge.preventDefault():x.contains(ge.target)||P(!1),document.removeEventListener("pointermove",ue),I.current=null};return I.current!==null&&(document.addEventListener("pointermove",ue),document.addEventListener("pointerup",Te,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ue),document.removeEventListener("pointerup",Te,{capture:!0})}}},[x,P,I]),h.useEffect(()=>{const K=()=>P(!1);return window.addEventListener("blur",K),window.addEventListener("resize",K),()=>{window.removeEventListener("blur",K),window.removeEventListener("resize",K)}},[P]);const[U,$]=Lx(K=>{const ue=L().filter(le=>!le.disabled),Te=ue.find(le=>le.ref.current===document.activeElement),ge=Mx(ue,K,Te);ge&&setTimeout(()=>ge.ref.current.focus())}),Q=h.useCallback((K,ue,Te)=>{const ge=!M.current&&!Te;(g.value!==void 0&&g.value===ue||ge)&&(_(K),ge&&(M.current=!0))},[g.value]),te=h.useCallback(()=>x==null?void 0:x.focus(),[x]),pe=h.useCallback((K,ue,Te)=>{const ge=!M.current&&!Te;(g.value!==void 0&&g.value===ue||ge)&&B(K)},[g.value]),Ce=r==="popper"?rd:yx,ie=Ce===rd?{side:l,sideOffset:c,align:u,alignOffset:p,arrowPadding:f,collisionBoundary:d,collisionPadding:y,sticky:b,hideWhenDetached:v,avoidCollisions:w}:{};return a.jsx(gx,{scope:n,content:x,viewport:C,onViewportChange:T,itemRefCallback:Q,selectedItem:R,onItemLeave:te,itemTextRefCallback:pe,focusSelectedItem:q,selectedItemText:O,position:r,isPositioned:V,searchRef:U,children:a.jsx(Of,{as:Ko,allowPinchZoom:!0,children:a.jsx(_f,{asChild:!0,trapped:g.open,onMountAutoFocus:K=>{K.preventDefault()},onUnmountAutoFocus:J(o,K=>{var ue;(ue=g.trigger)==null||ue.focus({preventScroll:!0}),K.preventDefault()}),children:a.jsx(ci,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:K=>K.preventDefault(),onDismiss:()=>g.onOpenChange(!1),children:a.jsx(Ce,E(N(N({role:"listbox",id:g.contentId,"data-state":g.open?"open":"closed",dir:g.dir,onContextMenu:K=>K.preventDefault()},m),ie),{onPlaced:()=>Y(!0),ref:k,style:N({display:"flex",flexDirection:"column",outline:"none"},m.style),onKeyDown:J(m.onKeyDown,K=>{const ue=K.ctrlKey||K.altKey||K.metaKey;if(K.key==="Tab"&&K.preventDefault(),!ue&&K.key.length===1&&$(K.key),["ArrowUp","ArrowDown","Home","End"].includes(K.key)){let ge=L().filter(le=>!le.disabled).map(le=>le.ref.current);if(["ArrowUp","End"].includes(K.key)&&(ge=ge.slice().reverse()),["ArrowUp","ArrowDown"].includes(K.key)){const le=K.target,de=ge.indexOf(le);ge=ge.slice(de+1)}setTimeout(()=>z(ge)),K.preventDefault()}})}))})})})})});vx.displayName=RP;var _P="SelectItemAlignedPosition",yx=h.forwardRef((e,t)=>{const k=e,{__scopeSelect:n,onPlaced:r}=k,o=D(k,["__scopeSelect","onPlaced"]),s=vr(Hr,n),i=yr(Hr,n),[l,c]=h.useState(null),[u,p]=h.useState(null),f=Ne(t,R=>p(R)),d=Ll(n),y=h.useRef(!1),b=h.useRef(!0),{viewport:v,selectedItem:w,selectedItemText:m,focusSelectedItem:g}=i,x=h.useCallback(()=>{if(s.trigger&&s.valueNode&&l&&u&&v&&w&&m){const R=s.trigger.getBoundingClientRect(),_=u.getBoundingClientRect(),O=s.valueNode.getBoundingClientRect(),B=m.getBoundingClientRect();if(s.dir!=="rtl"){const le=B.left-_.left,de=O.left-le,qe=R.left-de,Ge=R.width+qe,Ye=Math.max(Ge,_.width),Tn=window.innerWidth-zt,xr=rm(de,[zt,Math.max(zt,Tn-Ye)]);l.style.minWidth=Ge+"px",l.style.left=xr+"px"}else{const le=_.right-B.right,de=window.innerWidth-O.right-le,qe=window.innerWidth-R.right-de,Ge=R.width+qe,Ye=Math.max(Ge,_.width),Tn=window.innerWidth-zt,xr=rm(de,[zt,Math.max(zt,Tn-Ye)]);l.style.minWidth=Ge+"px",l.style.right=xr+"px"}const L=d(),V=window.innerHeight-zt*2,Y=v.scrollHeight,M=window.getComputedStyle(u),z=parseInt(M.borderTopWidth,10),q=parseInt(M.paddingTop,10),P=parseInt(M.borderBottomWidth,10),I=parseInt(M.paddingBottom,10),U=z+q+Y+I+P,$=Math.min(w.offsetHeight*5,U),Q=window.getComputedStyle(v),te=parseInt(Q.paddingTop,10),pe=parseInt(Q.paddingBottom,10),Ce=R.top+R.height/2-zt,ie=V-Ce,$e=w.offsetHeight/2,K=w.offsetTop+$e,ue=z+q+K,Te=U-ue;if(ue<=Ce){const le=L.length>0&&w===L[L.length-1].ref.current;l.style.bottom="0px";const de=u.clientHeight-v.offsetTop-v.offsetHeight,qe=Math.max(ie,$e+(le?pe:0)+de+P),Ge=ue+qe;l.style.height=Ge+"px"}else{const le=L.length>0&&w===L[0].ref.current;l.style.top="0px";const qe=Math.max(Ce,z+v.offsetTop+(le?te:0)+$e)+Te;l.style.height=qe+"px",v.scrollTop=ue-Ce+v.offsetTop}l.style.margin=`${zt}px 0`,l.style.minHeight=$+"px",l.style.maxHeight=V+"px",r==null||r(),requestAnimationFrame(()=>y.current=!0)}},[d,s.trigger,s.valueNode,l,u,v,w,m,s.dir,r]);nt(()=>x(),[x]);const[S,C]=h.useState();nt(()=>{u&&C(window.getComputedStyle(u).zIndex)},[u]);const T=h.useCallback(R=>{R&&b.current===!0&&(x(),g==null||g(),b.current=!1)},[x,g]);return a.jsx(AP,{scope:n,contentWrapper:l,shouldExpandOnScrollRef:y,onScrollButtonChange:T,children:a.jsx("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:a.jsx(ce.div,E(N({},o),{ref:f,style:N({boxSizing:"border-box",maxHeight:"100%"},o.style)}))})})});yx.displayName=_P;var OP="SelectPopperPosition",rd=h.forwardRef((e,t)=>{const l=e,{__scopeSelect:n,align:r="start",collisionPadding:o=zt}=l,s=D(l,["__scopeSelect","align","collisionPadding"]),i=Ml(n);return a.jsx(Mv,E(N(N({},i),s),{ref:t,align:r,collisionPadding:o,style:E(N({boxSizing:"border-box"},s.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))});rd.displayName=OP;var[AP,Df]=os(Hr,{}),od="SelectViewport",xx=h.forwardRef((e,t)=>{const u=e,{__scopeSelect:n,nonce:r}=u,o=D(u,["__scopeSelect","nonce"]),s=yr(od,n),i=Df(od,n),l=Ne(t,s.onViewportChange),c=h.useRef(0);return a.jsxs(a.Fragment,{children:[a.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),a.jsx(Al.Slot,{scope:n,children:a.jsx(ce.div,E(N({"data-radix-select-viewport":"",role:"presentation"},o),{ref:l,style:N({position:"relative",flex:1,overflow:"hidden auto"},o.style),onScroll:J(o.onScroll,p=>{const f=p.currentTarget,{contentWrapper:d,shouldExpandOnScrollRef:y}=i;if(y!=null&&y.current&&d){const b=Math.abs(c.current-f.scrollTop);if(b>0){const v=window.innerHeight-zt*2,w=parseFloat(d.style.minHeight),m=parseFloat(d.style.height),g=Math.max(w,m);if(g<v){const x=g+b,S=Math.min(v,x),C=x-S;d.style.height=S+"px",d.style.bottom==="0px"&&(f.scrollTop=C>0?C:0,d.style.justifyContent="flex-end")}}}c.current=f.scrollTop})}))})]})});xx.displayName=od;var wx="SelectGroup",[LP,MP]=os(wx),IP=h.forwardRef((e,t)=>{const s=e,{__scopeSelect:n}=s,r=D(s,["__scopeSelect"]),o=No();return a.jsx(LP,{scope:n,id:o,children:a.jsx(ce.div,E(N({role:"group","aria-labelledby":o},r),{ref:t}))})});IP.displayName=wx;var bx="SelectLabel",Sx=h.forwardRef((e,t)=>{const s=e,{__scopeSelect:n}=s,r=D(s,["__scopeSelect"]),o=MP(bx,n);return a.jsx(ce.div,E(N({id:o.id},r),{ref:t}))});Sx.displayName=bx;var Qa="SelectItem",[DP,Nx]=os(Qa),Cx=h.forwardRef((e,t)=>{const g=e,{__scopeSelect:n,value:r,disabled:o=!1,textValue:s}=g,i=D(g,["__scopeSelect","value","disabled","textValue"]),l=vr(Qa,n),c=yr(Qa,n),u=l.value===r,[p,f]=h.useState(s!=null?s:""),[d,y]=h.useState(!1),b=Ne(t,x=>{var S;return(S=c.itemRefCallback)==null?void 0:S.call(c,x,r,o)}),v=No(),w=h.useRef("touch"),m=()=>{o||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return a.jsx(DP,{scope:n,value:r,disabled:o,textId:v,isSelected:u,onItemTextChange:h.useCallback(x=>{f(S=>{var C;return S||((C=x==null?void 0:x.textContent)!=null?C:"").trim()})},[]),children:a.jsx(Al.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:a.jsx(ce.div,E(N({role:"option","aria-labelledby":v,"data-highlighted":d?"":void 0,"aria-selected":u&&d,"data-state":u?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1},i),{ref:b,onFocus:J(i.onFocus,()=>y(!0)),onBlur:J(i.onBlur,()=>y(!1)),onClick:J(i.onClick,()=>{w.current!=="mouse"&&m()}),onPointerUp:J(i.onPointerUp,()=>{w.current==="mouse"&&m()}),onPointerDown:J(i.onPointerDown,x=>{w.current=x.pointerType}),onPointerMove:J(i.onPointerMove,x=>{var S;w.current=x.pointerType,o?(S=c.onItemLeave)==null||S.call(c):w.current==="mouse"&&x.currentTarget.focus({preventScroll:!0})}),onPointerLeave:J(i.onPointerLeave,x=>{var S;x.currentTarget===document.activeElement&&((S=c.onItemLeave)==null||S.call(c))}),onKeyDown:J(i.onKeyDown,x=>{var C;((C=c.searchRef)==null?void 0:C.current)!==""&&x.key===" "||(NP.includes(x.key)&&m(),x.key===" "&&x.preventDefault())})}))})})});Cx.displayName=Qa;var ws="SelectItemText",jx=h.forwardRef((e,t)=>{const m=e,{__scopeSelect:n,className:r,style:o}=m,s=D(m,["__scopeSelect","className","style"]),i=vr(ws,n),l=yr(ws,n),c=Nx(ws,n),u=kP(ws,n),[p,f]=h.useState(null),d=Ne(t,g=>f(g),c.onItemTextChange,g=>{var x;return(x=l.itemTextRefCallback)==null?void 0:x.call(l,g,c.value,c.disabled)}),y=p==null?void 0:p.textContent,b=h.useMemo(()=>a.jsx("option",{value:c.value,disabled:c.disabled,children:y},c.value),[c.disabled,c.value,y]),{onNativeOptionAdd:v,onNativeOptionRemove:w}=u;return nt(()=>(v(b),()=>w(b)),[v,w,b]),a.jsxs(a.Fragment,{children:[a.jsx(ce.span,E(N({id:c.textId},s),{ref:d})),c.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Kr.createPortal(s.children,i.valueNode):null]})});jx.displayName=ws;var Ex="SelectItemIndicator",kx=h.forwardRef((e,t)=>{const s=e,{__scopeSelect:n}=s,r=D(s,["__scopeSelect"]);return Nx(Ex,n).isSelected?a.jsx(ce.span,E(N({"aria-hidden":!0},r),{ref:t})):null});kx.displayName=Ex;var sd="SelectScrollUpButton",Px=h.forwardRef((e,t)=>{const n=yr(sd,e.__scopeSelect),r=Df(sd,e.__scopeSelect),[o,s]=h.useState(!1),i=Ne(t,r.onScrollButtonChange);return nt(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=c.scrollTop>0;s(u)};const c=n.viewport;return l(),c.addEventListener("scroll",l),()=>c.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?a.jsx(Rx,E(N({},e),{ref:i,onAutoScroll:()=>{const{viewport:l,selectedItem:c}=n;l&&c&&(l.scrollTop=l.scrollTop-c.offsetHeight)}})):null});Px.displayName=sd;var id="SelectScrollDownButton",Tx=h.forwardRef((e,t)=>{const n=yr(id,e.__scopeSelect),r=Df(id,e.__scopeSelect),[o,s]=h.useState(!1),i=Ne(t,r.onScrollButtonChange);return nt(()=>{if(n.viewport&&n.isPositioned){let l=function(){const u=c.scrollHeight-c.clientHeight,p=Math.ceil(c.scrollTop)<u;s(p)};const c=n.viewport;return l(),c.addEventListener("scroll",l),()=>c.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?a.jsx(Rx,E(N({},e),{ref:i,onAutoScroll:()=>{const{viewport:l,selectedItem:c}=n;l&&c&&(l.scrollTop=l.scrollTop+c.offsetHeight)}})):null});Tx.displayName=id;var Rx=h.forwardRef((e,t)=>{const u=e,{__scopeSelect:n,onAutoScroll:r}=u,o=D(u,["__scopeSelect","onAutoScroll"]),s=yr("SelectScrollButton",n),i=h.useRef(null),l=Ll(n),c=h.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return h.useEffect(()=>()=>c(),[c]),nt(()=>{var f;const p=l().find(d=>d.ref.current===document.activeElement);(f=p==null?void 0:p.ref.current)==null||f.scrollIntoView({block:"nearest"})},[l]),a.jsx(ce.div,E(N({"aria-hidden":!0},o),{ref:t,style:N({flexShrink:0},o.style),onPointerDown:J(o.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:J(o.onPointerMove,()=>{var p;(p=s.onItemLeave)==null||p.call(s),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:J(o.onPointerLeave,()=>{c()})}))}),FP="SelectSeparator",_x=h.forwardRef((e,t)=>{const o=e,{__scopeSelect:n}=o,r=D(o,["__scopeSelect"]);return a.jsx(ce.div,E(N({"aria-hidden":!0},r),{ref:t}))});_x.displayName=FP;var ad="SelectArrow",$P=h.forwardRef((e,t)=>{const l=e,{__scopeSelect:n}=l,r=D(l,["__scopeSelect"]),o=Ml(n),s=vr(ad,n),i=yr(ad,n);return s.open&&i.position==="popper"?a.jsx(Iv,E(N(N({},o),r),{ref:t})):null});$P.displayName=ad;function Ox(e){return e===""||e===void 0}var Ax=h.forwardRef((e,t)=>{const l=e,{value:n}=l,r=D(l,["value"]),o=h.useRef(null),s=Ne(t,o),i=bP(n);return h.useEffect(()=>{const c=o.current,u=window.HTMLSelectElement.prototype,f=Object.getOwnPropertyDescriptor(u,"value").set;if(i!==n&&f){const d=new Event("change",{bubbles:!0});f.call(c,n),c.dispatchEvent(d)}},[i,n]),a.jsx(ui,{asChild:!0,children:a.jsx("select",E(N({},r),{ref:s,defaultValue:n}))})});Ax.displayName="BubbleSelect";function Lx(e){const t=Et(e),n=h.useRef(""),r=h.useRef(0),o=h.useCallback(i=>{const l=n.current+i;t(l),function c(u){n.current=u,window.clearTimeout(r.current),u!==""&&(r.current=window.setTimeout(()=>c(""),1e3))}(l)},[t]),s=h.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return h.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function Mx(e,t,n){const o=t.length>1&&Array.from(t).every(u=>u===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=zP(e,Math.max(s,0));o.length===1&&(i=i.filter(u=>u!==n));const c=i.find(u=>u.textValue.toLowerCase().startsWith(o.toLowerCase()));return c!==n?c:void 0}function zP(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var UP=lx,Ix=ux,BP=fx,WP=px,HP=hx,Dx=mx,VP=xx,Fx=Sx,$x=Cx,QP=jx,KP=kx,zx=Px,Ux=Tx,Bx=_x;const qP=UP,GP=BP,Wx=h.forwardRef((o,r)=>{var s=o,{className:e,children:t}=s,n=D(s,["className","children"]);return a.jsxs(Ix,E(N({ref:r,className:Le("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e)},n),{children:[t,a.jsx(WP,{asChild:!0,children:a.jsx(ov,{className:"h-4 w-4 opacity-50"})})]}))});Wx.displayName=Ix.displayName;const Hx=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(zx,E(N({ref:n,className:Le("flex cursor-default items-center justify-center py-1",e)},t),{children:a.jsx(RS,{className:"h-4 w-4"})}))});Hx.displayName=zx.displayName;const Vx=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(Ux,E(N({ref:n,className:Le("flex cursor-default items-center justify-center py-1",e)},t),{children:a.jsx(ov,{className:"h-4 w-4"})}))});Vx.displayName=Ux.displayName;const Qx=h.forwardRef((s,o)=>{var i=s,{className:e,children:t,position:n="popper"}=i,r=D(i,["className","children","position"]);return a.jsx(HP,{children:a.jsxs(Dx,E(N({ref:o,className:Le("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n},r),{children:[a.jsx(Hx,{}),a.jsx(VP,{className:Le("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),a.jsx(Vx,{})]}))})});Qx.displayName=Dx.displayName;const YP=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(Fx,N({ref:n,className:Le("py-1.5 pl-8 pr-2 text-sm font-semibold",e)},t))});YP.displayName=Fx.displayName;const no=h.forwardRef((o,r)=>{var s=o,{className:e,children:t}=s,n=D(s,["className","children"]);return a.jsxs($x,E(N({ref:r,className:Le("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e)},n),{children:[a.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:a.jsx(KP,{children:a.jsx(kS,{className:"h-4 w-4"})})}),a.jsx(QP,{children:t})]}))});no.displayName=$x.displayName;const XP=h.forwardRef((r,n)=>{var o=r,{className:e}=o,t=D(o,["className"]);return a.jsx(Bx,N({ref:n,className:Le("-mx-1 my-1 h-px bg-muted",e)},t))});XP.displayName=Bx.displayName;const ZP=()=>{h.useEffect(()=>{window.scrollTo(0,0)},[]);const[e,t]=h.useState({name:"",email:"",phone:"",subject:"",message:""}),[n,r]=h.useState(!1),[o,s]=h.useState(""),[i,l]=h.useState(!1),[c,u]=h.useState(!1),[p,f]=h.useState(""),[d,y]=h.useState(!0),[b,v]=h.useState(0),w=h.useRef(null),[m,g]=h.useState({isOpen:!1,type:"success",title:"",message:"",autoClose:!1});h.useEffect(()=>{se(Ve,null,function*(){try{const V=yield EE.getRecaptchaConfig();V.success&&V.data?(f(V.data.site_key),y(V.data.enabled)):f("6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI")}catch(V){f("6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI")}})},[]);const x=L=>{t(E(N({},e),{[L.target.name]:L.target.value}))},S=L=>{t(E(N({},e),{subject:L}))},C=(L,V,Y,M=!1)=>{g({isOpen:!0,type:L,title:V,message:Y,autoClose:M})},T=()=>{g(L=>E(N({},L),{isOpen:!1}))},k=L=>{s(L),l(!1),u(!1)},R=()=>{s(""),l(!1)},_=()=>{s(""),l(!0),u(!1),C("error","CAPTCHA Error","There was an error with the CAPTCHA. Please try again.",!1)},O=()=>{u(!0),l(!1)},B=L=>se(Ve,null,function*(){var V,Y;if(L.preventDefault(),!n){if(d&&!o){C("warning","CAPTCHA Required","Please complete the CAPTCHA verification to send your message.",!1);return}r(!0);try{const M=yield kE.submitContactForm(E(N({},e),{captcha_token:o}));if(M.success)C("success","Message Sent Successfully",M.message||"Thank you for contacting us. We'll get back to you within 24 hours.",!0),t({name:"",email:"",phone:"",subject:"",message:""}),s(""),l(!1),u(!1),v(z=>z+1),(V=w.current)==null||V.reset();else throw new Error(M.message||"Failed to send message")}catch(M){console.error("Contact form submission error:",M),s(""),l(!0),u(!1),v(q=>q+1),(Y=w.current)==null||Y.reset();const z=M instanceof Error?M.message:"Unknown error occurred";z.includes("422")||z.includes("Validation")?C("error","Validation Error","Please check your form fields and CAPTCHA, then try again.",!1):C("error","Error Sending Message","There was a problem sending your message. Please try again or contact us directly.",!1)}finally{r(!1)}}});return a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50/30",children:[a.jsxs("section",{className:"relative bg-gradient-to-br from-blue-50 via-white to-gray-50 py-24 overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-10",children:[a.jsx("div",{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-blue-600 rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-1000"}),a.jsx("div",{className:"absolute bottom-1/4 left-1/3 w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-2000"}),a.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-3000"})]}),a.jsx("div",{className:"container mx-auto px-4 relative z-10",children:a.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsxs("div",{className:"inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6 animate-fade-in",children:[a.jsx($i,{className:"w-4 h-4 inline mr-2"}),"Get In Touch"]}),a.jsxs("h1",{className:"text-5xl md:text-6xl font-bold mb-6 animate-slide-up",children:["Contact"," ",a.jsx("span",{className:"bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent",children:"Our Team"})]}),a.jsx("p",{className:"text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto animate-slide-up delay-200",children:"Ready to transform your life with advanced prosthetic solutions? Our expert team is here to guide you every step of the way. Let's start your journey together."})]})})]}),a.jsx("section",{className:"py-20",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[a.jsx("div",{className:"medical-card bg-white/90 backdrop-blur-sm p-8 lg:p-10 animate-fade-in",children:a.jsxs("form",{onSubmit:B,className:"space-y-6",children:[a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{className:"group",children:[a.jsxs("label",{htmlFor:"name",className:"block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors",children:[a.jsx(fh,{className:"w-4 h-4 inline mr-2"}),"Full Name *"]}),a.jsx("input",{type:"text",id:"name",name:"name",required:!0,value:e.name,onChange:x,className:"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300",placeholder:"Enter your full name"})]}),a.jsxs("div",{className:"group",children:[a.jsxs("label",{htmlFor:"email",className:"block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors",children:[a.jsx(la,{className:"w-4 h-4 inline mr-2"}),"Email Address *"]}),a.jsx("input",{type:"email",id:"email",name:"email",required:!0,value:e.email,onChange:x,className:"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300",placeholder:"<EMAIL>"})]})]}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsxs("div",{className:"group",children:[a.jsxs("label",{htmlFor:"phone",className:"block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors",children:[a.jsx(Eu,{className:"w-4 h-4 inline mr-2"}),"Phone Number"]}),a.jsx("input",{type:"tel",id:"phone",name:"phone",value:e.phone,onChange:x,className:"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300",placeholder:"+****************"})]}),a.jsxs("div",{className:"group",children:[a.jsxs("label",{htmlFor:"subject",className:"block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors",children:[a.jsx($i,{className:"w-4 h-4 inline mr-2"}),"Subject *"]}),a.jsxs(qP,{value:e.subject,onValueChange:S,required:!0,children:[a.jsx(Wx,{className:"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300 h-auto",children:a.jsx(GP,{placeholder:"Select a subject"})}),a.jsxs(Qx,{className:"bg-white/95 backdrop-blur-sm border-2 border-gray-200 rounded-xl shadow-xl",children:[a.jsx(no,{value:"product-inquiry",className:"px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx($i,{className:"w-4 h-4 text-blue-600"}),a.jsx("span",{children:"Product Inquiry"})]})}),a.jsx(no,{value:"consultation",className:"px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(fh,{className:"w-4 h-4 text-green-600"}),a.jsx("span",{children:"Request Consultation"})]})}),a.jsx(no,{value:"support",className:"px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(Er,{className:"w-4 h-4 text-orange-600"}),a.jsx("span",{children:"Technical Support"})]})}),a.jsx(no,{value:"warranty",className:"px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(ju,{className:"w-4 h-4 text-purple-600"}),a.jsx("span",{children:"Warranty Claim"})]})}),a.jsx(no,{value:"other",className:"px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer",children:a.jsxs("div",{className:"flex items-center space-x-2",children:[a.jsx(la,{className:"w-4 h-4 text-gray-600"}),a.jsx("span",{children:"Other"})]})})]})]})]})]}),a.jsxs("div",{className:"group",children:[a.jsxs("label",{htmlFor:"message",className:"block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors",children:[a.jsx($i,{className:"w-4 h-4 inline mr-2"}),"Message *"]}),a.jsx("textarea",{id:"message",name:"message",required:!0,rows:6,value:e.message,onChange:x,className:"w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300 resize-none",placeholder:"Tell us about your needs, questions, or how we can help you..."})]}),d&&p&&a.jsxs("div",{className:"group",children:[a.jsxs("label",{className:"block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors",children:[a.jsx(Er,{className:`w-4 h-4 inline mr-2 ${o?"text-green-600":i?"text-red-600":"text-blue-600"}`}),"Security Verification *",o&&a.jsx("span",{className:"ml-2 text-xs text-green-600 font-medium",children:"✓ Verified"}),i&&a.jsx("span",{className:"ml-2 text-xs text-red-600 font-medium",children:"⚠ Error"})]}),a.jsxs("div",{className:`bg-gradient-to-br from-white/90 to-blue-50/50 backdrop-blur-sm border-2 rounded-xl p-6 transition-all duration-300 ${o?"border-green-300 bg-green-50/30":i?"border-red-300 bg-red-50/30":"border-gray-200 hover:border-blue-300"}`,children:[a.jsx("div",{className:"text-center mb-4",children:a.jsxs("div",{className:"inline-flex items-center space-x-2 text-sm text-gray-600",children:[a.jsx(Er,{className:"w-4 h-4"}),a.jsx("span",{children:"Please verify you're human to send your message"})]})}),a.jsx(Ey,{ref:w,siteKey:p,onVerify:k,onExpired:R,onError:_,onLoad:O,theme:"light",size:"normal",className:`recaptcha-medical ${i?"recaptcha-error":o?"recaptcha-success":""}`,containerClassName:"transform transition-all duration-300"},b),c&&a.jsx("div",{className:"text-center mt-3",children:a.jsxs("div",{className:"inline-flex items-center space-x-2 text-sm text-blue-600",children:[a.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),a.jsx("span",{children:"Loading verification..."})]})})]})]}),!d&&a.jsx("div",{className:"text-center p-4 bg-yellow-50 border border-yellow-200 rounded-xl",children:a.jsxs("div",{className:"inline-flex items-center space-x-2 text-sm text-yellow-700",children:[a.jsx(Er,{className:"w-4 h-4"}),a.jsx("span",{children:"CAPTCHA verification is currently disabled"})]})}),a.jsxs("button",{type:"submit",disabled:n||d&&!o,className:`medical-button w-full group flex items-center justify-center space-x-3 py-4 px-8 text-lg font-semibold ${n||d&&!o?"opacity-75 cursor-not-allowed":""}`,children:[a.jsx(WS,{className:`w-5 h-5 transition-transform duration-300 ${n?"animate-pulse":"group-hover:translate-x-1"}`}),a.jsx("span",{children:n?"Sending...":"Send Message"})]})]})}),a.jsx("div",{className:"space-y-8 animate-fade-in delay-300",children:a.jsx("div",{className:"medical-card bg-gradient-to-br from-blue-50 to-white p-8 lg:p-10",children:a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[a.jsx("div",{className:"rounded-xl",children:a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0",children:a.jsx(sv,{className:"w-6 h-6 text-white"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-bold text-gray-900 text-lg mb-2",children:"Visit Our Office"}),a.jsxs("p",{className:"text-gray-600 leading-relaxed",children:["EDGEROOK DR",a.jsx("br",{}),"TORONTO ON M9V 5E8",a.jsx("br",{}),"CANADA"]})]})]})}),a.jsx("div",{className:"rounded-xl",children:a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0",children:a.jsx(Eu,{className:"w-6 h-6 text-white"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-bold text-gray-900 text-lg mb-2",children:"Call Us"}),a.jsxs("p",{className:"text-gray-600 leading-relaxed",children:[a.jsx("a",{href:"tel:+16476466640",className:"hover:text-blue-600 transition-colors",children:"+****************"}),a.jsx("br",{}),a.jsx("span",{className:"text-sm text-gray-500",children:"24/7 Technical Support"})]})]})]})}),a.jsx("div",{className:"rounded-xl",children:a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0",children:a.jsx(la,{className:"w-6 h-6 text-white"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-bold text-gray-900 text-lg mb-2",children:"Email Us"}),a.jsxs("p",{className:"text-gray-600 leading-relaxed",children:[a.jsx("a",{href:"mailto:<EMAIL>",className:"hover:text-blue-600 transition-colors",children:"<EMAIL>"}),a.jsx("br",{}),a.jsx("span",{className:"text-sm text-gray-500",children:"Business Inquiries"})]})]})]})}),a.jsx("div",{className:"rounded-xl",children:a.jsxs("div",{className:"flex items-start space-x-4",children:[a.jsx("div",{className:"w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0",children:a.jsx(ju,{className:"w-6 h-6 text-white"})}),a.jsxs("div",{children:[a.jsx("h3",{className:"font-bold text-gray-900 text-lg mb-2",children:"Business Hours"}),a.jsxs("p",{className:"text-gray-600 leading-relaxed",children:["Monday - Friday: 8:00 AM - 6:00 PM",a.jsx("br",{}),"Saturday: 9:00 AM - 3:00 PM",a.jsx("br",{}),"Sunday: Closed"]})]})]})})]})})})]})})}),a.jsx(yP,{isOpen:m.isOpen,onClose:T,type:m.type,title:m.title,message:m.message,autoClose:m.autoClose,autoCloseDelay:5e3})]})},JP=()=>{const[e,t]=h.useState("All"),[n,r]=h.useState("name"),[o,s]=h.useState(""),[i,l]=h.useState("grid"),[c,u]=h.useState(!1),{data:p,isLoading:f,error:d}=yy({per_page:50,sort:n==="name"?"name":n==="category"?"category.name":"created_at"}),{data:y,isLoading:b}=RE(),v=f||b,w=h.useMemo(()=>(p==null?void 0:p.data)||[],[p]),m=h.useMemo(()=>["All",...((y==null?void 0:y.data)||[]).map(C=>C.name)],[y]),g=h.useMemo(()=>w.filter(S=>{const C=e==="All"||S.category===e,T=S.title.toLowerCase().includes(o.toLowerCase())||S.description.toLowerCase().includes(o.toLowerCase());return C&&T}),[w,e,o]),x=h.useMemo(()=>[...g].sort((S,C)=>{switch(n){case"name":return S.title.localeCompare(C.title);case"category":return S.category.localeCompare(C.category);case"price":return S.price.localeCompare(C.price);default:return 0}}),[g,n]);return h.useEffect(()=>{window.scrollTo(0,0)},[]),d?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h1",{className:"text-4xl font-bold mb-4 text-red-600",children:"Error Loading Products"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8",children:d instanceof Error?d.message:"Failed to load products"}),a.jsx("button",{onClick:()=>window.location.reload(),className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700",children:"Try Again"})]})}):a.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-green-50/30",children:[a.jsxs("section",{className:"bg-gradient-to-br from-gray-50 via-white to-green-50 py-24 relative overflow-hidden",children:[a.jsxs("div",{className:"absolute inset-0 opacity-30",children:[a.jsx("div",{className:"absolute top-1/4 left-1/4 w-2 h-2 bg-green-300 rounded-full animate-pulse"}),a.jsx("div",{className:"absolute top-1/3 right-1/3 w-1 h-1 bg-green-400 rounded-full animate-pulse delay-1000"}),a.jsx("div",{className:"absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse delay-2000"}),a.jsx("div",{className:"absolute bottom-1/3 right-1/4 w-1 h-1 bg-green-600 rounded-full animate-pulse delay-3000"})]}),a.jsx("div",{className:"container mx-auto px-4 relative z-10",children:a.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsx("span",{className:"inline-block px-4 py-2 bg-green-100 text-green-700 rounded-full text-sm font-semibold mb-6",children:"Our Product Range"}),a.jsxs("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-8 leading-tight",children:["Advanced"," ",a.jsx("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:"Prosthetic Solutions"})]}),a.jsx("p",{className:"text-xl md:text-2xl text-gray-600 leading-relaxed max-w-3xl mx-auto",children:"Explore our comprehensive range of cutting-edge prosthetic solutions designed to meet diverse needs and lifestyles."})]})}),a.jsx("div",{className:"absolute top-20 left-10 w-20 h-20 bg-green-200 rounded-full opacity-20 animate-pulse"}),a.jsx("div",{className:"absolute bottom-20 right-10 w-32 h-32 bg-green-300 rounded-full opacity-20 animate-pulse delay-1000"})]}),a.jsx("section",{className:"py-8 bg-white/80 backdrop-blur-sm border-b border-gray-200/50",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsx("div",{className:"mb-6",children:a.jsxs("div",{className:"relative max-w-2xl mx-auto",children:[a.jsx("input",{type:"text",placeholder:"Search products...",value:o,onChange:S=>s(S.target.value),className:"w-full pl-12 pr-4 py-4 border border-gray-300 rounded-2xl focus:ring-4 focus:ring-green-100 focus:border-green-400 transition-all duration-300 text-lg bg-white/90 backdrop-blur-sm"}),a.jsx(Da,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-6 h-6"})]})}),a.jsxs("div",{className:"flex flex-col lg:flex-row justify-between items-center gap-6",children:[a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsxs("button",{onClick:()=>u(!c),className:"lg:hidden flex items-center space-x-2 px-4 py-2 bg-green-100 text-green-700 rounded-xl font-medium hover:bg-green-200 transition-colors",children:[a.jsx(HS,{className:"w-4 h-4"}),a.jsx("span",{children:"Filters"})]}),a.jsx("div",{className:`${c?"block":"hidden lg:block"} w-full lg:w-auto`,children:a.jsx("div",{className:"flex flex-wrap gap-3",children:m.map(S=>a.jsx("button",{onClick:()=>t(S),className:`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${e===S?"bg-gradient-to-r from-green-600 to-green-700 text-white shadow-lg transform scale-105":"bg-gray-100 text-gray-700 hover:bg-green-100 hover:text-green-700 hover:scale-105"}`,children:S},S))})})]}),a.jsxs("div",{className:"flex items-center space-x-4",children:[a.jsxs("div",{className:"flex items-center bg-gray-100 rounded-xl p-1",children:[a.jsx("button",{onClick:()=>l("grid"),className:`p-2 rounded-lg transition-all duration-300 ${i==="grid"?"bg-white shadow-sm text-green-600":"text-gray-500 hover:text-gray-700"}`,children:a.jsx(IS,{className:"w-5 h-5"})}),a.jsx("button",{onClick:()=>l("list"),className:`p-2 rounded-lg transition-all duration-300 ${i==="list"?"bg-white shadow-sm text-green-600":"text-gray-500 hover:text-gray-700"}`,children:a.jsx(zS,{className:"w-5 h-5"})})]}),a.jsxs("select",{value:n,onChange:S=>r(S.target.value),className:"border border-gray-300 rounded-xl px-4 py-3 focus:ring-4 focus:ring-green-100 focus:border-green-400 transition-all duration-300 bg-white/90 backdrop-blur-sm",children:[a.jsx("option",{value:"name",children:"Sort by Name"}),a.jsx("option",{value:"category",children:"Sort by Category"}),a.jsx("option",{value:"price",children:"Sort by Price"})]})]})]})]})}),a.jsx("section",{className:"py-16",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsx("div",{className:"flex items-center justify-between mb-8",children:a.jsxs("div",{children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:e==="All"?"All Products":e}),a.jsxs("p",{className:"text-gray-600",children:[v?"Loading...":`Showing ${x.length} products`,e!=="All"&&!v&&` in ${e}`,o&&!v&&` matching "${o}"`]})]})}),v?a.jsx(jf,{count:6}):a.jsxs(a.Fragment,{children:[a.jsx("div",{className:`${i==="grid"?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8":"space-y-6"}`,children:x.map((S,C)=>a.jsx("div",{className:"fade-in",style:{animationDelay:`${C*.1}s`},children:a.jsx(Cf,{product:S})},S.id))}),x.length===0&&a.jsxs("div",{className:"text-center py-20",children:[a.jsx("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6",children:a.jsx(Da,{className:"w-12 h-12 text-gray-400"})}),a.jsx("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"No products found"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-md mx-auto",children:"We couldn't find any products matching your criteria. Try adjusting your filters or search terms."}),a.jsx("button",{onClick:()=>{t("All"),s("")},className:"medical-button",children:"Clear All Filters"})]})]})]})}),a.jsx("section",{className:"py-20 bg-gradient-to-br from-gray-800 via-gray-900 to-green-800 text-white",children:a.jsxs("div",{className:"container mx-auto px-4 text-center",children:[a.jsx("h2",{className:"text-3xl md:text-4xl font-bold mb-4",children:"Need Help Choosing?"}),a.jsx("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Our expert team is here to help you find the perfect prosthetic solution for your specific needs and lifestyle."}),a.jsx("a",{href:"/contact",className:"bg-white text-gray-800 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 inline-block",children:"Schedule a Consultation"})]})})]})},e4=()=>{const{id:e}=V2(),[t,n]=h.useState(0),{data:r,isLoading:o,error:s}=yy(),i=(r==null?void 0:r.data)||[],l=i.find(c=>c.id===e);return h.useEffect(()=>{window.scrollTo(0,0)},[e]),o?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsx(jf,{count:1})}):s?a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h1",{className:"text-4xl font-bold mb-4 text-red-600",children:"Error Loading Product"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"Failed to load product details."}),a.jsx(Ae,{to:"/products",className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700",children:"Back to Products"})]})}):l?a.jsxs("div",{className:"min-h-screen bg-white",children:[a.jsx("div",{className:"bg-gray-50 py-4",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsxs("nav",{className:"text-sm",children:[a.jsx(Ae,{to:"/",className:"text-blue-600 hover:underline",children:"Home"})," > ",a.jsx(Ae,{to:"/products",className:"text-blue-600 hover:underline",children:"Products"})," > ",a.jsx("span",{className:"text-gray-600",children:l.title})]})})}),a.jsxs("div",{className:"container mx-auto px-4 py-12",children:[a.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[a.jsxs("div",{className:"space-y-4",children:[a.jsx("div",{className:"aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg overflow-hidden",children:a.jsx("img",{src:l.images[t],alt:l.title,className:"w-full h-96 object-cover"})}),a.jsx("div",{className:"flex space-x-2 overflow-x-auto",children:l.images.map((c,u)=>a.jsx("button",{onClick:()=>n(u),className:`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${t===u?"border-blue-600":"border-gray-300"}`,children:a.jsx("img",{src:c,alt:`${l.title} view ${u+1}`,className:"w-full h-full object-cover"})},u))})]}),a.jsxs("div",{className:"space-y-8",children:[a.jsxs("div",{children:[a.jsxs("div",{className:"flex items-center justify-between mb-4",children:[a.jsx("span",{className:"text-sm font-medium text-blue-600 bg-blue-100 px-3 py-1 rounded-full",children:l.category}),a.jsx("span",{className:"text-2xl font-bold text-gray-900",children:l.price})]}),a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-4",children:l.title}),a.jsx("p",{className:"text-lg text-gray-700 leading-relaxed",children:l.description})]}),a.jsxs("div",{children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-4",children:"Specifications"}),a.jsx("div",{className:"bg-gray-50 rounded-lg p-6",children:a.jsx("dl",{className:"grid grid-cols-1 gap-4",children:Object.entries(l.specs).map(([c,u])=>a.jsxs("div",{className:"flex justify-between py-2 border-b border-gray-200 last:border-b-0",children:[a.jsxs("dt",{className:"font-medium text-gray-900",children:[c,":"]}),a.jsx("dd",{className:"text-gray-700",children:u})]},c))})})]})]})]}),a.jsxs("div",{className:"mt-20",children:[a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Related Products"}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:i.filter(c=>c.category===l.category&&c.id!==l.id).slice(0,3).map(c=>a.jsx(Ae,{to:`/product/${c.id}`,className:"group",children:a.jsxs("div",{className:"bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden",children:[a.jsx("img",{src:c.image,alt:c.title,className:"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"}),a.jsxs("div",{className:"p-4",children:[a.jsx("h3",{className:"text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors",children:c.title}),a.jsx("p",{className:"text-gray-600 text-sm mt-2",children:c.category})]})]})},c.id))})]})]})]}):a.jsx("div",{className:"min-h-screen flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h1",{className:"text-4xl font-bold mb-4",children:"Product Not Found"}),a.jsx("p",{className:"text-xl text-gray-600 mb-8",children:"The product you're looking for doesn't exist."}),a.jsx(Ae,{to:"/products",className:"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700",children:"Back to Products"})]})})},kc=[{id:"1",title:"Advanced Lower Limb Prosthetic",description:"State-of-the-art lower limb prosthetic with microprocessor knee technology for natural movement and stability.",price:"Contact for Pricing",category:"Lower Limb",image:"/src/assets/photo_6048487991124541851_y.jpg",images:["/src/assets/photo_6048487991124541851_y.jpg","/src/assets/photo_6048487991124541852_y.jpg","/src/assets/photo_6048487991124541853_y.jpg"],specs:{Weight:"2.1 kg",Material:"Carbon fiber composite","Knee Type":"Microprocessor controlled","Battery Life":"24-48 hours","Water Resistance":"IP67 rated",Warranty:"2 years"}},{id:"2",title:"Myoelectric Upper Limb Prosthetic",description:"Advanced myoelectric arm prosthetic with multiple grip patterns and intuitive muscle signal control.",price:"Contact for Pricing",category:"Upper Limb",image:"/src/assets/photo_6048487991124541852_y.jpg",images:["/src/assets/photo_6048487991124541852_y.jpg","/src/assets/photo_6048487991124541853_y.jpg","/src/assets/photo_6048487991124541854_y.jpg"],specs:{Weight:"580g",Material:"Titanium alloy","Grip Patterns":"12 different patterns","Battery Life":"18-24 hours",Control:"EMG signal based",Warranty:"3 years"}},{id:"3",title:"Pediatric Prosthetic Leg",description:"Lightweight, adjustable prosthetic leg designed specifically for growing children with colorful design options.",price:"Contact for Pricing",category:"Pediatric",image:"/src/assets/photo_6048487991124541853_y.jpg",images:["/src/assets/photo_6048487991124541853_y.jpg","/src/assets/photo_6048487991124541854_y.jpg","/src/assets/photo_6048487991124541851_y.jpg"],specs:{Weight:"0.9 kg",Material:"Lightweight polymer",Adjustability:"Growth compensating","Age Range":"3-16 years","Color Options":"8 available",Warranty:"18 months"}},{id:"4",title:"Sports Performance Prosthetic",description:"High-performance running prosthetic designed for athletes with carbon fiber spring technology.",price:"Contact for Pricing",category:"Sports",image:"/src/assets/photo_6048487991124541854_y.jpg",images:["/src/assets/photo_6048487991124541854_y.jpg","/src/assets/photo_6048487991124541851_y.jpg","/src/assets/photo_6048487991124541852_y.jpg"],specs:{Weight:"1.2 kg",Material:"Carbon fiber","Spring Rate":"Customizable","Activity Level":"High impact sports",Certification:"Paralympic approved",Warranty:"2 years"}},{id:"5",title:"Basic Mechanical Arm",description:"Reliable mechanical arm prosthetic with body-powered operation and durable construction.",price:"Contact for Pricing",category:"Upper Limb",image:"/src/assets/photo_6048487991124541851_y.jpg",images:["/src/assets/photo_6048487991124541851_y.jpg","/src/assets/photo_6048487991124541853_y.jpg"],specs:{Weight:"1.1 kg",Material:"Aluminum alloy",Operation:"Body-powered cable","Grip Force":"22 lbs",Maintenance:"Low maintenance",Warranty:"3 years"}},{id:"6",title:"Waterproof Activity Prosthetic",description:"Specialized prosthetic designed for water activities with corrosion-resistant materials.",price:"Contact for Pricing",category:"Specialty",image:"/src/assets/photo_6048487991124541852_y.jpg",images:["/src/assets/photo_6048487991124541852_y.jpg","/src/assets/photo_6048487991124541854_y.jpg"],specs:{Weight:"1.8 kg",Material:"Marine-grade titanium","Water Rating":"Fully submersible","Depth Rating":"50 meters",Corrosion:"Saltwater resistant",Warranty:"2 years"}}],t4=()=>{const[e]=vE(),t=e.get("q")||"",[n,r]=h.useState(kc);return h.useEffect(()=>{if(t.trim()){const o=kc.filter(s=>s.title.toLowerCase().includes(t.toLowerCase())||s.description.toLowerCase().includes(t.toLowerCase())||s.category.toLowerCase().includes(t.toLowerCase())||Object.values(s.specs).some(i=>i.toLowerCase().includes(t.toLowerCase())));r(o)}else r(kc)},[t]),a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("section",{className:"bg-gradient-to-br from-blue-50 to-blue-100 py-20",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:"Search Results"}),t&&a.jsxs("p",{className:"text-xl text-gray-700 leading-relaxed",children:['Showing results for "',a.jsx("span",{className:"font-semibold",children:t}),'"']})]})})}),a.jsx("section",{className:"py-12",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsxs("div",{className:"mb-6 flex items-center justify-between",children:[a.jsxs("p",{className:"text-gray-600",children:["Found ",n.length," product",n.length!==1?"s":"",t&&` matching "${t}"`]}),t&&a.jsx(Ae,{to:"/products",className:"text-blue-600 hover:text-blue-800 font-medium",children:"View All Products"})]}),n.length>0?a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:n.map(o=>a.jsx(Cf,{product:o},o.id))}):a.jsxs("div",{className:"text-center py-12",children:[a.jsx("div",{className:"text-gray-400 text-6xl mb-4",children:"🔍"}),a.jsx("h3",{className:"text-2xl font-semibold text-gray-900 mb-2",children:"No results found"}),a.jsxs("p",{className:"text-gray-600 mb-6",children:[`We couldn't find any products matching "`,t,'". Try:']}),a.jsxs("ul",{className:"text-gray-600 space-y-2 mb-8",children:[a.jsx("li",{children:"• Checking your spelling"}),a.jsx("li",{children:"• Using different keywords"}),a.jsx("li",{children:"• Searching for more general terms"})]}),a.jsx(Ae,{to:"/products",className:"bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 inline-block",children:"Browse All Products"})]})]})}),n.length===0&&a.jsx("section",{className:"py-12 bg-white",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"Popular Searches"}),a.jsx("div",{className:"flex flex-wrap justify-center gap-4",children:["lower limb","upper limb","pediatric","sports","myoelectric","microprocessor"].map(o=>a.jsx(Ae,{to:`/search?q=${encodeURIComponent(o)}`,className:"bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full text-gray-700 transition-colors",children:o},o))})]})})]})},n4=()=>{const[e,t]=h.useState("all"),[n,r]=h.useState(""),{data:o,isLoading:s,error:i}=OE({category:e==="all"?void 0:e,search:n||void 0}),{data:l,isLoading:c}=AE(),u=(o==null?void 0:o.data)||[],p=(l==null?void 0:l.data)||[];h.useEffect(()=>{window.scrollTo(0,0)},[]);const f=d=>{if(d.download_url){const y=document.createElement("a");y.href=d.download_url,y.download=`${d.name}.pdf`,document.body.appendChild(y),y.click(),document.body.removeChild(y)}else alert("Download URL not available for this catalog.")};return a.jsxs("div",{className:"min-h-screen bg-gray-50",children:[a.jsx("section",{className:"bg-gradient-to-br from-gray-50 via-white to-green-50 py-20",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsxs("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsxs("h1",{className:"text-4xl md:text-5xl font-bold text-gray-900 mb-6",children:["Product ",a.jsx("span",{className:"bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent",children:"Catalogs"})]}),a.jsx("p",{className:"text-xl text-gray-700 leading-relaxed",children:"Download comprehensive product catalogs with detailed specifications, compatibility guides, and technical documentation."})]})})}),a.jsx("section",{className:"py-8 bg-white border-b",children:a.jsx("div",{className:"container mx-auto px-4",children:a.jsxs("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[a.jsx("div",{className:"flex-1 max-w-md",children:a.jsx("input",{type:"text",placeholder:"Search catalogs...",value:n,onChange:d=>r(d.target.value),className:"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"})}),a.jsxs("div",{className:"flex gap-2 flex-wrap",children:[a.jsx("button",{onClick:()=>t("all"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${e==="all"?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"All Categories"}),p.map(d=>a.jsx("button",{onClick:()=>t(d),className:`px-4 py-2 rounded-lg font-medium transition-colors ${e===d?"bg-green-600 text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:d},d))]})]})})}),a.jsx("section",{className:"py-12",children:a.jsx("div",{className:"container mx-auto px-4",children:s?a.jsx("div",{className:"flex justify-center items-center py-20",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"})}):i?a.jsxs("div",{className:"text-center py-20",children:[a.jsx("h3",{className:"text-xl font-semibold text-red-600 mb-4",children:"Error Loading Catalogs"}),a.jsx("p",{className:"text-gray-600 mb-8",children:i instanceof Error?i.message:"Failed to load catalogs"}),a.jsx("button",{onClick:()=>window.location.reload(),className:"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700",children:"Try Again"})]}):u.length===0?a.jsxs("div",{className:"text-center py-20",children:[a.jsx("h3",{className:"text-xl font-semibold text-gray-600 mb-4",children:"No Catalogs Found"}),a.jsx("p",{className:"text-gray-500",children:n||e!=="all"?"Try adjusting your search or filter criteria.":"No catalogs are currently available."})]}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:u.map(d=>a.jsxs("div",{className:"bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden",children:[a.jsx("div",{className:"aspect-[16/10] bg-gradient-to-br from-green-100 to-green-200 overflow-hidden relative",children:d.image_url?a.jsx("img",{src:d.image_url,alt:d.name,className:"w-full h-full object-cover transition-transform duration-300 hover:scale-105",onError:y=>{const v=y.target.parentElement;v&&(v.innerHTML=`
                              <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-100 to-green-200">
                                <div class="text-center">
                                  <div class="text-4xl mb-2">📄</div>
                                  <div class="text-sm text-green-700 font-medium">PDF Catalog</div>
                                </div>
                              </div>
                            `)}}):a.jsx("div",{className:"w-full h-full flex items-center justify-center",children:a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"text-4xl mb-2",children:"📄"}),a.jsx("div",{className:"text-sm text-green-700 font-medium",children:"PDF Catalog"})]})})}),a.jsxs("div",{className:"p-6",children:[a.jsxs("div",{className:"flex items-center justify-between mb-3",children:[a.jsx("span",{className:"text-xs font-medium text-green-700 bg-green-100 px-2 py-1 rounded-full",children:d.category}),a.jsxs("div",{className:"flex flex-col items-end",children:[a.jsx("span",{className:"text-sm text-gray-500",children:d.file_size_formatted||"N/A"}),d.version&&a.jsxs("span",{className:"text-xs text-gray-400",children:["v",d.version]})]})]}),a.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-3",children:d.name}),a.jsx("p",{className:"text-gray-600 text-sm mb-4 line-clamp-3",children:d.description}),a.jsxs("div",{className:"flex items-center justify-between mb-4 text-xs text-gray-500",children:[a.jsxs("span",{children:["Downloads: ",d.download_count]}),d.published_date&&a.jsxs("span",{children:["Published: ",new Date(d.published_date).toLocaleDateString()]})]}),a.jsxs("button",{onClick:()=>f(d),className:"w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2",children:[a.jsx("span",{children:"📥"}),a.jsx("span",{children:"Download PDF"})]})]})]},d.id))})})}),a.jsx("section",{className:"py-20 bg-gray-50",children:a.jsxs("div",{className:"container mx-auto px-4",children:[a.jsx("div",{className:"text-center mb-12",children:a.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"What You'll Find in Our Catalogs"})}),a.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[a.jsxs("div",{className:"text-center p-6",children:[a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx("span",{className:"text-2xl",children:"📊"})}),a.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Detailed Specifications"}),a.jsx("p",{className:"text-gray-600",children:"Complete technical specifications, measurements, and performance data."})]}),a.jsxs("div",{className:"text-center p-6",children:[a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx("span",{className:"text-2xl",children:"🔧"})}),a.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Installation Guides"}),a.jsx("p",{className:"text-gray-600",children:"Step-by-step installation and setup instructions for professionals."})]}),a.jsxs("div",{className:"text-center p-6",children:[a.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx("span",{className:"text-2xl",children:"🎯"})}),a.jsx("h3",{className:"text-xl font-semibold mb-3",children:"Compatibility Information"}),a.jsx("p",{className:"text-gray-600",children:"Comprehensive compatibility charts and component matching guides."})]})]})]})})]})},r4=()=>{const e=gr();return h.useEffect(()=>{console.error("404 Error: User attempted to access non-existent route:",e.pathname)},[e.pathname]),a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-100",children:a.jsxs("div",{className:"text-center",children:[a.jsx("h1",{className:"text-4xl font-bold mb-4",children:"404"}),a.jsx("p",{className:"text-xl text-gray-600 mb-4",children:"Oops! Page not found"}),a.jsx("a",{href:"/",className:"text-blue-500 hover:text-blue-700 underline",children:"Return to Home"})]})})},o4=new Zj,s4=()=>a.jsx(r2,{client:o4,children:a.jsxs(Mj,{children:[a.jsx(ON,{}),a.jsx(aC,{}),a.jsxs(pE,{children:[a.jsx(bE,{}),a.jsxs("div",{className:"min-h-screen flex flex-col",children:[a.jsx(yE,{}),a.jsx("main",{className:"flex-grow",children:a.jsxs(sE,{children:[a.jsx(gn,{path:"/",element:a.jsx(LE,{})}),a.jsx(gn,{path:"/about",element:a.jsx(ME,{})}),a.jsx(gn,{path:"/contact",element:a.jsx(ZP,{})}),a.jsx(gn,{path:"/products",element:a.jsx(JP,{})}),a.jsx(gn,{path:"/product/:id",element:a.jsx(e4,{})}),a.jsx(gn,{path:"/search",element:a.jsx(t4,{})}),a.jsx(gn,{path:"/catalogs",element:a.jsx(n4,{})}),a.jsx(gn,{path:"*",element:a.jsx(r4,{})})]})}),a.jsx(wE,{})]})]})]})});k0(document.getElementById("root")).render(a.jsx(s4,{}))});export default i4();
