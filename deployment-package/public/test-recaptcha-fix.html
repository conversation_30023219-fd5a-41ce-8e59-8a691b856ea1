<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>reCAPTCHA Fix Test - RELIFE</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h2 {
            color: #007cba;
            margin-top: 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007cba;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🏥 RELIFE reCAPTCHA Fix Test</h1>
        
        <div class="test-section">
            <h2>📋 Test Results</h2>
            <div id="testResults">
                <div class="info">Click "Run Tests" to verify the reCAPTCHA fix</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 Test Controls</h2>
            <button onclick="runTests()">Run Tests</button>
            <button onclick="clearResults()">Clear Results</button>
            <button onclick="openContactPage()">Open Contact Page</button>
            <button onclick="testAPI()">Test API</button>
        </div>

        <div class="test-section">
            <h2>📊 Console Output</h2>
            <div id="consoleOutput" class="console-output">
                Console output will appear here...
            </div>
        </div>

        <div class="test-section">
            <h2>ℹ️ Fix Details</h2>
            <p><strong>Issues Fixed:</strong></p>
            <ul>
                <li>✅ Replaced custom reCAPTCHA implementation with standard <code>react-google-recaptcha</code> library</li>
                <li>✅ Added proper component lifecycle management with useImperativeHandle</li>
                <li>✅ Implemented key-based re-rendering to prevent "already rendered" errors</li>
                <li>✅ Added proper cleanup and reset functionality</li>
                <li>✅ Simplified event handling with standard library callbacks</li>
            </ul>
            
            <p><strong>Expected Behavior:</strong></p>
            <ul>
                <li>reCAPTCHA widget renders correctly on first load</li>
                <li>No console errors about "already rendered in this element"</li>
                <li>Widget can be completed and reset properly</li>
                <li>Form submission works with valid reCAPTCHA tokens</li>
                <li>Component re-renders cleanly after form submission</li>
            </ul>
        </div>
    </div>

    <script>
        let consoleOutput = document.getElementById('consoleOutput');
        let testResults = document.getElementById('testResults');

        // Capture console messages
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        const originalConsoleWarn = console.warn;

        function logToOutput(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            consoleOutput.textContent += logMessage;
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }

        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            logToOutput(args.join(' '), 'log');
        };

        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            logToOutput(args.join(' '), 'error');
        };

        console.warn = function(...args) {
            originalConsoleWarn.apply(console, args);
            logToOutput(args.join(' '), 'warn');
        };

        async function runTests() {
            clearResults();
            addResult('🔄 Starting reCAPTCHA fix tests...', 'info');

            // Test 1: Check if React app loads
            try {
                const response = await fetch('/');
                if (response.ok) {
                    addResult('✅ React application loads successfully', 'success');
                } else {
                    addResult('❌ React application failed to load', 'error');
                }
            } catch (error) {
                addResult('❌ Error loading React application: ' + error.message, 'error');
            }

            // Test 2: Check reCAPTCHA configuration
            try {
                const response = await fetch('/api/v1/config/recaptcha');
                const data = await response.json();
                if (data.success && data.data.enabled) {
                    addResult('✅ reCAPTCHA configuration is valid and enabled', 'success');
                    addResult(`   Site Key: ${data.data.site_key}`, 'info');
                } else {
                    addResult('❌ reCAPTCHA configuration is invalid or disabled', 'error');
                }
            } catch (error) {
                addResult('❌ Error fetching reCAPTCHA config: ' + error.message, 'error');
            }

            // Test 3: Check contact page
            try {
                const response = await fetch('/contact');
                if (response.ok) {
                    addResult('✅ Contact page loads successfully', 'success');
                } else {
                    addResult('❌ Contact page failed to load', 'error');
                }
            } catch (error) {
                addResult('❌ Error loading contact page: ' + error.message, 'error');
            }

            // Test 4: Check for JavaScript errors
            setTimeout(() => {
                const errorCount = consoleOutput.textContent.split('ERROR:').length - 1;
                if (errorCount === 0) {
                    addResult('✅ No JavaScript errors detected', 'success');
                } else {
                    addResult(`⚠️ ${errorCount} JavaScript error(s) detected - check console`, 'error');
                }

                addResult('🎉 Test suite completed! Check the results above.', 'info');
            }, 2000);
        }

        function addResult(message, type) {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = message;
            testResults.appendChild(div);
        }

        function clearResults() {
            testResults.innerHTML = '';
            consoleOutput.textContent = 'Console cleared...\n';
        }

        function openContactPage() {
            window.open('/contact', '_blank');
            addResult('📱 Opened contact page in new tab', 'info');
        }

        async function testAPI() {
            try {
                const response = await fetch('/api/v1/config/recaptcha');
                const data = await response.json();
                addResult('📡 API Response: ' + JSON.stringify(data, null, 2), 'info');
            } catch (error) {
                addResult('❌ API Test Error: ' + error.message, 'error');
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            setTimeout(runTests, 1000);
        });
    </script>
</body>
</html>
