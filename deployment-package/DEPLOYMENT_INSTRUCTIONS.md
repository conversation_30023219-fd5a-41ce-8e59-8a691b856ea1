# RELIFE Medical Technologies - Deployment Instructions

## 📋 Pre-Deployment Checklist

### 1. Server Requirements
- PHP 8.2 or higher
- MySQL 5.7+ or MariaDB 10.3+
- Apache/Nginx web server
- SSL certificate (recommended)

### 2. Required PHP Extensions
- BCMath
- Ctype
- Fileinfo
- JSON
- Mbstring
- OpenSSL
- PDO
- Tokenizer
- XML
- GD or Imagick (for image processing)

## 🚀 Deployment Steps

### 1. Upload Files
1. Extract the deployment package
2. Upload all files to your hosting account
3. Point your domain to the `public` directory

### 2. Install PHP Dependencies
```bash
# Install Composer dependencies (vendor directory not included in package)
composer install --optimize-autoloader --no-dev --no-interaction

# Verify installation completed successfully
php artisan --version
```

### 3. Environment Configuration
1. Copy `.env.production` to `.env`
2. Update database credentials in `.env`
3. Update email configuration in `.env`
4. Generate application key: `php artisan key:generate`

### 4. Database Setup
```bash
# Run migrations
php artisan migrate --force

# Seed initial data (optional)
php artisan db:seed --force
```

### 5. Storage Setup
```bash
# Create storage link
php artisan storage:link

# Set proper permissions
chmod -R 755 storage/
chmod -R 755 bootstrap/cache/
```

### 6. Cache Optimization
```bash
# Cache configurations for production
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### 7. File Permissions
Set the following permissions:
- `storage/` and `bootstrap/cache/` directories: 755
- All files: 644
- `artisan` file: 755

## 🔧 Configuration Notes

### Frontend Integration
- React app is served from `/build/index.html`
- API endpoints available at `/api/v1/*`
- Admin panel accessible at `/admin`

### URL Structure
- Frontend routes: `/`, `/about`, `/contact`, `/products`, etc.
- API routes: `/api/v1/products`, `/api/v1/categories`, etc.
- Admin routes: `/admin/*`

### Security
- Update `.env` with strong passwords
- Enable HTTPS in production
- Configure proper file permissions
- Regular security updates

## 🆘 Troubleshooting

### Common Issues
1. **500 Error**: Check file permissions and `.env` configuration
2. **Database Connection**: Verify database credentials in `.env`
3. **Missing Assets**: Ensure `storage:link` command was run
4. **React Routes 404**: Check `.htaccess` configuration

### Support
For technical support, contact: <EMAIL>
