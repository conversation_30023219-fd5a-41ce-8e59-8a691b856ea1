
// Updated interface to match backend API structure
export interface Product {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description?: string;
  features?: string[];
  specifications?: { [key: string]: string };
  price?: number;
  sku?: string;
  model_number?: string;
  is_active: boolean;
  is_featured: boolean;
  status: string;
  release_date?: string;
  warranty_info?: string;
  compatibility?: string[];
  category: {
    id: number;
    name: string;
    slug: string;
  };
  images: Array<{
    id: number;
    url: string;
    thumb?: string;
    medium?: string;
    large?: string;
    alt?: string;
  }>;
  thumbnails?: Array<{
    id: number;
    url: string;
    thumb?: string;
  }>;
  created_at: string;
  updated_at: string;
}

// Legacy interface for backward compatibility
export interface LegacyProduct {
  id: string;
  title: string;
  description: string;
  price: string;
  category: string;
  image: string;
  images: string[];
  specs: {
    [key: string]: string;
  };
}

export interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image?: string;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  pagination?: {
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

export const products: Product[] = [
  {
    id: '1',
    title: 'Advanced Lower Limb Prosthetic',
    description: 'State-of-the-art lower limb prosthetic with microprocessor knee technology for natural movement and stability.',
    price: 'Contact for Pricing',
    category: 'Lower Limb',
    image: '/src/assets/photo_6048487991124541851_y.jpg',
    images: [
      '/src/assets/photo_6048487991124541851_y.jpg',
      '/src/assets/photo_6048487991124541852_y.jpg',
      '/src/assets/photo_6048487991124541853_y.jpg',
    ],
    specs: {
      'Weight': '2.1 kg',
      'Material': 'Carbon fiber composite',
      'Knee Type': 'Microprocessor controlled',
      'Battery Life': '24-48 hours',
      'Water Resistance': 'IP67 rated',
      'Warranty': '2 years',
    },
  },
  {
    id: '2',
    title: 'Myoelectric Upper Limb Prosthetic',
    description: 'Advanced myoelectric arm prosthetic with multiple grip patterns and intuitive muscle signal control.',
    price: 'Contact for Pricing',
    category: 'Upper Limb',
    image: '/src/assets/photo_6048487991124541852_y.jpg',
    images: [
      '/src/assets/photo_6048487991124541852_y.jpg',
      '/src/assets/photo_6048487991124541853_y.jpg',
      '/src/assets/photo_6048487991124541854_y.jpg',
    ],
    specs: {
      'Weight': '580g',
      'Material': 'Titanium alloy',
      'Grip Patterns': '12 different patterns',
      'Battery Life': '18-24 hours',
      'Control': 'EMG signal based',
      'Warranty': '3 years',
    },
  },
  {
    id: '3',
    title: 'Pediatric Prosthetic Leg',
    description: 'Lightweight, adjustable prosthetic leg designed specifically for growing children with colorful design options.',
    price: 'Contact for Pricing',
    category: 'Pediatric',
    image: '/src/assets/photo_6048487991124541853_y.jpg',
    images: [
      '/src/assets/photo_6048487991124541853_y.jpg',
      '/src/assets/photo_6048487991124541854_y.jpg',
      '/src/assets/photo_6048487991124541851_y.jpg',
    ],
    specs: {
      'Weight': '0.9 kg',
      'Material': 'Lightweight polymer',
      'Adjustability': 'Growth compensating',
      'Age Range': '3-16 years',
      'Color Options': '8 available',
      'Warranty': '18 months',
    },
  },
  {
    id: '4',
    title: 'Sports Performance Prosthetic',
    description: 'High-performance running prosthetic designed for athletes with carbon fiber spring technology.',
    price: 'Contact for Pricing',
    category: 'Sports',
    image: '/src/assets/photo_6048487991124541854_y.jpg',
    images: [
      '/src/assets/photo_6048487991124541854_y.jpg',
      '/src/assets/photo_6048487991124541851_y.jpg',
      '/src/assets/photo_6048487991124541852_y.jpg',
    ],
    specs: {
      'Weight': '1.2 kg',
      'Material': 'Carbon fiber',
      'Spring Rate': 'Customizable',
      'Activity Level': 'High impact sports',
      'Certification': 'Paralympic approved',
      'Warranty': '2 years',
    },
  },
  {
    id: '5',
    title: 'Basic Mechanical Arm',
    description: 'Reliable mechanical arm prosthetic with body-powered operation and durable construction.',
    price: 'Contact for Pricing',
    category: 'Upper Limb',
    image: '/src/assets/photo_6048487991124541851_y.jpg',
    images: [
      '/src/assets/photo_6048487991124541851_y.jpg',
      '/src/assets/photo_6048487991124541853_y.jpg',
    ],
    specs: {
      'Weight': '1.1 kg',
      'Material': 'Aluminum alloy',
      'Operation': 'Body-powered cable',
      'Grip Force': '22 lbs',
      'Maintenance': 'Low maintenance',
      'Warranty': '3 years',
    },
  },
  {
    id: '6',
    title: 'Waterproof Activity Prosthetic',
    description: 'Specialized prosthetic designed for water activities with corrosion-resistant materials.',
    price: 'Contact for Pricing',
    category: 'Specialty',
    image: '/src/assets/photo_6048487991124541852_y.jpg',
    images: [
      '/src/assets/photo_6048487991124541852_y.jpg',
      '/src/assets/photo_6048487991124541854_y.jpg',
    ],
    specs: {
      'Weight': '1.8 kg',
      'Material': 'Marine-grade titanium',
      'Water Rating': 'Fully submersible',
      'Depth Rating': '50 meters',
      'Corrosion': 'Saltwater resistant',
      'Warranty': '2 years',
    },
  },
];

export const categories = ['All', 'Upper Limb', 'Lower Limb', 'Pediatric', 'Sports', 'Specialty'];
