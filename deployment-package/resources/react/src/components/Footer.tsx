import React from "react";
import { <PERSON> } from "react-router-dom";
import {
  Activity,
  MapPin,
  Phone,
  Mail,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  ArrowUp,
  Heart,
} from "lucide-react";
import Logo from "../assets/logoWhite.png";

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  return (
    <footer className="bg-gradient-to-br from-gray-900 via-gray-800 to-blue-900 text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-pulse"></div>
        <div className="absolute top-1/3 right-1/3 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-1000"></div>
        <div className="absolute bottom-1/4 left-1/3 w-1 h-1 bg-white rounded-full animate-pulse delay-2000"></div>
        <div className="absolute bottom-1/3 right-1/4 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-3000"></div>
      </div>

      <div className="container mx-auto px-4 py-20 relative z-10">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-6xl mx-auto">
          {/* Company Info */}
          <div className="space-y-6 md:col-span-1">
            <div className="flex items-center space-x-3">
              <img
                src={Logo}
                alt="RELIFE Logo"
                className="h-14 w-auto object-contain"
              />
            </div>
            <p className="text-gray-300 text-sm leading-relaxed">
              Pioneering the future of prosthetic technology with innovative
              solutions designed for enhanced mobility, comfort, and quality of
              life.
            </p>
            <div className="flex space-x-3">
              <a href="#" className="group">
                <div className="w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110">
                  <Facebook className="w-5 h-5" />
                </div>
              </a>
              <a href="#" className="group">
                <div className="w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110">
                  <Twitter className="w-5 h-5" />
                </div>
              </a>
              <a href="#" className="group">
                <div className="w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110">
                  <Instagram className="w-5 h-5" />
                </div>
              </a>
              <a href="#" className="group">
                <div className="w-12 h-12 bg-gray-700/50 backdrop-blur-sm rounded-xl flex items-center justify-center hover:bg-blue-600 transition-all duration-300 group-hover:scale-110">
                  <Linkedin className="w-5 h-5" />
                </div>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="text-center md:text-left">
            <h3 className="text-lg font-bold mb-6 text-blue-200">Navigation</h3>
            <ul className="space-y-3">
              <li>
                <Link
                  to="/"
                  className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
                >
                  Home
                </Link>
              </li>
              <li>
                <Link
                  to="/about"
                  className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
                >
                  About Us
                </Link>
              </li>
              <li>
                <Link
                  to="/products"
                  className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
                >
                  Products
                </Link>
              </li>
              <li>
                <Link
                  to="/catalogs"
                  className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
                >
                  Catalogs
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div className="text-center md:text-left md:col-span-1 lg:col-span-1">
            <h3 className="text-lg font-bold mb-6 text-blue-200">
              Contact Information
            </h3>
            <div className="space-y-4 text-gray-300 text-sm max-w-xs mx-auto md:max-w-none md:mx-0">
              <div className="flex items-start space-x-3 justify-center md:justify-start">
                <MapPin className="w-5 h-5 text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <p className="font-medium">RELIFE INC</p>
                  <p>EDGEROOK DR</p>
                  <p>TORONTO ON M9V 5E8 CANADA</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 justify-center md:justify-start">
                <Phone className="w-5 h-5 text-blue-400 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <a href="tel:+16476466640" className="font-medium hover:text-blue-300 transition-colors">
                    +****************
                  </a>
                  <p className="text-xs text-blue-300">24/7 Technical Support</p>
                </div>
              </div>
              <div className="flex items-center space-x-3 justify-center md:justify-start">
                <Mail className="w-5 h-5 text-blue-400 flex-shrink-0" />
                <div className="min-w-0 flex-1">
                  <a href="mailto:<EMAIL>" className="font-medium hover:text-blue-300 transition-colors">
                    <EMAIL>
                  </a>
                  <p className="text-xs text-blue-300">Business Inquiries</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-700/50 mt-16 pt-8">
          <div className="flex flex-col items-center justify-center space-y-6">
            <div className="text-center text-gray-400 text-sm">
              <p className="flex flex-col md:flex-row items-center justify-center space-y-2 md:space-y-0 md:space-x-2">
                <span>&copy; {new Date().getFullYear()} RELIFE Inc. All rights reserved.</span>
                <span className="hidden md:inline">•</span>
                <span className="flex items-center space-x-1">
                  <span>Made with</span>
                  <Heart className="w-4 h-4 text-red-400 fill-current" />
                  <span>for better lives</span>
                </span>
              </p>
            </div>

            {/* Developer Attribution */}
            <div className="text-center text-gray-500 text-xs">
              <p className="flex items-center justify-center space-x-1">
                <span>Developed by</span>
                <a
                  href="https://synapta.art/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-400 hover:text-blue-300 transition-colors duration-300 font-medium underline decoration-blue-400/30 hover:decoration-blue-300/50 underline-offset-2"
                >
                  Synapta.art
                </a>
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll to Top Button */}
      <button
        onClick={scrollToTop}
        className="fixed bottom-8 right-8 w-14 h-14 bg-gradient-to-br from-blue-600 to-blue-700 text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 z-50 group"
        aria-label="Scroll to top"
      >
        <ArrowUp className="w-6 h-6 mx-auto group-hover:-translate-y-1 transition-transform duration-300" />
      </button>
    </footer>
  );
};

export default Footer;
