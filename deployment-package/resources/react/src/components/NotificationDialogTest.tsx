import React, { useState } from 'react';
import NotificationDialog from './NotificationDialog';

const NotificationDialogTest: React.FC = () => {
  const [notificationDialog, setNotificationDialog] = useState({
    isOpen: false,
    type: 'success' as 'success' | 'error' | 'warning',
    title: '',
    message: '',
    autoClose: false,
  });

  const showNotification = (
    type: 'success' | 'error' | 'warning',
    title: string,
    message: string,
    autoClose: boolean = false
  ) => {
    setNotificationDialog({
      isOpen: true,
      type,
      title,
      message,
      autoClose,
    });
  };

  const closeNotification = () => {
    setNotificationDialog(prev => ({ ...prev, isOpen: false }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50/30 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-center mb-8 text-gray-800">
          Notification Dialog Test
        </h1>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Success Notification Test */}
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-green-700">Success Notification</h2>
            <p className="text-gray-600 mb-4">
              Test the success notification with auto-close functionality.
            </p>
            <button
              onClick={() => showNotification(
                'success',
                'Message Sent Successfully',
                'Thank you for contacting us. We\'ll get back to you within 24 hours.',
                true
              )}
              className="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              Show Success
            </button>
          </div>

          {/* Error Notification Test */}
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-red-700">Error Notification</h2>
            <p className="text-gray-600 mb-4">
              Test the error notification that requires manual dismissal.
            </p>
            <button
              onClick={() => showNotification(
                'error',
                'Error Sending Message',
                'There was a problem sending your message. Please try again or contact us directly.',
                false
              )}
              className="w-full bg-red-600 hover:bg-red-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              Show Error
            </button>
          </div>

          {/* Warning Notification Test */}
          <div className="bg-white p-6 rounded-xl shadow-lg">
            <h2 className="text-xl font-semibold mb-4 text-orange-700">Warning Notification</h2>
            <p className="text-gray-600 mb-4">
              Test the warning notification for validation errors.
            </p>
            <button
              onClick={() => showNotification(
                'warning',
                'CAPTCHA Required',
                'Please complete the CAPTCHA verification to send your message.',
                false
              )}
              className="w-full bg-orange-600 hover:bg-orange-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              Show Warning
            </button>
          </div>
        </div>

        {/* Additional Tests */}
        <div className="mt-8 bg-white p-6 rounded-xl shadow-lg">
          <h2 className="text-xl font-semibold mb-4 text-blue-700">Additional Tests</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button
              onClick={() => showNotification(
                'success',
                'Form Submitted',
                'Your contact form has been submitted successfully. Our team will review your message and respond within 1-2 business days.',
                true
              )}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              Long Success Message
            </button>
            
            <button
              onClick={() => showNotification(
                'error',
                'Validation Failed',
                'Please check your form fields and CAPTCHA, then try again.',
                false
              )}
              className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              Validation Error
            </button>
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-blue-50 p-6 rounded-xl border border-blue-200">
          <h3 className="text-lg font-semibold mb-2 text-blue-800">Test Instructions</h3>
          <ul className="text-blue-700 space-y-1">
            <li>• Success notifications auto-close after 5 seconds</li>
            <li>• Error and warning notifications require manual dismissal</li>
            <li>• Click outside the dialog or press ESC to close</li>
            <li>• Test on different screen sizes for responsiveness</li>
          </ul>
        </div>
      </div>

      {/* Notification Dialog */}
      <NotificationDialog
        isOpen={notificationDialog.isOpen}
        onClose={closeNotification}
        type={notificationDialog.type}
        title={notificationDialog.title}
        message={notificationDialog.message}
        autoClose={notificationDialog.autoClose}
        autoCloseDelay={5000}
      />
    </div>
  );
};

export default NotificationDialogTest;
