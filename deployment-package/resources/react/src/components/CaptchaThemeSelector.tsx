import React from 'react';
import { Palette, <PERSON>, Moon, Shield } from 'lucide-react';

interface CaptchaThemeSelectorProps {
  currentTheme: 'light' | 'dark';
  onThemeChange: (theme: 'light' | 'dark') => void;
  className?: string;
}

const CaptchaThemeSelector: React.FC<CaptchaThemeSelectorProps> = ({
  currentTheme,
  onThemeChange,
  className = ''
}) => {
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <Palette className="w-4 h-4 text-gray-500" />
      <span className="text-xs text-gray-600">Theme:</span>
      <div className="flex bg-gray-100 rounded-lg p-1">
        <button
          type="button"
          onClick={() => onThemeChange('light')}
          className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-all ${
            currentTheme === 'light'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-blue-600'
          }`}
        >
          <Sun className="w-3 h-3" />
          <span>Light</span>
        </button>
        <button
          type="button"
          onClick={() => onThemeChange('dark')}
          className={`flex items-center space-x-1 px-2 py-1 rounded text-xs transition-all ${
            currentTheme === 'dark'
              ? 'bg-white text-blue-600 shadow-sm'
              : 'text-gray-600 hover:text-blue-600'
          }`}
        >
          <Moon className="w-3 h-3" />
          <span>Dark</span>
        </button>
      </div>
    </div>
  );
};

export default CaptchaThemeSelector;
