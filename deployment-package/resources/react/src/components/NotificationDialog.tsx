import React, { useEffect } from 'react';
import { CheckCircle, AlertCircle, XCircle, Shield } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';

interface NotificationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  type: 'success' | 'error' | 'warning';
  title: string;
  message: string;
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const NotificationDialog: React.FC<NotificationDialogProps> = ({
  isOpen,
  onClose,
  type,
  title,
  message,
  autoClose = false,
  autoCloseDelay = 4000,
}) => {
  // Auto-close functionality
  useEffect(() => {
    if (isOpen && autoClose) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);

      return () => clearTimeout(timer);
    }
  }, [isOpen, autoClose, autoCloseDelay, onClose]);

  // Keyboard event handling
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen, onClose]);

  // Get icon and colors based on type
  const getTypeConfig = () => {
    switch (type) {
      case 'success':
        return {
          icon: CheckCircle,
          iconColor: 'text-green-600',
          bgGradient: 'from-green-50 to-blue-50',
          borderColor: 'border-green-300',
          titleColor: 'text-green-800',
          messageColor: 'text-green-700',
        };
      case 'error':
        return {
          icon: XCircle,
          iconColor: 'text-red-600',
          bgGradient: 'from-red-50 to-blue-50',
          borderColor: 'border-red-300',
          titleColor: 'text-red-800',
          messageColor: 'text-red-700',
        };
      case 'warning':
        return {
          icon: AlertCircle,
          iconColor: 'text-orange-600',
          bgGradient: 'from-orange-50 to-blue-50',
          borderColor: 'border-orange-300',
          titleColor: 'text-orange-800',
          messageColor: 'text-orange-700',
        };
      default:
        return {
          icon: Shield,
          iconColor: 'text-blue-600',
          bgGradient: 'from-blue-50 to-white',
          borderColor: 'border-blue-300',
          titleColor: 'text-blue-800',
          messageColor: 'text-blue-700',
        };
    }
  };

  const config = getTypeConfig();
  const IconComponent = config.icon;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className={`
          max-w-md mx-auto
          bg-gradient-to-br ${config.bgGradient}
          backdrop-blur-sm
          border-2 ${config.borderColor}
          rounded-2xl
          shadow-2xl
          p-0
          overflow-hidden
          transform transition-all duration-300
          focus:outline-none
          focus:ring-4 focus:ring-blue-100
        `}
        aria-describedby="notification-description"
        role="alertdialog"
        aria-modal="true"
      >
        {/* Header with Icon */}
        <DialogHeader className="relative p-8 pb-4">
          <div className="flex items-center justify-center mb-4">
            <div className={`
              w-16 h-16 
              rounded-full 
              bg-white/80 
              backdrop-blur-sm 
              border-2 ${config.borderColor}
              flex items-center justify-center
              shadow-lg
              transform transition-all duration-300 hover:scale-105
            `}>
              <IconComponent className={`w-8 h-8 ${config.iconColor}`} />
            </div>
          </div>
          
          <DialogTitle className={`
            text-2xl font-bold text-center mb-2
            ${config.titleColor}
            leading-tight
          `}>
            {title}
          </DialogTitle>
        </DialogHeader>

        {/* Message Content */}
        <div className="px-8 pb-8">
          <DialogDescription 
            id="notification-description"
            className={`
              text-center text-base leading-relaxed
              ${config.messageColor}
              font-medium
            `}
          >
            {message}
          </DialogDescription>

          {/* Auto-close indicator for success messages */}
          {autoClose && type === 'success' && (
            <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-green-600">
              <div className="w-2 h-2 bg-green-600 rounded-full animate-pulse"></div>
              <span>This message will close automatically</span>
            </div>
          )}

          {/* Manual close hint for error messages */}
          {!autoClose && type === 'error' && (
            <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-red-600">
              <span>Click outside or press ESC to close</span>
            </div>
          )}
        </div>

        {/* Decorative bottom border */}
        <div className={`
          h-1 w-full 
          bg-gradient-to-r 
          ${type === 'success' ? 'from-green-400 to-blue-500' : 
            type === 'error' ? 'from-red-400 to-blue-500' : 
            'from-orange-400 to-blue-500'}
        `} />
      </DialogContent>
    </Dialog>
  );
};

export default NotificationDialog;
