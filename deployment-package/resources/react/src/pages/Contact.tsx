
import React, { useState, useEffect, useRef } from 'react';
import { MapPin, Phone, Mail, Clock, Send, User, MessageSquare, Shield } from 'lucide-react';
import { contactApi, ContactFormData, configApi } from '../services/api';
import ReCaptcha, { ReCaptchaRef } from '../components/ReCaptcha';
import NotificationDialog from '../components/NotificationDialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

const Contact = () => {
  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    subject: '',
    message: '',
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captchaToken, setCaptchaToken] = useState<string>('');
  const [captchaError, setCaptchaError] = useState<boolean>(false);
  const [captchaLoading, setCaptchaLoading] = useState<boolean>(false);
  const [recaptchaSiteKey, setRecaptchaSiteKey] = useState<string>('');
  const [recaptchaEnabled, setRecaptchaEnabled] = useState<boolean>(true);
  const [recaptchaKey, setRecaptchaKey] = useState<number>(0); // Key to force re-render
  const captchaRef = useRef<ReCaptchaRef>(null);

  // Notification dialog state
  const [notificationDialog, setNotificationDialog] = useState({
    isOpen: false,
    type: 'success' as 'success' | 'error' | 'warning',
    title: '',
    message: '',
    autoClose: false,
  });



  // Fetch reCAPTCHA configuration from Laravel backend
  useEffect(() => {
    const fetchRecaptchaConfig = async () => {
      try {
        const response = await configApi.getRecaptchaConfig();

        if (response.success && response.data) {
          setRecaptchaSiteKey(response.data.site_key);
          setRecaptchaEnabled(response.data.enabled);
        } else {
          // Fallback to environment variable
          const fallbackKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI';
          setRecaptchaSiteKey(fallbackKey);
        }
      } catch (error) {
        // Fallback to environment variable
        const fallbackKey = import.meta.env.VITE_RECAPTCHA_SITE_KEY || '6LeIxAcTAAAAAJcZVRqyHh71UMIEGNQ_MXjiZKhI';
        setRecaptchaSiteKey(fallbackKey);
      }
    };

    fetchRecaptchaConfig();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubjectChange = (value: string) => {
    setFormData({
      ...formData,
      subject: value,
    });
  };

  // Helper functions for showing notifications
  const showNotification = (
    type: 'success' | 'error' | 'warning',
    title: string,
    message: string,
    autoClose: boolean = false
  ) => {
    setNotificationDialog({
      isOpen: true,
      type,
      title,
      message,
      autoClose,
    });
  };

  const closeNotification = () => {
    setNotificationDialog(prev => ({ ...prev, isOpen: false }));
  };

  const handleCaptchaVerify = (token: string) => {
    setCaptchaToken(token);
    setCaptchaError(false);
    setCaptchaLoading(false);
  };

  const handleCaptchaExpired = () => {
    setCaptchaToken('');
    setCaptchaError(false);
  };

  const handleCaptchaError = () => {
    setCaptchaToken('');
    setCaptchaError(true);
    setCaptchaLoading(false);
    showNotification(
      'error',
      'CAPTCHA Error',
      'There was an error with the CAPTCHA. Please try again.',
      false
    );
  };

  const handleCaptchaLoad = () => {
    setCaptchaLoading(true);
    setCaptchaError(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (isSubmitting) return;

    // Validate CAPTCHA (only if enabled)
    if (recaptchaEnabled && !captchaToken) {
      showNotification(
        'warning',
        'CAPTCHA Required',
        'Please complete the CAPTCHA verification to send your message.',
        false
      );
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await contactApi.submitContactForm({
        ...formData,
        captcha_token: captchaToken,
      } as ContactFormData & { captcha_token: string });

      if (response.success) {
        showNotification(
          'success',
          'Message Sent Successfully',
          response.message || "Thank you for contacting us. We'll get back to you within 24 hours.",
          true // Auto-close success messages
        );

        // Reset form
        setFormData({
          name: '',
          email: '',
          phone: '',
          subject: '',
          message: '',
        });

        // Reset CAPTCHA
        setCaptchaToken('');
        setCaptchaError(false);
        setCaptchaLoading(false);
        setRecaptchaKey(prev => prev + 1); // Force re-render
        captchaRef.current?.reset();
      } else {
        throw new Error(response.message || 'Failed to send message');
      }
    } catch (error: unknown) {
      console.error('Contact form submission error:', error);

      // Reset CAPTCHA on error
      setCaptchaToken('');
      setCaptchaError(true);
      setCaptchaLoading(false);
      setRecaptchaKey(prev => prev + 1); // Force re-render
      captchaRef.current?.reset();

      // Handle validation errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      if (errorMessage.includes('422') || errorMessage.includes('Validation')) {
        showNotification(
          'error',
          'Validation Error',
          'Please check your form fields and CAPTCHA, then try again.',
          false
        );
      } else {
        showNotification(
          'error',
          'Error Sending Message',
          'There was a problem sending your message. Please try again or contact us directly.',
          false
        );
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50/30">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 via-white to-gray-50 py-24 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-2 h-2 bg-blue-600 rounded-full animate-pulse delay-2000"></div>
          <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-blue-500 rounded-full animate-pulse delay-3000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <div className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6 animate-fade-in">
              <MessageSquare className="w-4 h-4 inline mr-2" />
              Get In Touch
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6 animate-slide-up">
              Contact{' '}
              <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                Our Team
              </span>
            </h1>
            <p className="text-xl text-gray-700 leading-relaxed max-w-3xl mx-auto animate-slide-up delay-200">
              Ready to transform your life with advanced prosthetic solutions? Our expert team
              is here to guide you every step of the way. Let's start your journey together.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Form and Info */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div className="medical-card bg-white/90 backdrop-blur-sm p-8 lg:p-10 animate-fade-in">

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="group">
                    <label htmlFor="name" className="block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors">
                      <User className="w-4 h-4 inline mr-2" />
                      Full Name *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleChange}
                      className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300"
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div className="group">
                    <label htmlFor="email" className="block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors">
                      <Mail className="w-4 h-4 inline mr-2" />
                      Email Address *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleChange}
                      className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="group">
                    <label htmlFor="phone" className="block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors">
                      <Phone className="w-4 h-4 inline mr-2" />
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300"
                      placeholder="+****************"
                    />
                  </div>
                  <div className="group">
                    <label htmlFor="subject" className="block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors">
                      <MessageSquare className="w-4 h-4 inline mr-2" />
                      Subject *
                    </label>
                    <Select
                      value={formData.subject}
                      onValueChange={handleSubjectChange}
                      required
                    >
                      <SelectTrigger className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300 h-auto">
                        <SelectValue placeholder="Select a subject" />
                      </SelectTrigger>
                      <SelectContent className="bg-white/95 backdrop-blur-sm border-2 border-gray-200 rounded-xl shadow-xl">
                        <SelectItem value="product-inquiry" className="px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <MessageSquare className="w-4 h-4 text-blue-600" />
                            <span>Product Inquiry</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="consultation" className="px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <User className="w-4 h-4 text-green-600" />
                            <span>Request Consultation</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="support" className="px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <Shield className="w-4 h-4 text-orange-600" />
                            <span>Technical Support</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="warranty" className="px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <Clock className="w-4 h-4 text-purple-600" />
                            <span>Warranty Claim</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="other" className="px-4 py-3 hover:bg-blue-50 focus:bg-blue-50 cursor-pointer">
                          <div className="flex items-center space-x-2">
                            <Mail className="w-4 h-4 text-gray-600" />
                            <span>Other</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="group">
                  <label htmlFor="message" className="block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors">
                    <MessageSquare className="w-4 h-4 inline mr-2" />
                    Message *
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    required
                    rows={6}
                    value={formData.message}
                    onChange={handleChange}
                    className="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:ring-4 focus:ring-blue-100 focus:border-blue-500 transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300 resize-none"
                    placeholder="Tell us about your needs, questions, or how we can help you..."
                  />
                </div>

                {/* CAPTCHA - Only show if enabled and site key is available */}
                {recaptchaEnabled && recaptchaSiteKey && (
                  <div className="group">
                    <label className="block text-sm font-semibold text-gray-700 mb-3 group-focus-within:text-blue-600 transition-colors">
                      <Shield className={`w-4 h-4 inline mr-2 ${captchaToken ? 'text-green-600' : captchaError ? 'text-red-600' : 'text-blue-600'}`} />
                      Security Verification *
                      {captchaToken && (
                        <span className="ml-2 text-xs text-green-600 font-medium">✓ Verified</span>
                      )}
                      {captchaError && (
                        <span className="ml-2 text-xs text-red-600 font-medium">⚠ Error</span>
                      )}
                    </label>
                    <div className={`bg-gradient-to-br from-white/90 to-blue-50/50 backdrop-blur-sm border-2 rounded-xl p-6 transition-all duration-300 ${
                      captchaToken
                        ? 'border-green-300 bg-green-50/30'
                        : captchaError
                          ? 'border-red-300 bg-red-50/30'
                          : 'border-gray-200 hover:border-blue-300'
                    }`}>
                      <div className="text-center mb-4">
                        <div className="inline-flex items-center space-x-2 text-sm text-gray-600">
                          <Shield className="w-4 h-4" />
                          <span>Please verify you're human to send your message</span>
                        </div>

                      </div>
                      <ReCaptcha
                        key={recaptchaKey}
                        ref={captchaRef}
                        siteKey={recaptchaSiteKey}
                        onVerify={handleCaptchaVerify}
                        onExpired={handleCaptchaExpired}
                        onError={handleCaptchaError}
                        onLoad={handleCaptchaLoad}
                        theme="light"
                        size="normal"
                        className={`recaptcha-medical ${captchaError ? 'recaptcha-error' : captchaToken ? 'recaptcha-success' : ''}`}
                        containerClassName="transform transition-all duration-300"
                      />
                      {captchaLoading && (
                        <div className="text-center mt-3">
                          <div className="inline-flex items-center space-x-2 text-sm text-blue-600">
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                            <span>Loading verification...</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Show message if CAPTCHA is disabled */}
                {!recaptchaEnabled && (
                  <div className="text-center p-4 bg-yellow-50 border border-yellow-200 rounded-xl">
                    <div className="inline-flex items-center space-x-2 text-sm text-yellow-700">
                      <Shield className="w-4 h-4" />
                      <span>CAPTCHA verification is currently disabled</span>
                    </div>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={isSubmitting || (recaptchaEnabled && !captchaToken)}
                  className={`medical-button w-full group flex items-center justify-center space-x-3 py-4 px-8 text-lg font-semibold ${
                    isSubmitting || (recaptchaEnabled && !captchaToken) ? 'opacity-75 cursor-not-allowed' : ''
                  }`}
                >
                  <Send className={`w-5 h-5 transition-transform duration-300 ${
                    isSubmitting ? 'animate-pulse' : 'group-hover:translate-x-1'
                  }`} />
                  <span>{isSubmitting ? 'Sending...' : 'Send Message'}</span>
                </button>
              </form>
            </div>

            {/* Contact Information */}
            <div className="space-y-8 animate-fade-in delay-300">
              {/* Main Contact Info */}
              <div className="medical-card bg-gradient-to-br from-blue-50 to-white p-8 lg:p-10">


                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div className="rounded-xl">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                        <MapPin className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg mb-2">Visit Our Office</h3>
                        <p className="text-gray-600 leading-relaxed">
                          EDGEROOK DR<br />
                          TORONTO ON M9V 5E8<br />
                          CANADA
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-xl">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Phone className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg mb-2">Call Us</h3>
                        <p className="text-gray-600 leading-relaxed">
                          <a href="tel:+16476466640" className="hover:text-blue-600 transition-colors">+****************</a><br />
                          <span className="text-sm text-gray-500">24/7 Technical Support</span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-xl">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Mail className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg mb-2">Email Us</h3>
                        <p className="text-gray-600 leading-relaxed">
                          <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors"><EMAIL></a><br />
                          <span className="text-sm text-gray-500">Business Inquiries</span>
                        </p>
                      </div>
                    </div>
                  </div>

                  <div className="rounded-xl">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-blue-700 rounded-xl flex items-center justify-center flex-shrink-0">
                        <Clock className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-bold text-gray-900 text-lg mb-2">Business Hours</h3>
                        <p className="text-gray-600 leading-relaxed">
                          Monday - Friday: 8:00 AM - 6:00 PM<br />
                          Saturday: 9:00 AM - 3:00 PM<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>
        </div>
      </section>

      {/* Notification Dialog */}
      <NotificationDialog
        isOpen={notificationDialog.isOpen}
        onClose={closeNotification}
        type={notificationDialog.type}
        title={notificationDialog.title}
        message={notificationDialog.message}
        autoClose={notificationDialog.autoClose}
        autoCloseDelay={5000}
      />
    </div>
  );
};

export default Contact;
