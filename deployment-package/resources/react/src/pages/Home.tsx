
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { useFeaturedProductsLegacy } from '../hooks/useProducts';
import { ProductCardSkeleton } from '../components/ui/loading';
import {
  ArrowRight,
  Shield,
  Heart,
  CheckCircle,
  TrendingUp,
  Clock
} from 'lucide-react';

const Home = () => {
  const [isVisible, setIsVisible] = useState(false);

  // Fetch featured products from API
  const { data: featuredProducts, isLoading: featuredLoading, error: featuredError } = useFeaturedProductsLegacy();

  useEffect(() => {
    setIsVisible(true);
    // Scroll to top when component mounts
    window.scrollTo(0, 0);
  }, []);



  const features = [
    {
      icon: Shield,
      title: 'Approved',
      description: 'All our products meet the highest safety and quality standards'
    },
    {
      icon: Heart,
      title: 'Patient-Centered',
      description: 'Designed with comfort and user experience as our top priority'
    },
    {
      icon: TrendingUp,
      title: 'Advanced Technology',
      description: 'Cutting-edge materials and engineering for superior performance'
    },
    {
      icon: Clock,
      title: '24/7 Support',
      description: 'Round-the-clock technical support and maintenance services'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background with gradient and pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
        <div className="absolute inset-0 opacity-30">
          <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-blue-300 rounded-full animate-pulse"></div>
          <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-blue-500 rounded-full animate-pulse delay-2000"></div>
          <div className="absolute bottom-1/3 right-1/4 w-1 h-1 bg-blue-600 rounded-full animate-pulse delay-3000"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className={`max-w-6xl mx-auto text-center transition-all duration-1000 ${
            isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
          }`}>

            <h1 className="text-5xl md:text-7xl font-bold text-gray-900 mb-8 leading-tight">
              Advanced Prosthetic Solutions for{' '}
              <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                Better Living
              </span>
            </h1>

            <p className="text-xl md:text-2xl text-gray-600 mb-12 leading-relaxed max-w-4xl mx-auto">
              Discover cutting-edge prosthetic technology designed to restore mobility,
              independence, and confidence. Our premium solutions combine innovation
              with compassionate care.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <Link
                to="/products"
                className="medical-button group flex items-center justify-center space-x-2"
              >
                <span>Explore Products</span>
                <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
              </Link>

              <Link
                to="/contact"
                className="group flex items-center justify-center space-x-3 border-2 border-blue-600 text-blue-600 px-8 py-4 rounded-xl font-semibold hover:bg-blue-600 hover:text-white transition-all duration-300"
              >
                <span>Contact Us</span>
              </Link>
            </div>


          </div>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-32 h-32 bg-blue-300 rounded-full opacity-20 animate-pulse delay-1000"></div>
      </section>

      {/* Features Section */}
      <section className="medical-section bg-white relative overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{ color: '#27ae60' }}>
              Why Choose{' '}
              <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                RELIFE?
              </span>
            </h2>
            <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: '#6b7280' }}>
              We're committed to providing the highest quality prosthetic solutions
              with advanced technology and personalized care.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="group">
                <div className="modern-card p-8 text-center h-full">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="w-8 h-8" style={{ color: '#3b4d66' }} />
                  </div>
                  <h3 className="text-xl font-bold mb-4" style={{ color: '#27ae60' }}>{feature.title}</h3>
                  <p className="leading-relaxed" style={{ color: '#6b7280' }}>{feature.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full opacity-20 transform translate-x-32 -translate-y-32"></div>
      </section>

      {/* Featured Products Section */}
      <section className="medical-section bg-gradient-to-br from-gray-50 to-blue-50/30">
        <div className="container mx-auto px-4">
          <div className="text-center mb-20">
            <span className="inline-block px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-semibold mb-6">
              Our Products
            </span>
            <h2 className="text-4xl md:text-5xl font-bold mb-6" style={{ color: '#27ae60' }}>
              Featured{' '}
              <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
                Products
              </span>
            </h2>
            <p className="text-xl max-w-3xl mx-auto leading-relaxed" style={{ color: '#6b7280' }}>
              Explore our most popular prosthetic solutions designed for comfort,
              durability, and natural movement.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {featuredLoading ? (
              <ProductCardSkeleton count={3} />
            ) : featuredError ? (
              <div className="col-span-3 text-center py-8">
                <p className="text-red-600 mb-4">Failed to load featured products</p>
                <button
                  onClick={() => window.location.reload()}
                  className="text-blue-600 hover:underline"
                >
                  Try again
                </button>
              </div>
            ) : featuredProducts && featuredProducts.length > 0 ? (
              featuredProducts.slice(0, 3).map((product, index) => (
                <div key={product.id} className={`fade-in`} style={{ animationDelay: `${index * 0.2}s` }}>
                  <ProductCard product={product} />
                </div>
              ))
            ) : (
              <div className="col-span-3 text-center py-8">
                <p className="text-gray-600">No featured products available</p>
              </div>
            )}
          </div>

          <div className="text-center">
            <Link
              to="/products"
              className="medical-button group inline-flex items-center space-x-2"
            >
              <span>View All Products</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="medical-section bg-gradient-to-br from-gray-800 via-gray-900 to-blue-800 text-white relative overflow-hidden">
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-1 h-1 bg-white rounded-full animate-pulse"></div>
          <div className="absolute top-1/3 right-1/3 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-1000"></div>
          <div className="absolute bottom-1/4 left-1/3 w-1 h-1 bg-white rounded-full animate-pulse delay-2000"></div>
          <div className="absolute bottom-1/3 right-1/4 w-0.5 h-0.5 bg-white rounded-full animate-pulse delay-3000"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to{' '}
            <span className="text-blue-300">Transform Your Life?</span>
          </h2>
          <p className="text-xl mb-12 max-w-3xl mx-auto leading-relaxed opacity-90">
            Contact our team today to learn more about our prosthetic solutions
            and find the perfect fit for your needs. Your journey to independence starts here.
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link
              to="/contact"
              className="bg-white text-gray-800 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
            >
              Contact Us Today
            </Link>
            <Link
              to="/products"
              className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-gray-800 transition-all duration-300"
            >
              Browse Products
            </Link>
          </div>
        </div>

        {/* Background decorations */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-white opacity-5 rounded-full transform -translate-x-48 -translate-y-48"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-white opacity-5 rounded-full transform translate-x-48 translate-y-48"></div>
      </section>
    </div>
  );
};

export default Home;
