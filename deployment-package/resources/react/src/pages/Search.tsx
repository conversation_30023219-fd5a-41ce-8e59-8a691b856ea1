
import React, { useState, useEffect } from 'react';
import { useSearchParams, Link } from 'react-router-dom';
import ProductCard from '../components/ProductCard';
import { products } from '../data/products';

const Search = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';
  const [searchResults, setSearchResults] = useState(products);

  useEffect(() => {
    if (query.trim()) {
      const filtered = products.filter(product =>
        product.title.toLowerCase().includes(query.toLowerCase()) ||
        product.description.toLowerCase().includes(query.toLowerCase()) ||
        product.category.toLowerCase().includes(query.toLowerCase()) ||
        Object.values(product.specs).some(spec => 
          spec.toLowerCase().includes(query.toLowerCase())
        )
      );
      setSearchResults(filtered);
    } else {
      setSearchResults(products);
    }
  }, [query]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-blue-50 to-blue-100 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Search Results
            </h1>
            {query && (
              <p className="text-xl text-gray-700 leading-relaxed">
                Showing results for "<span className="font-semibold">{query}</span>"
              </p>
            )}
          </div>
        </div>
      </section>

      {/* Search Results */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <div className="mb-6 flex items-center justify-between">
            <p className="text-gray-600">
              Found {searchResults.length} product{searchResults.length !== 1 ? 's' : ''}
              {query && ` matching "${query}"`}
            </p>
            
            {query && (
              <Link
                to="/products"
                className="text-blue-600 hover:text-blue-800 font-medium"
              >
                View All Products
              </Link>
            )}
          </div>
          
          {searchResults.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {searchResults.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 text-6xl mb-4">🔍</div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-2">No results found</h3>
              <p className="text-gray-600 mb-6">
                We couldn't find any products matching "{query}". Try:
              </p>
              <ul className="text-gray-600 space-y-2 mb-8">
                <li>• Checking your spelling</li>
                <li>• Using different keywords</li>
                <li>• Searching for more general terms</li>
              </ul>
              <Link
                to="/products"
                className="bg-blue-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 inline-block"
              >
                Browse All Products
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* Popular Searches */}
      {searchResults.length === 0 && (
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 text-center">Popular Searches</h2>
            <div className="flex flex-wrap justify-center gap-4">
              {['lower limb', 'upper limb', 'pediatric', 'sports', 'myoelectric', 'microprocessor'].map((term) => (
                <Link
                  key={term}
                  to={`/search?q=${encodeURIComponent(term)}`}
                  className="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-full text-gray-700 transition-colors"
                >
                  {term}
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}
    </div>
  );
};

export default Search;
