
import React, { useEffect, useState } from 'react';
import { useCatalogs, useCatalogCategories } from '../hooks/useProducts';
import { catalogApi, Catalog } from '../services/api';

const Catalogs = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  // Fetch catalogs and categories from API
  const { data: catalogsResponse, isLoading: catalogsLoading, error: catalogsError } = useCatalogs({
    category: selectedCategory === 'all' ? undefined : selectedCategory,
    search: searchQuery || undefined,
  });

  const { data: categoriesResponse, isLoading: categoriesLoading } = useCatalogCategories();

  const catalogs = catalogsResponse?.data || [];
  const categories = categoriesResponse?.data || [];

  // Scroll to top when component mounts
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleDownload = (catalog: Catalog) => {
    if (catalog.download_url) {
      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = catalog.download_url;
      link.download = `${catalog.name}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      alert('Download URL not available for this catalog.');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-gray-50 via-white to-green-50 py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Product <span className="bg-gradient-to-r from-green-600 to-green-700 bg-clip-text text-transparent">Catalogs</span>
            </h1>
            <p className="text-xl text-gray-700 leading-relaxed">
              Download comprehensive product catalogs with detailed specifications,
              compatibility guides, and technical documentation.
            </p>
          </div>
        </div>
      </section>

      {/* Filters Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="flex-1 max-w-md">
              <input
                type="text"
                placeholder="Search catalogs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>

            {/* Category Filter */}
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={() => setSelectedCategory('all')}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  selectedCategory === 'all'
                    ? 'bg-green-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                All Categories
              </button>
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    selectedCategory === category
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Catalogs Grid */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {catalogsLoading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600"></div>
            </div>
          ) : catalogsError ? (
            <div className="text-center py-20">
              <h3 className="text-xl font-semibold text-red-600 mb-4">Error Loading Catalogs</h3>
              <p className="text-gray-600 mb-8">
                {catalogsError instanceof Error ? catalogsError.message : 'Failed to load catalogs'}
              </p>
              <button
                onClick={() => window.location.reload()}
                className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700"
              >
                Try Again
              </button>
            </div>
          ) : catalogs.length === 0 ? (
            <div className="text-center py-20">
              <h3 className="text-xl font-semibold text-gray-600 mb-4">No Catalogs Found</h3>
              <p className="text-gray-500">
                {searchQuery || selectedCategory !== 'all'
                  ? 'Try adjusting your search or filter criteria.'
                  : 'No catalogs are currently available.'}
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {catalogs.map((catalog) => (
                <div key={catalog.id} className="bg-white rounded-lg shadow-md hover:shadow-xl transition-shadow duration-300 overflow-hidden">
                  <div className="aspect-[16/10] bg-gradient-to-br from-green-100 to-green-200 overflow-hidden relative">
                    {catalog.image_url ? (
                      <img
                        src={catalog.image_url}
                        alt={catalog.name}
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                        onError={(e) => {
                          // Fallback to default icon if image fails to load
                          const target = e.target as HTMLImageElement;
                          const parent = target.parentElement;
                          if (parent) {
                            parent.innerHTML = `
                              <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-100 to-green-200">
                                <div class="text-center">
                                  <div class="text-4xl mb-2">📄</div>
                                  <div class="text-sm text-green-700 font-medium">PDF Catalog</div>
                                </div>
                              </div>
                            `;
                          }
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="text-center">
                          <div className="text-4xl mb-2">📄</div>
                          <div className="text-sm text-green-700 font-medium">PDF Catalog</div>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="p-6">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-xs font-medium text-green-700 bg-green-100 px-2 py-1 rounded-full">
                        {catalog.category}
                      </span>
                      <div className="flex flex-col items-end">
                        <span className="text-sm text-gray-500">{catalog.file_size_formatted || 'N/A'}</span>
                        {catalog.version && (
                          <span className="text-xs text-gray-400">v{catalog.version}</span>
                        )}
                      </div>
                    </div>

                    <h3 className="text-lg font-semibold text-gray-900 mb-3">
                      {catalog.name}
                    </h3>

                    <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                      {catalog.description}
                    </p>

                    <div className="flex items-center justify-between mb-4 text-xs text-gray-500">
                      <span>Downloads: {catalog.download_count}</span>
                      {catalog.published_date && (
                        <span>Published: {new Date(catalog.published_date).toLocaleDateString()}</span>
                      )}
                    </div>

                    <button
                      onClick={() => handleDownload(catalog)}
                      className="w-full bg-gradient-to-r from-green-600 to-green-700 text-white py-3 px-4 rounded-lg font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 flex items-center justify-center space-x-2"
                    >
                      <span>📥</span>
                      <span>Download PDF</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </section>



      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              What You'll Find in Our Catalogs
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">📊</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Detailed Specifications</h3>
              <p className="text-gray-600">
                Complete technical specifications, measurements, and performance data.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🔧</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Installation Guides</h3>
              <p className="text-gray-600">
                Step-by-step installation and setup instructions for professionals.
              </p>
            </div>

            <div className="text-center p-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-semibold mb-3">Compatibility Information</h3>
              <p className="text-gray-600">
                Comprehensive compatibility charts and component matching guides.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Catalogs;
