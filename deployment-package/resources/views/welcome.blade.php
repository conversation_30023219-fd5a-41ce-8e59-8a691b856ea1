<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RELIFE Medical Technologies API</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 2rem;
        }
        .logo {
            font-size: 2.5rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .subtitle {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }
        .links {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 2rem;
        }
        .link {
            display: inline-block;
            padding: 12px 24px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        .link:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        .api-link {
            background: #28a745;
        }
        .api-link:hover {
            background: #218838;
        }
        .features {
            margin-top: 2rem;
            text-align: left;
        }
        .feature {
            margin: 0.5rem 0;
            color: #555;
        }
        .feature::before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">RELIFE</div>
        <div class="subtitle">Medical Technologies API</div>
        
        <p>Welcome to the RELIFE Medical Technologies backend system. This Laravel application provides a comprehensive API for managing prosthetic products and medical devices.</p>
        
        <div class="features">
            <div class="feature">Filament PHP v3 Admin Panel</div>
            <div class="feature">RESTful API with Laravel 11</div>
            <div class="feature">Spatie Media Library Integration</div>
            <div class="feature">Advanced Product Management</div>
            <div class="feature">Role-based Access Control</div>
        </div>
        
        <div class="links">
            <a href="/admin" class="link">Admin Panel</a>
            <a href="/api/v1/products" class="link api-link">API Products</a>
            <a href="/api/v1/products/featured" class="link api-link">Featured Products</a>
        </div>
        
        <p style="margin-top: 2rem; color: #888; font-size: 0.9rem;">
            Admin Login: <EMAIL> | Password: password123
        </p>
    </div>
</body>
</html>
