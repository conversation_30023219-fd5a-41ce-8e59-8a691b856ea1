"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_iterator = void 0;
const base_config_1 = require("./base-config");
const es2015_iterable_1 = require("./es2015.iterable");
exports.esnext_iterator = {
    ...es2015_iterable_1.es2015_iterable,
    Iterator: base_config_1.TYPE_VALUE,
    IteratorObjectConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.iterator.js.map