"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.esnext_asynciterable = void 0;
const base_config_1 = require("./base-config");
const es2015_iterable_1 = require("./es2015.iterable");
const es2015_symbol_1 = require("./es2015.symbol");
exports.esnext_asynciterable = {
    ...es2015_symbol_1.es2015_symbol,
    ...es2015_iterable_1.es2015_iterable,
    AsyncIterable: base_config_1.TYPE,
    AsyncIterableIterator: base_config_1.TYPE,
    AsyncIterator: base_config_1.TYPE,
    AsyncIteratorObject: base_config_1.TYPE,
    SymbolConstructor: base_config_1.TYPE,
};
//# sourceMappingURL=esnext.asynciterable.js.map