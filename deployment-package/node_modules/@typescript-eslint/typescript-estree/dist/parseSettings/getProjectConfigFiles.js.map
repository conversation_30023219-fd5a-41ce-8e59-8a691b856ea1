{"version": 3, "file": "getProjectConfigFiles.js", "sourceRoot": "", "sources": ["../../src/parseSettings/getProjectConfigFiles.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBA,sDA6CC;AA/DD,kDAA0B;AAC1B,4CAA8B;AAC9B,gDAAkC;AAKlC,MAAM,GAAG,GAAG,IAAA,eAAK,EAAC,2DAA2D,CAAC,CAAC;AAE/E;;;;;;;;GAQG;AACH,SAAgB,qBAAqB,CACnC,aAGC,EACD,OAAmC;IAEnC,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;QACrB,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,KAAK,EAAE,CAAC;YACzC,OAAO,IAAI,CAAC;QACd,CAAC;QACD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YAC3B,OAAO,OAAO,CAAC;QACjB,CAAC;QACD,OAAO,CAAC,OAAO,CAAC,CAAC;IACnB,CAAC;IAED,GAAG,CAAC,gDAAgD,EAAE,aAAa,CAAC,QAAQ,CAAC,CAAC;IAC9E,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;IACrD,MAAM,kBAAkB,GAAG,CAAC,SAAS,CAAC,CAAC;IAEvC,GAAG,CAAC;QACF,GAAG,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC;QAClD,MAAM,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAC3D,MAAM,MAAM,GACV,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC;YAC/C,CAAC,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC,CAAC;QAEhD,IAAI,MAAM,EAAE,CAAC;YACX,KAAK,MAAM,SAAS,IAAI,kBAAkB,EAAE,CAAC;gBAC3C,aAAa,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;YAC1D,CAAC;YACD,OAAO,CAAC,MAAM,CAAC,CAAC;QAClB,CAAC;QAED,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QACpC,kBAAkB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IACrC,CAAC,QACC,SAAS,CAAC,MAAM,GAAG,CAAC;QACpB,SAAS,CAAC,MAAM,IAAI,aAAa,CAAC,eAAe,CAAC,MAAM,EACxD;IAEF,MAAM,IAAI,KAAK,CACb,gFAAgF,aAAa,CAAC,QAAQ,aAAa,aAAa,CAAC,eAAe,IAAI,CACrJ,CAAC;AACJ,CAAC"}