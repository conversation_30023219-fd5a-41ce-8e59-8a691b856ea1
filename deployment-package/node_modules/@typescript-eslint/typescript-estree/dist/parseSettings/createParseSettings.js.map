{"version": 3, "file": "createParseSettings.js", "sourceRoot": "", "sources": ["../../src/parseSettings/createParseSettings.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,kDA8JC;AAED,0DAEC;AAED,kEAEC;AA7MD,kDAA0B;AAC1B,0DAA6B;AAC7B,+CAAiC;AAMjC,iFAA8E;AAC9E,qDAA8D;AAC9D,kDAA+C;AAC/C,mDAGyB;AACzB,mEAAgE;AAChE,qDAAkD;AAClD,6DAA0D;AAC1D,6DAA0D;AAE1D,MAAM,GAAG,GAAG,IAAA,eAAK,EACf,8EAA8E,CAC/E,CAAC;AAEF,IAAI,oBAA0D,CAAC;AAC/D,IAAI,wBAAwB,GAAkC,IAAI,CAAC;AAEnE,gGAAgG;AAChG,+GAA+G;AAC/G,uDAAuD;AACvD,gEAAgE;AAChE,MAAM,gBAAgB,GAAG;IACvB,QAAQ,EAAE,EAAE,CAAC,gBAAgB,EAAE,QAAQ;IACvC,kBAAkB,EAAE,EAAE,CAAC,gBAAgB,EAAE,kBAAkB;IAC3D,gBAAgB,EAAE,EAAE,CAAC,gBAAgB,EAAE,gBAAgB;IACvD,SAAS,EAAE,EAAE,CAAC,gBAAgB,EAAE,SAAS;CACjC,CAAC;AACX,+DAA+D;AAE/D,SAAgB,mBAAmB,CACjC,IAA4B,EAC5B,kBAA4C,EAAE;IAE9C,MAAM,YAAY,GAAG,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,MAAM,SAAS,GAAG,IAAA,+BAAc,EAAC,eAAe,CAAC,CAAC;IAClD,MAAM,eAAe,GACnB,OAAO,eAAe,CAAC,eAAe,KAAK,QAAQ;QACjD,CAAC,CAAC,eAAe,CAAC,eAAe;QACjC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC;IACpB,MAAM,cAAc,GAAG,OAAO,eAAe,CAAC,QAAQ,KAAK,UAAU,CAAC;IACtE,MAAM,QAAQ,GAAG,IAAA,2BAAkB,EACjC,OAAO,eAAe,CAAC,QAAQ,KAAK,QAAQ;QAC1C,eAAe,CAAC,QAAQ,KAAK,SAAS;QACtC,CAAC,CAAC,eAAe,CAAC,QAAQ;QAC1B,CAAC,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,EACpC,eAAe,CAChB,CAAC;IACF,MAAM,SAAS,GAAG,mBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAkB,CAAC;IACvE,MAAM,gBAAgB,GAAG,CAAC,GAAwB,EAAE;QAClD,QAAQ,eAAe,CAAC,gBAAgB,EAAE,CAAC;YACzC,KAAK,KAAK;gBACR,OAAO,gBAAgB,CAAC,QAAQ,CAAC;YAEnC,KAAK,MAAM;gBACT,OAAO,gBAAgB,CAAC,SAAS,CAAC;YAEpC,KAAK,WAAW;gBACd,OAAO,gBAAgB,CAAC,gBAAgB,CAAC;YAE3C;gBACE,OAAO,gBAAgB,CAAC,QAAQ,CAAC;QACrC,CAAC;IACH,CAAC,CAAC,EAAE,CAAC;IAEL,MAAM,aAAa,GAAyB;QAC1C,GAAG,EAAE,eAAe,CAAC,GAAG,KAAK,IAAI;QACjC,KAAK,EAAE,eAAe,CAAC,KAAK,KAAK,IAAI;QACrC,eAAe,EAAE,eAAe,CAAC,eAAe,KAAK,IAAI;QACzD,IAAI;QACJ,YAAY;QACZ,OAAO,EAAE,eAAe,CAAC,OAAO,KAAK,IAAI;QACzC,QAAQ,EAAE,EAAE;QACZ,UAAU,EACR,eAAe,CAAC,UAAU,KAAK,IAAI;YACjC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,mBAAmB,CAAC,CAAC;YAChC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,UAAU,CAAC;gBACzC,CAAC,CAAC,IAAI,GAAG,CAAC,eAAe,CAAC,UAAU,CAAC;gBACrC,CAAC,CAAC,IAAI,GAAG,EAAE;QACjB,2CAA2C,EAAE,KAAK;QAClD,qBAAqB,EAAE,eAAe,CAAC,qBAAqB,KAAK,IAAI;QACrE,mBAAmB,EACjB,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,mBAAmB,CAAC;YAClD,eAAe,CAAC,mBAAmB,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK,QAAQ,CAAC;YACvE,CAAC,CAAC,eAAe,CAAC,mBAAmB;YACrC,CAAC,CAAC,EAAE;QACR,QAAQ;QACR,gBAAgB;QAChB,GAAG,EAAE,eAAe,CAAC,GAAG,KAAK,IAAI;QACjC,GAAG,EACD,OAAO,eAAe,CAAC,QAAQ,KAAK,UAAU;YAC5C,CAAC,CAAC,eAAe,CAAC,QAAQ;YAC1B,CAAC,CAAC,eAAe,CAAC,QAAQ,KAAK,KAAK;gBAClC,CAAC,CAAC,GAAS,EAAE,GAAE,CAAC,CAAC,2DAA2D;gBAC5E,CAAC,CAAC,OAAO,CAAC,GAAG,EAAE,iCAAiC;QACtD,gBAAgB,EAAE,eAAe,CAAC,gBAAgB,KAAK,KAAK;QAC5D,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC;YAC/C,CAAC,CAAC,eAAe,CAAC,QAAQ;YAC1B,CAAC,CAAC,IAAI;QACR,QAAQ,EAAE,IAAI,GAAG,EAAE;QACnB,cAAc,EACZ,eAAe,CAAC,cAAc;YAC9B,CAAC,eAAe,CAAC,OAAO;gBACtB,eAAe,CAAC,cAAc,KAAK,KAAK;gBACxC,OAAO,CAAC,GAAG,CAAC,iCAAiC,KAAK,MAAM,CAAC;YACzD,CAAC,CAAC,CAAC,wBAAwB,KAAK,IAAA,2CAAoB,EAChD,eAAe,CAAC,cAAc,EAC9B,gBAAgB,EAChB,eAAe,CAChB,CAAC;YACJ,CAAC,CAAC,SAAS;QACf,0BAA0B,EACxB,eAAe,CAAC,UAAU,KAAK,QAAQ;YACvC,CAAC,eAAe,CAAC,UAAU,KAAK,SAAS;gBACvC,SAAS,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;YACjC,CAAC,eAAe,CAAC,UAAU,KAAK,SAAS;gBACvC,SAAS,KAAK,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;YAC/B,CAAC,CAAC,CAAC,IAAI,EAAQ,EAAE;gBACb,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC;YACtC,CAAC;YACH,CAAC,CAAC,SAAS;QACf,SAAS;QACT,kCAAkC,EAChC,eAAe,CAAC,kCAAkC;YAClD,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,MAAM;QACjC,MAAM,EAAE,eAAe,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI;QACnD,kBAAkB,EAAE,CAAC,oBAAoB,KAAK,IAAI,6BAAa,CAC7D,SAAS;YACP,CAAC,CAAC,UAAU;YACZ,CAAC,CAAC,eAAe,CAAC,aAAa,EAAE,IAAI;gBACnC,uDAAuC,CAC5C,CAAC;QACF,eAAe;KAChB,CAAC;IAEF,8EAA8E;IAC9E,IAAI,aAAa,CAAC,UAAU,CAAC,IAAI,GAAG,CAAC,EAAE,CAAC;QACtC,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,IAAI,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,mBAAmB,CAAC,EAAE,CAAC;YACtD,UAAU,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QACzC,CAAC;QACD,IACE,aAAa,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;YACtC,6EAA6E;YAC7E,eAAK,CAAC,OAAO,CAAC,4BAA4B,CAAC,EAC3C,CAAC;YACD,mGAAmG;YACnG,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAChD,CAAC;QACD,eAAK,CAAC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IACrC,CAAC;IAED,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;QAC5C,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;YACrC,MAAM,IAAI,KAAK,CACb,qPAAqP,CACtP,CAAC;QACJ,CAAC;QACD,GAAG,CACD,gFAAgF,CACjF,CAAC;IACJ,CAAC;IAED,sEAAsE;IACtE,IAAI,CAAC,aAAa,CAAC,QAAQ,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAC;QAC7D,aAAa,CAAC,QAAQ,GAAG,IAAA,uCAAkB,EAAC;YAC1C,aAAa,EAAE,eAAe,CAAC,aAAa;YAC5C,OAAO,EAAE,IAAA,6CAAqB,EAAC,aAAa,EAAE,eAAe,CAAC,OAAO,CAAC;YACtE,uBAAuB,EAAE,eAAe,CAAC,uBAAuB;YAChE,SAAS,EAAE,aAAa,CAAC,SAAS;YAClC,eAAe;SAChB,CAAC,CAAC;IACL,CAAC;IAED,yFAAyF;IACzF,yEAAyE;IACzE,IACE,eAAe,CAAC,gBAAgB,IAAI,IAAI;QACxC,aAAa,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC;QACjC,aAAa,CAAC,QAAQ,IAAI,IAAI;QAC9B,aAAa,CAAC,cAAc,IAAI,IAAI,EACpC,CAAC;QACD,aAAa,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,SAAS,CAAC;IAC9D,CAAC;IAED,IAAA,uCAAkB,EAAC,aAAa,EAAE,cAAc,CAAC,CAAC;IAElD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED,SAAgB,uBAAuB;IACrC,oBAAoB,EAAE,KAAK,EAAE,CAAC;AAChC,CAAC;AAED,SAAgB,2BAA2B;IACzC,wBAAwB,GAAG,IAAI,CAAC;AAClC,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,IAAa;IACtC,OAAO,IAAA,2BAAY,EAAC,IAAI,CAAC;QACvB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;QACxB,CAAC,CAAC,OAAO,IAAI,KAAK,QAAQ;YACxB,CAAC,CAAC,IAAI;YACN,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AACrB,CAAC;AAED;;;;;GAKG;AACH,SAAS,WAAW,CAAC,GAAa;IAChC,OAAO,GAAG,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,WAAW,CAAC;AAC1C,CAAC"}