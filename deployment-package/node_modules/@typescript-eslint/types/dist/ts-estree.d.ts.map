{"version": 3, "file": "ts-estree.d.ts", "sourceRoot": "", "sources": ["../src/ts-estree.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,QAAQ,MAAM,sBAAsB,CAAC;AAGtD,OAAO,QAAQ,sBAAsB,CAAC;IACpC,UAAU,QAAQ;QAChB,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC;KACvB;IAED,UAAU,OAAO;QACf;;WAEG;QACH,MAAM,CAAC,EAAE,KAAK,CAAC;KAChB;IAED,UAAU,4BAA4B;QACpC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,+BAA+B;QACvC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,sCAAsC;QAC9C,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,yCAAyC;QACjD,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IAED,UAAU,oCAAoC;QAC5C,MAAM,EAAE,QAAQ,CAAC,mBAAmB,CAAC;KACtC;IACD,UAAU,2BAA2B;QACnC,MAAM,EAAE,QAAQ,CAAC,mBAAmB,CAAC;KACtC;IACD,UAAU,wBAAwB;QAChC,MAAM,EAAE,QAAQ,CAAC,mBAAmB,CAAC;KACtC;IACD,UAAU,sBAAsB;QAC9B,MAAM,EAAE,QAAQ,CAAC,mBAAmB,CAAC;KACtC;IACD,UAAU,8BAA8B;QACtC,MAAM,EAAE,QAAQ,CAAC,mBAAmB,CAAC;KACtC;IAED,UAAU,WAAW;QACnB,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC;KAC/B;IAED,UAAU,SAAS;QACjB,MAAM,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,CAAC;KAC9D;IAED,UAAU,eAAe;QACvB,MAAM,EAAE,QAAQ,CAAC,iBAAiB,GAAG,QAAQ,CAAC,gBAAgB,CAAC;KAChE;IAED,UAAU,sBAAsB;QAC9B,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC;KACpC;IAED,UAAU,wBAAwB;QAChC,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC;KACpC;IAED,UAAU,eAAe;QACvB,MAAM,EACF,QAAQ,CAAC,oBAAoB,GAC7B,QAAQ,CAAC,sBAAsB,GAC/B,QAAQ,CAAC,iBAAiB,CAAC;KAChC;IAED,UAAU,YAAY;QACpB,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC;KACpC;IAED,UAAU,iBAAiB;QACzB,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;KAC7B;IAED,UAAU,kBAAkB;QAC1B,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC;KAC9B;IAED,UAAU,iBAAiB;QACzB,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;KAC7B;IAED,UAAU,kBAAkB;QAC1B,MAAM,EAAE,QAAQ,CAAC,WAAW,CAAC;KAC9B;IAED,UAAU,kBAAkB;QAC1B,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC;KACpC;IAED,UAAU,4BAA4B;QACpC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,+BAA+B;QACvC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,sCAAsC;QAC9C,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,yCAAyC;QACjD,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IAED,UAAU,oBAAoB;QAC5B,MAAM,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC5D;IACD,UAAU,uBAAuB;QAC/B,MAAM,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC5D;IAED,UAAU,8BAA8B;QACtC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,iCAAiC;QACzC,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,wCAAwC;QAChD,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IACD,UAAU,2CAA2C;QACnD,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IAED,UAAU,aAAa;QACrB,MAAM,EACF,QAAQ,CAAC,eAAe,GACxB,QAAQ,CAAC,cAAc,GACvB,QAAQ,CAAC,aAAa,GACtB,QAAQ,CAAC,gBAAgB,CAAC;KAC/B;IAED,UAAU,WAAW;QACnB,MAAM,EAAE,QAAQ,CAAC,SAAS,CAAC;KAC5B;IAED,UAAU,UAAU;QAClB,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC;KAClC;IAED,UAAU,eAAe;QACvB,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,qBAAqB,CAAC;KACnE;IAED,UAAU,0BAA0B;QAClC,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC3D;IAED,UAAU,+BAA+B;QACvC,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC3D;IAED,UAAU,iBAAiB;QACzB,MAAM,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,CAAC;KAC9D;IAED,UAAU,UAAU;QAClB,MAAM,EAAE,QAAQ,CAAC,iBAAiB,CAAC;KACpC;IAED,UAAU,wBAAwB;QAChC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;KAC7B;IACD,UAAU,2BAA2B;QACnC,MAAM,EAAE,QAAQ,CAAC,UAAU,CAAC;KAC7B;IAED,UAAU,gBAAgB;QACxB,MAAM,EACF,QAAQ,CAAC,SAAS,GAClB,QAAQ,CAAC,eAAe,GACxB,QAAQ,CAAC,aAAa,CAAC;KAC5B;IAED,UAAU,eAAe;QACvB,MAAM,EAAE,QAAQ,CAAC,sBAAsB,CAAC;KACzC;IAED,UAAU,mBAAmB;QAC3B,MAAM,EAAE,QAAQ,CAAC,eAAe,CAAC;KAClC;IAED,UAAU,6BAA6B;QACrC,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC3D;IACD,UAAU,gCAAgC;QACxC,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC3D;IAED,UAAU,aAAa;QACrB,MAAM,EAAE,QAAQ,CAAC,mBAAmB,CAAC;KACtC;IAED,UAAU,mBAAmB;QAC3B,MAAM,EAAE,QAAQ,CAAC,YAAY,CAAC;KAC/B;IAED,UAAU,+BAA+B;QACvC,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC3D;IACD,UAAU,kCAAkC;QAC1C,MAAM,EAAE,QAAQ,CAAC,eAAe,GAAG,QAAQ,CAAC,aAAa,CAAC;KAC3D;IAED,UAAU,eAAe;QACvB,MAAM,EACF,QAAQ,CAAC,WAAW,GACpB,QAAQ,CAAC,YAAY,GACrB,QAAQ,CAAC,0BAA0B,CAAC;KACzC;IAED,UAAU,kCAAkC;QAC1C,MAAM,EAAE,QAAQ,CAAC,sBAAsB,CAAC;KACzC;IACD,UAAU,uCAAuC;QAC/C,MAAM,EAAE,QAAQ,CAAC,sBAAsB,CAAC;KACzC;CACF;AAED,OAAO,KAAK,QAAQ,MAAM,sBAAsB,CAAC"}