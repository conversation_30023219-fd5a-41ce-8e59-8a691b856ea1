{"version": 3, "file": "deepMerge.js", "sourceRoot": "", "sources": ["../../src/eslint-utils/deepMerge.ts"], "names": [], "mappings": ";;AAiBA,8BA+BC;AAEQ,4CAAgB;AAhDzB;;;GAGG;AACH,SAAS,gBAAgB,CAAC,GAAY;IACpC,OAAO,OAAO,GAAG,KAAK,QAAQ,IAAI,GAAG,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AACvE,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,SAAS,CACvB,QAAoB,EAAE,EACtB,SAAqB,EAAE;IAEvB,iDAAiD;IACjD,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;IAEtE,OAAO,MAAM,CAAC,WAAW,CACvB,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;QAClB,MAAM,WAAW,GAAG,GAAG,IAAI,KAAK,CAAC;QACjC,MAAM,YAAY,GAAG,GAAG,IAAI,MAAM,CAAC;QACnC,MAAM,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9B,MAAM,WAAW,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;QAEhC,IAAI,KAAK,CAAC;QACV,IAAI,WAAW,IAAI,YAAY,EAAE,CAAC;YAChC,IAAI,gBAAgB,CAAC,UAAU,CAAC,IAAI,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;gBAClE,cAAc;gBACd,KAAK,GAAG,SAAS,CAAC,UAAU,EAAE,WAAW,CAAC,CAAC;YAC7C,CAAC;iBAAM,CAAC;gBACN,aAAa;gBACb,KAAK,GAAG,WAAW,CAAC;YACtB,CAAC;QACH,CAAC;aAAM,IAAI,WAAW,EAAE,CAAC;YACvB,KAAK,GAAG,UAAU,CAAC;QACrB,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,WAAW,CAAC;QACtB,CAAC;QACD,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;IACtB,CAAC,CAAC,CACH,CAAC;AACJ,CAAC"}