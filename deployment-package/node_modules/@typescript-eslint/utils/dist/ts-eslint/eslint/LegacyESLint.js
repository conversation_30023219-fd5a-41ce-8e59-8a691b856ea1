"use strict";
/* eslint-disable @typescript-eslint/no-namespace */
Object.defineProperty(exports, "__esModule", { value: true });
exports.LegacyESLint = void 0;
const use_at_your_own_risk_1 = require("eslint/use-at-your-own-risk");
/**
 * The ESLint class is the primary class to use in Node.js applications.
 * This class depends on the Node.js fs module and the file system, so you cannot use it in browsers.
 *
 * If you want to lint code on browsers, use the Linter class instead.
 */
class LegacyESLint extends use_at_your_own_risk_1.LegacyESLint {
}
exports.LegacyESLint = LegacyESLint;
//# sourceMappingURL=LegacyESLint.js.map