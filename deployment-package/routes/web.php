<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\SpaController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// Serve React SPA for all non-API routes
// This must be the last route to catch all frontend routes
Route::get('/{any}', function () {
    return view('app');
})->where('any', '^(?!api|admin|storage|css|js|images|assets|build).*$');
